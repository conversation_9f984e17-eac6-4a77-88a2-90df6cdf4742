#!/usr/bin/env python3
"""
Test Beautiful UI
Triggers the pre-reporting phase to show the beautiful UI
"""

import sqlite3
import json
import sys
import os
from datetime import datetime

def trigger_pre_reporting_ui():
    """Trigger pre-reporting phase to show beautiful UI"""
    
    try:
        print("🎨 TRIGGERING BEAUTIFUL PRE-REPORTING UI:")
        
        # Connect to database
        conn = sqlite3.connect('payroll_audit.db')
        cursor = conn.cursor()
        
        # Get current session
        cursor.execute("SELECT session_id FROM sessions WHERE is_current = 1 LIMIT 1")
        session_result = cursor.fetchone()
        
        if not session_result:
            print("❌ No current session found")
            return {'success': False, 'error': 'No current session'}
        
        session_id = session_result[0]
        print(f"   Using session: {session_id}")
        
        # Check if we have pre-reporting data
        cursor.execute("SELECT COUNT(*) FROM pre_reporting_results WHERE session_id = ?", (session_id,))
        count = cursor.fetchone()[0]
        print(f"   Pre-reporting data available: {count} changes")
        
        if count == 0:
            print("❌ No pre-reporting data found")
            return {'success': False, 'error': 'No pre-reporting data'}
        
        # Get sample of the data to verify it's working
        cursor.execute("""
            SELECT employee_id, employee_name, section, item_label, change_type, 
                   current_value, previous_value, priority_level, bulk_category
            FROM pre_reporting_results 
            WHERE session_id = ? 
            LIMIT 5
        """, (session_id,))
        
        sample_data = cursor.fetchall()
        
        print("\n📊 SAMPLE PRE-REPORTING DATA:")
        for row in sample_data:
            print(f"   {row[0]} | {row[1]} | {row[2]} | {row[3]} | {row[4]}")
        
        conn.close()
        
        print("\n✅ DATABASE READY FOR BEAUTIFUL UI!")
        print("   The pre-reporting data is loaded and ready")
        print("   Navigate to Payroll Audit → Start process to see the beautiful UI")
        
        return {
            'success': True,
            'session_id': session_id,
            'changes_count': count,
            'sample_data': [
                {
                    'employee_id': row[0],
                    'employee_name': row[1],
                    'section': row[2],
                    'item_label': row[3],
                    'change_type': row[4],
                    'current_value': row[5],
                    'previous_value': row[6],
                    'priority_level': row[7],
                    'bulk_category': row[8]
                } for row in sample_data
            ]
        }
        
    except Exception as e:
        print(f"❌ Error checking pre-reporting data: {e}")
        return {'success': False, 'error': str(e)}

def main():
    """Main entry point"""
    result = trigger_pre_reporting_ui()
    print(json.dumps(result, indent=2))

if __name__ == '__main__':
    main()
