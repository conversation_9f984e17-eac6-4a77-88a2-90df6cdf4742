#!/usr/bin/env python3
"""
Debug IN-HOUSE Loan Detection Logic
Investigate why only 3 IN-HOUSE loans are detected when there should be 40+
"""

import os
import sys
import sqlite3
from pathlib import Path

def get_database_path():
    """Get the database path"""
    db_path = Path(__file__).parent / "data" / "templar_payroll_auditor.db"
    if db_path.exists():
        return str(db_path)
    return None

def debug_in_house_loan_detection():
    """Debug IN-HOUSE loan detection logic"""
    
    print("🔍 DEBUGGING IN-HOUSE LOAN DETECTION LOGIC")
    print("=" * 50)
    
    try:
        db_path = get_database_path()
        if not db_path:
            print("❌ Database file not found")
            return False
        
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 1. Check what's in tracker_results
        print("1. 📊 ANALYZING TRACKER_RESULTS DATA:")
        
        cursor.execute("""
            SELECT tracker_type, COUNT(*) as count
            FROM tracker_results
            GROUP BY tracker_type
        """)
        tracker_summary = cursor.fetchall()
        
        print("   📋 Tracker types summary:")
        for tracker_type, count in tracker_summary:
            print(f"      {tracker_type}: {count} items")
        
        # 2. Check all loan items in tracker_results
        print(f"\n2. 📊 ALL LOAN ITEMS IN TRACKER_RESULTS:")
        
        cursor.execute("""
            SELECT DISTINCT item_label, tracker_type, COUNT(*) as count
            FROM tracker_results
            WHERE tracker_type IN ('IN_HOUSE_LOAN', 'EXTERNAL_LOAN')
            GROUP BY item_label, tracker_type
            ORDER BY tracker_type, item_label
        """)
        
        loan_items = cursor.fetchall()
        
        in_house_items = []
        external_items = []
        
        for item_label, tracker_type, count in loan_items:
            print(f"   {tracker_type}: {item_label} ({count} employees)")
            if tracker_type == 'IN_HOUSE_LOAN':
                in_house_items.append(item_label)
            else:
                external_items.append(item_label)
        
        print(f"\n   📊 Summary:")
        print(f"      IN_HOUSE_LOAN items: {len(in_house_items)}")
        print(f"      EXTERNAL_LOAN items: {len(external_items)}")
        
        # 3. Check the dictionary loan types
        print(f"\n3. 📚 CHECKING DICTIONARY LOAN TYPES:")
        
        try:
            sys.path.append(os.path.dirname(__file__))
            from core.dictionary_manager import PayrollDictionaryManager
            
            dict_manager = PayrollDictionaryManager(debug=False)
            loan_types = dict_manager.get_loan_types()
            
            print(f"   📋 Dictionary loan types: {len(loan_types)}")
            
            in_house_dict = []
            external_dict = []
            
            for loan_type_name, loan_data in loan_types.items():
                classification = loan_data.get('classification', 'UNKNOWN')
                print(f"      {loan_type_name}: {classification}")
                
                if 'IN-HOUSE' in classification.upper():
                    in_house_dict.append(loan_type_name)
                elif 'EXTERNAL' in classification.upper():
                    external_dict.append(loan_type_name)
            
            print(f"\n   📊 Dictionary summary:")
            print(f"      IN-HOUSE types: {len(in_house_dict)}")
            print(f"      EXTERNAL types: {len(external_dict)}")
            
        except Exception as e:
            print(f"   ❌ Could not load dictionary: {e}")
            loan_types = {}
            in_house_dict = []
        
        # 4. Test classification logic
        print(f"\n4. 🧪 TESTING CLASSIFICATION LOGIC:")
        
        # Test each loan item from tracker_results
        print("   📋 Testing each loan item classification:")
        
        all_loan_items = set()
        cursor.execute("SELECT DISTINCT item_label FROM tracker_results WHERE tracker_type LIKE '%LOAN'")
        for row in cursor.fetchall():
            all_loan_items.add(row[0])
        
        print(f"   Found {len(all_loan_items)} unique loan items to test")
        
        # Test with different classification methods
        classification_results = {}
        
        for item_label in sorted(all_loan_items):
            # Clean the loan name (remove " - BALANCE B/F" etc.)
            loan_name = item_label
            if ' - ' in loan_name:
                loan_name = loan_name.split(' - ')[0].strip()
            
            classification_results[item_label] = {
                'cleaned_name': loan_name,
                'current_tracker_type': None,
                'dictionary_classification': None,
                'keyword_classification': None
            }
            
            # Get current tracker type
            cursor.execute("""
                SELECT tracker_type FROM tracker_results 
                WHERE item_label = ? 
                LIMIT 1
            """, (item_label,))
            result = cursor.fetchone()
            if result:
                classification_results[item_label]['current_tracker_type'] = result[0]
            
            # Test dictionary classification
            if loan_types:
                try:
                    dict_classification = dict_manager.classify_loan_type(loan_name)
                    classification_results[item_label]['dictionary_classification'] = dict_classification
                except:
                    pass
            
            # Test keyword classification
            loan_upper = loan_name.upper()
            in_house_keywords = [
                'STAFF', 'EMPLOYEE', 'SALARY ADVANCE', 'RENT ADVANCE',
                'COMPANY', 'INTERNAL', 'WELFARE', 'CREDIT UNION',
                'BUILDING', 'PENSIONS', 'MISSIONS'
            ]
            
            is_in_house_keyword = any(keyword in loan_upper for keyword in in_house_keywords)
            classification_results[item_label]['keyword_classification'] = 'IN_HOUSE_LOAN' if is_in_house_keyword else 'EXTERNAL_LOAN'
        
        # 5. Show classification comparison
        print(f"\n5. 📊 CLASSIFICATION COMPARISON:")
        
        mismatches = 0
        potential_in_house = 0
        
        for item_label, results in classification_results.items():
            current = results['current_tracker_type']
            dictionary = results['dictionary_classification']
            keyword = results['keyword_classification']
            
            # Check if there's a mismatch
            mismatch = False
            if keyword == 'IN_HOUSE_LOAN' and current == 'EXTERNAL_LOAN':
                mismatch = True
                mismatches += 1
                potential_in_house += 1
            
            if mismatch or current == 'IN_HOUSE_LOAN':
                print(f"   {'⚠️' if mismatch else '✅'} {item_label}")
                print(f"      Current: {current}")
                print(f"      Dictionary: {dictionary}")
                print(f"      Keyword: {keyword}")
                print(f"      Cleaned: {results['cleaned_name']}")
        
        # 6. Count employees for potential IN-HOUSE loans
        print(f"\n6. 📊 EMPLOYEE COUNT ANALYSIS:")
        
        # Count employees with potential IN-HOUSE loans
        potential_in_house_employees = 0
        
        for item_label, results in classification_results.items():
            if results['keyword_classification'] == 'IN_HOUSE_LOAN':
                cursor.execute("""
                    SELECT COUNT(DISTINCT employee_id) 
                    FROM tracker_results 
                    WHERE item_label = ?
                """, (item_label,))
                employee_count = cursor.fetchone()[0]
                potential_in_house_employees += employee_count
                
                if results['current_tracker_type'] == 'EXTERNAL_LOAN':
                    print(f"   🔄 MISCLASSIFIED: {item_label} ({employee_count} employees)")
        
        print(f"\n   📊 Summary:")
        print(f"      Current IN-HOUSE employees: 3")
        print(f"      Potential IN-HOUSE employees: {potential_in_house_employees}")
        print(f"      Misclassified items: {mismatches}")
        
        conn.close()
        
        # 7. Summary and recommendations
        print(f"\n7. 📋 DIAGNOSIS & RECOMMENDATIONS:")
        print("=" * 35)
        
        if mismatches > 0:
            print(f"❌ ISSUE FOUND: {mismatches} loan types are misclassified")
            print(f"✅ SOLUTION: Update classification logic to use keyword-based detection")
            print(f"📊 IMPACT: Could increase IN-HOUSE loans from 3 to {potential_in_house_employees}")
            
            print(f"\n🔧 RECOMMENDED FIXES:")
            print(f"   1. Update tracker feeding to use keyword-based classification")
            print(f"   2. Re-run tracker population with corrected logic")
            print(f"   3. Verify dictionary contains all IN-HOUSE loan types")
            
            return True
        else:
            print(f"✅ No obvious misclassification issues found")
            print(f"⚠️ May need to investigate other causes")
            return False
        
    except Exception as e:
        print(f"❌ Debug failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = debug_in_house_loan_detection()
    
    if success:
        print("\n🎯 IN-HOUSE LOAN DETECTION DEBUG COMPLETED!")
        print("   Issues identified - see recommendations above")
    else:
        print("\n⚠️ IN-HOUSE LOAN DETECTION DEBUG INCONCLUSIVE!")
        print("   May need further investigation")
