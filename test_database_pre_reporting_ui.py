#!/usr/bin/env python3
"""
Test Database-Only Pre-Reporting UI Integration
Tests that the pre-reporting UI can load data correctly from the database
"""

import os
import sys
import sqlite3
from pathlib import Path

def get_database_path():
    """Get the database path"""
    db_path = Path(__file__).parent / "data" / "templar_payroll_auditor.db"
    if db_path.exists():
        return str(db_path)
    return None

def test_database_pre_reporting_ui():
    """Test that pre-reporting UI can load data from database"""
    
    print("🧪 TESTING DATABASE-ONLY PRE-REPORTING UI INTEGRATION")
    print("=" * 60)
    
    try:
        db_path = get_database_path()
        if not db_path:
            print("❌ Database file not found")
            return
        
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 1. Check for current session
        print("\n1. 📊 CHECKING CURRENT SESSION:")
        
        cursor.execute("""
            SELECT session_id, created_at,
                   (SELECT COUNT(*) FROM comparison_results WHERE session_id = s.session_id) as comparison_count,
                   (SELECT COUNT(*) FROM pre_reporting_results WHERE session_id = s.session_id) as pre_reporting_count
            FROM audit_sessions s
            ORDER BY created_at DESC
            LIMIT 5
        """)
        
        sessions = cursor.fetchall()
        
        if not sessions:
            print("   ❌ No audit sessions found")
            return
        
        # Find session with data
        best_session = None
        for session in sessions:
            session_id, created_at, comp_count, pre_count = session
            print(f"   Session: {session_id}")
            print(f"     Created: {created_at}")
            print(f"     Comparison results: {comp_count}")
            print(f"     Pre-reporting results: {pre_count}")
            
            if comp_count > 0:
                best_session = session_id
                break
        
        if not best_session:
            print("   ❌ No session with comparison data found")
            return
        
        print(f"   ✅ Using session: {best_session}")
        
        # 2. Test the database query that the UI will use
        print("\n2. 🔍 TESTING DATABASE QUERY:")
        
        # This is the exact query from get_pre_reporting_data
        query = '''SELECT cr.id, cr.employee_id, cr.employee_name, cr.section_name, cr.item_label,
                          cr.previous_value, cr.current_value, cr.change_type, cr.priority,
                          pr.bulk_category, pr.bulk_size, pr.selected_for_report
                   FROM comparison_results cr
                   LEFT JOIN pre_reporting_results pr ON cr.id = pr.change_id
                   WHERE cr.session_id = ?
                   ORDER BY cr.priority DESC, cr.section_name, cr.employee_id'''
        
        cursor.execute(query, (best_session,))
        results = cursor.fetchall()
        
        print(f"   Query returned: {len(results)} rows")
        
        if results:
            # Show sample data structure
            print("   Sample data structure:")
            sample = results[0]
            print(f"     ID: {sample[0]}")
            print(f"     Employee ID: {sample[1]}")
            print(f"     Employee Name: {sample[2]}")
            print(f"     Section: {sample[3]}")
            print(f"     Item: {sample[4]}")
            print(f"     Change Type: {sample[7]}")
            print(f"     Priority: {sample[8]}")
            
            # Check data completeness
            complete_records = 0
            for row in results:
                if all([row[1], row[2], row[3], row[4], row[7], row[8]]):  # Required fields
                    complete_records += 1
            
            print(f"   Complete records: {complete_records}/{len(results)}")
            
            if complete_records == len(results):
                print("   ✅ All records have required fields")
            else:
                print("   ⚠️ Some records missing required fields")
        
        # 3. Test the API method
        print("\n3. 🧪 TESTING API METHOD:")
        
        try:
            sys.path.append(os.path.dirname(__file__))
            from core.phased_process_manager import PhasedProcessManager
            
            manager = PhasedProcessManager()
            result = manager.get_pre_reporting_data(best_session)
            
            print(f"   API Success: {result.get('success')}")
            print(f"   Data Count: {len(result.get('data', []))}")
            print(f"   Total Changes: {result.get('total_changes', 0)}")
            print(f"   Session ID: {result.get('session_id', 'unknown')}")
            
            if result.get('success') and result.get('data'):
                sample_data = result['data'][0]
                print("   Sample API data:")
                for key, value in sample_data.items():
                    print(f"     {key}: {value}")
                
                # Verify required fields for UI
                required_fields = ['id', 'employee_id', 'employee_name', 'section_name', 
                                 'item_label', 'change_type', 'priority']
                
                missing_fields = [field for field in required_fields if field not in sample_data]
                
                if not missing_fields:
                    print("   ✅ All required UI fields present")
                else:
                    print(f"   ❌ Missing UI fields: {missing_fields}")
            
        except Exception as e:
            print(f"   ❌ API test failed: {e}")
        
        # 4. Summary
        print("\n4. 📋 SUMMARY:")
        
        if results and len(results) > 0:
            print("   ✅ Database has pre-reporting data")
            print("   ✅ Query returns proper structure")
            print("   ✅ UI should be able to load data")
            print(f"   📊 Ready to display {len(results)} changes")
        else:
            print("   ❌ No data available for UI")
        
        conn.close()
        
        print("\n🎯 DATABASE-ONLY PRE-REPORTING UI TEST COMPLETE!")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_database_pre_reporting_ui()
