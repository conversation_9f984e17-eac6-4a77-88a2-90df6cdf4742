#!/usr/bin/env python3
"""
Fix Dictionary Loan Types
Update dictionary to include missing IN-HOUSE loan types
"""

import os
import sys

def fix_dictionary_loan_types():
    """Fix missing IN-HOUSE loan types in dictionary"""
    
    print("🔧 FIXING DICTIONARY LOAN TYPES")
    print("=" * 35)
    
    try:
        sys.path.append(os.path.dirname(__file__))
        from core.dictionary_manager import PayrollDictionaryManager
        
        dict_manager = PayrollDictionaryManager(debug=True)
        
        # 1. Check current loan types
        print("1. 📚 CURRENT DICTIONARY LOAN TYPES:")
        
        loan_types = dict_manager.get_loan_types()
        print(f"   📋 Current loan types: {len(loan_types)}")
        
        for loan_type_name, loan_data in loan_types.items():
            classification = loan_data.get('classification', 'UNKNOWN')
            print(f"      {loan_type_name}: {classification}")
        
        # 2. Add missing IN-HOUSE loan types
        print(f"\n2. 🔧 ADDING MISSING IN-HOUSE LOAN TYPES:")
        
        missing_in_house_loans = [
            'PENSIONS SALARY ADVANCE',
            'PENSIONS SALARY ADVA',  # Short version
            'STAFF CREDIT UNION LOAN',
            'STAFF CREDIT UNION LO'  # Short version
        ]
        
        added_count = 0
        
        for loan_type in missing_in_house_loans:
            if not dict_manager.loan_type_exists(loan_type):
                success = dict_manager.create_loan_type(loan_type, "IN-HOUSE LOAN")
                if success:
                    print(f"   ✅ Added: {loan_type} → IN-HOUSE LOAN")
                    added_count += 1
                else:
                    print(f"   ❌ Failed to add: {loan_type}")
            else:
                # Update existing classification if needed
                current_classification = dict_manager.get_loan_type_classification(loan_type)
                if current_classification != "IN-HOUSE LOAN":
                    print(f"   🔄 Updating: {loan_type} from {current_classification} to IN-HOUSE LOAN")
                    # Update the classification
                    loan_types = dict_manager.get_loan_types()
                    if loan_type in loan_types:
                        loan_types[loan_type]['classification'] = "IN-HOUSE LOAN"
                        added_count += 1
                else:
                    print(f"   ✅ Already correct: {loan_type} → {current_classification}")
        
        # 3. Save the updated dictionary
        print(f"\n3. 💾 SAVING UPDATED DICTIONARY:")
        
        try:
            dict_manager.save_dictionary()
            print(f"   ✅ Dictionary saved successfully")
        except Exception as e:
            print(f"   ❌ Failed to save dictionary: {e}")
        
        # 4. Verify the changes
        print(f"\n4. ✅ VERIFYING CHANGES:")
        
        # Reload dictionary
        dict_manager = PayrollDictionaryManager(debug=False)
        updated_loan_types = dict_manager.get_loan_types()
        
        print(f"   📋 Updated loan types: {len(updated_loan_types)}")
        
        in_house_count = 0
        external_count = 0
        
        for loan_type_name, loan_data in updated_loan_types.items():
            classification = loan_data.get('classification', 'UNKNOWN')
            print(f"      {loan_type_name}: {classification}")
            
            if 'IN-HOUSE' in classification.upper():
                in_house_count += 1
            elif 'EXTERNAL' in classification.upper():
                external_count += 1
        
        print(f"\n   📊 Summary:")
        print(f"      IN-HOUSE loan types: {in_house_count}")
        print(f"      EXTERNAL loan types: {external_count}")
        print(f"      Added/Updated: {added_count}")
        
        # 5. Test the problematic loans
        print(f"\n5. 🧪 TESTING PROBLEMATIC LOANS:")
        
        test_loans = [
            'PENSIONS SALARY ADVANCE',
            'STAFF CREDIT UNION LOAN'
        ]
        
        all_correct = True
        
        for loan in test_loans:
            classification = dict_manager.classify_loan_type(loan)
            is_correct = classification == "IN-HOUSE LOAN"
            status = "✅" if is_correct else "❌"
            print(f"   {status} {loan}: {classification}")
            
            if not is_correct:
                all_correct = False
        
        # 6. Summary
        print(f"\n6. 📋 DICTIONARY FIX SUMMARY:")
        print("=" * 30)
        
        if all_correct and added_count > 0:
            print(f"✅ Dictionary updated successfully")
            print(f"✅ Missing loan types added: {added_count}")
            print(f"✅ All problematic loans now classified correctly")
            print(f"✅ Total IN-HOUSE loan types: {in_house_count}")
            return True
        elif all_correct:
            print(f"✅ Dictionary already correct")
            print(f"✅ No changes needed")
            return True
        else:
            print(f"❌ Some issues remain")
            print(f"⚠️ Manual intervention may be required")
            return False
        
    except Exception as e:
        print(f"❌ Fix failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = fix_dictionary_loan_types()
    
    if success:
        print("\n🎉 DICTIONARY LOAN TYPES FIXED!")
        print("   All IN-HOUSE loans should now be correctly classified")
    else:
        print("\n⚠️ DICTIONARY FIX INCOMPLETE!")
        print("   Some issues may remain")
