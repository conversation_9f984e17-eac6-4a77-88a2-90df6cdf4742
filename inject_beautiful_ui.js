const { app, BrowserWindow } = require('electron');
const fs = require('fs');
const path = require('path');

// Wait for app to be ready
app.whenReady().then(() => {
    // Get all windows
    const windows = BrowserWindow.getAllWindows();
    
    if (windows.length > 0) {
        const mainWindow = windows[0];
        
        console.log('🎯 Injecting Beautiful UI trigger...');
        
        // Read the trigger script
        const triggerScript = fs.readFileSync(path.join(__dirname, 'trigger_beautiful_ui.js'), 'utf8');
        
        // Execute the script in the renderer process
        mainWindow.webContents.executeJavaScript(triggerScript)
            .then(result => {
                console.log('✅ Beautiful UI trigger injected successfully');
                console.log('Result:', result);
            })
            .catch(error => {
                console.error('❌ Error injecting script:', error);
            });
    } else {
        console.log('❌ No Electron windows found');
    }
});

// Handle app activation
app.on('activate', () => {
    if (BrowserWindow.getAllWindows().length === 0) {
        console.log('No windows available for injection');
    }
});
