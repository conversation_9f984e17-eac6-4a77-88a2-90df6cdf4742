<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Beautiful Pre-Reporting UI</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
            color: white;
            padding: 20px;
            text-align: center;
        }
        
        .content {
            padding: 30px;
        }
        
        .btn {
            background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
            transition: all 0.3s ease;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        
        .btn.primary {
            background: linear-gradient(135deg, #2196F3 0%, #1976D2 100%);
        }
        
        .status {
            margin: 20px 0;
            padding: 15px;
            border-radius: 8px;
            background: #f5f5f5;
        }
        
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        #pre-reporting-container {
            margin-top: 20px;
            min-height: 400px;
            border: 2px dashed #ddd;
            border-radius: 8px;
            padding: 20px;
            background: #fafafa;
        }
        
        .loading {
            text-align: center;
            padding: 40px;
        }
        
        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #3498db;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎨 Beautiful Pre-Reporting UI Test</h1>
            <p>Test the beautiful pre-reporting interface with real data</p>
        </div>
        
        <div class="content">
            <div class="controls">
                <button class="btn primary" onclick="testPreReportingData()">
                    📊 Load Pre-Reporting Data
                </button>
                <button class="btn" onclick="triggerBeautifulUI()">
                    🎨 Trigger Beautiful UI
                </button>
                <button class="btn" onclick="clearContainer()">
                    🗑️ Clear Container
                </button>
            </div>
            
            <div id="status" class="status" style="display: none;"></div>
            
            <div id="pre-reporting-container">
                <div class="loading">
                    <div class="spinner"></div>
                    <p>Ready to load beautiful pre-reporting UI...</p>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Test functions
        async function testPreReportingData() {
            const statusDiv = document.getElementById('status');
            statusDiv.style.display = 'block';
            statusDiv.className = 'status';
            statusDiv.innerHTML = '⏳ Loading pre-reporting data...';
            
            try {
                // Check if we're in Electron environment
                if (typeof window.api !== 'undefined') {
                    const result = await window.api.getLatestPreReportingData();
                    
                    if (result && result.success) {
                        statusDiv.className = 'status success';
                        statusDiv.innerHTML = `✅ Pre-reporting data loaded successfully!<br>
                                             📊 ${result.data.length} changes found<br>
                                             🆔 Session: ${result.session_id}`;
                        
                        // Store data for UI rendering
                        window.preReportingData = result;
                        
                        console.log('Pre-reporting data:', result);
                    } else {
                        statusDiv.className = 'status error';
                        statusDiv.innerHTML = `❌ Failed to load pre-reporting data: ${result?.error || 'Unknown error'}`;
                    }
                } else {
                    statusDiv.className = 'status error';
                    statusDiv.innerHTML = '❌ Not running in Electron environment. Please run this in the main app.';
                }
            } catch (error) {
                statusDiv.className = 'status error';
                statusDiv.innerHTML = `❌ Error loading data: ${error.message}`;
                console.error('Error:', error);
            }
        }
        
        async function triggerBeautifulUI() {
            const container = document.getElementById('pre-reporting-container');
            const statusDiv = document.getElementById('status');
            
            if (!window.preReportingData) {
                statusDiv.style.display = 'block';
                statusDiv.className = 'status error';
                statusDiv.innerHTML = '❌ Please load pre-reporting data first!';
                return;
            }
            
            try {
                statusDiv.style.display = 'block';
                statusDiv.className = 'status';
                statusDiv.innerHTML = '🎨 Rendering beautiful UI...';
                
                // Load the interactive pre-reporting script
                if (!window.InteractivePreReporting) {
                    await loadScript('ui/interactive_pre_reporting.js');
                }
                
                // Initialize and render the beautiful UI
                if (window.InteractivePreReporting) {
                    window.interactivePreReporting = new window.InteractivePreReporting(container);
                    await window.interactivePreReporting.render(window.preReportingData.data);
                    
                    statusDiv.className = 'status success';
                    statusDiv.innerHTML = '🎉 Beautiful UI rendered successfully!';
                } else {
                    throw new Error('InteractivePreReporting class not loaded');
                }
            } catch (error) {
                statusDiv.className = 'status error';
                statusDiv.innerHTML = `❌ Error rendering UI: ${error.message}`;
                console.error('Error:', error);
            }
        }
        
        function clearContainer() {
            const container = document.getElementById('pre-reporting-container');
            container.innerHTML = `
                <div class="loading">
                    <div class="spinner"></div>
                    <p>Ready to load beautiful pre-reporting UI...</p>
                </div>
            `;
            
            const statusDiv = document.getElementById('status');
            statusDiv.style.display = 'none';
        }
        
        function loadScript(src) {
            return new Promise((resolve, reject) => {
                const script = document.createElement('script');
                script.src = src;
                script.onload = resolve;
                script.onerror = reject;
                document.head.appendChild(script);
            });
        }
        
        // Auto-load data when page loads if in Electron
        window.addEventListener('load', () => {
            if (typeof window.api !== 'undefined') {
                console.log('🎯 Electron environment detected, auto-loading data...');
                setTimeout(testPreReportingData, 1000);
            }
        });
    </script>
</body>
</html>
