// Inject Real Pre-Reporting UI - Copy and paste this into the browser console
// This will trigger the beautiful pre-reporting UI in your running app

(async function triggerRealPreReportingUI() {
    try {
        console.log('🎯 TRIGGERING REAL PRE-REPORTING UI...');
        
        // Step 1: Switch to Payroll Audit tab if not already there
        const payrollTab = document.querySelector('[data-tab="payroll-audit"]');
        if (payrollTab && !payrollTab.classList.contains('active')) {
            console.log('📋 Switching to Payroll Audit tab...');
            payrollTab.click();
            await new Promise(resolve => setTimeout(resolve, 500));
        }
        
        // Step 2: Load pre-reporting data using the real API
        console.log('📊 Loading real pre-reporting data...');
        
        if (!window.api || !window.api.getLatestPreReportingData) {
            throw new Error('API not available. Make sure you are running this in the Electron app.');
        }
        
        const preReportingData = await window.api.getLatestPreReportingData();
        
        if (!preReportingData || !preReportingData.success) {
            throw new Error('Failed to load pre-reporting data: ' + (preReportingData?.error || 'Unknown error'));
        }
        
        console.log('✅ Pre-reporting data loaded:', preReportingData.data.length, 'changes');
        console.log('📊 Session ID:', preReportingData.session_id);
        
        // Step 3: Find or create the pre-reporting container
        let container = document.getElementById('pre-reporting-container') || 
                       document.getElementById('pre-reporting-interactive-ui') ||
                       document.getElementById('processing-pre-reporting-container');
        
        if (!container) {
            // Create container if it doesn't exist
            container = document.createElement('div');
            container.id = 'pre-reporting-container';
            container.style.cssText = `
                margin: 20px; 
                padding: 20px; 
                background: white; 
                border-radius: 12px; 
                box-shadow: 0 4px 20px rgba(0,0,0,0.1);
                border: 2px solid #4CAF50;
                position: relative;
                z-index: 1000;
            `;
            
            // Add a header to identify this as the injected UI
            const header = document.createElement('div');
            header.innerHTML = `
                <div style="background: linear-gradient(135deg, #4CAF50, #45a049); color: white; padding: 15px; margin: -20px -20px 20px -20px; border-radius: 10px 10px 0 0;">
                    <h2 style="margin: 0; font-size: 24px;">🎨 Beautiful Pre-Reporting UI (LIVE)</h2>
                    <p style="margin: 5px 0 0 0; opacity: 0.9;">Real data from your payroll audit session</p>
                </div>
            `;
            container.appendChild(header);
            
            // Find a good place to insert it
            const mainContent = document.getElementById('main-content') || 
                              document.getElementById('payroll-audit-content') ||
                              document.querySelector('.tab-content.active') ||
                              document.querySelector('#payroll-audit-tab') ||
                              document.body;
            
            mainContent.appendChild(container);
            console.log('📦 Created new pre-reporting container');
        } else {
            // Clear existing container
            container.innerHTML = '';
            console.log('🧹 Cleared existing container');
        }
        
        // Step 4: Check if Interactive Pre-Reporting class is available
        if (!window.InteractivePreReporting) {
            console.log('⚠️ InteractivePreReporting class not found, attempting to load...');
            
            // Try to load the script
            const script = document.createElement('script');
            script.src = 'ui/interactive_pre_reporting.js';
            document.head.appendChild(script);
            
            // Wait for script to load
            await new Promise((resolve, reject) => {
                script.onload = resolve;
                script.onerror = () => reject(new Error('Failed to load interactive_pre_reporting.js'));
                setTimeout(() => reject(new Error('Timeout loading script')), 5000);
            });
            
            console.log('✅ Interactive pre-reporting script loaded');
        }
        
        if (!window.InteractivePreReporting) {
            throw new Error('InteractivePreReporting class still not available after loading script');
        }
        
        // Step 5: Initialize the Interactive Pre-Reporting UI
        console.log('🎨 Initializing beautiful pre-reporting UI...');
        
        // Create content container
        const contentContainer = document.createElement('div');
        contentContainer.id = 'beautiful-ui-content';
        container.appendChild(contentContainer);
        
        // Initialize the beautiful UI
        window.interactivePreReporting = new window.InteractivePreReporting(contentContainer);
        
        // Step 6: Render the beautiful UI with real data
        console.log('✨ Rendering beautiful UI with', preReportingData.data.length, 'real changes...');
        await window.interactivePreReporting.render(preReportingData.data);
        
        // Step 7: Update phase indicators to show pre-reporting is active
        const preReportingPhase = document.querySelector('[data-phase="pre-reporting"]') ||
                                document.getElementById('phase-pre-reporting');
        
        if (preReportingPhase) {
            preReportingPhase.classList.add('active', 'completed');
            const statusElement = preReportingPhase.querySelector('.phase-status');
            if (statusElement) {
                statusElement.textContent = 'Ready for Review';
                statusElement.style.color = '#4CAF50';
                statusElement.style.fontWeight = 'bold';
            }
            console.log('📊 Updated phase indicator');
        }
        
        // Step 8: Add some visual flair
        container.style.animation = 'fadeInUp 0.5s ease-out';
        
        // Add CSS animation if not already present
        if (!document.getElementById('beautiful-ui-animations')) {
            const style = document.createElement('style');
            style.id = 'beautiful-ui-animations';
            style.textContent = `
                @keyframes fadeInUp {
                    from {
                        opacity: 0;
                        transform: translateY(30px);
                    }
                    to {
                        opacity: 1;
                        transform: translateY(0);
                    }
                }
            `;
            document.head.appendChild(style);
        }
        
        // Step 9: Scroll to the beautiful UI
        container.scrollIntoView({ behavior: 'smooth', block: 'start' });
        
        // Step 10: Show success notification
        const notification = document.createElement('div');
        notification.innerHTML = `
            <div style="
                position: fixed; 
                top: 20px; 
                right: 20px; 
                background: #4CAF50; 
                color: white; 
                padding: 15px 20px; 
                border-radius: 8px; 
                box-shadow: 0 4px 12px rgba(0,0,0,0.2);
                z-index: 10000;
                font-family: 'Segoe UI', sans-serif;
                animation: slideInRight 0.3s ease-out;
            ">
                🎉 Beautiful Pre-Reporting UI Active!<br>
                <small>Showing ${preReportingData.data.length} real changes</small>
            </div>
        `;
        document.body.appendChild(notification);
        
        // Remove notification after 5 seconds
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 5000);
        
        console.log('🎉 BEAUTIFUL PRE-REPORTING UI SUCCESSFULLY TRIGGERED!');
        console.log(`📊 Showing ${preReportingData.data.length} real changes from session ${preReportingData.session_id}`);
        
        return {
            success: true,
            message: 'Beautiful pre-reporting UI rendered successfully',
            changesCount: preReportingData.data.length,
            sessionId: preReportingData.session_id,
            container: container
        };
        
    } catch (error) {
        console.error('❌ Error triggering real UI:', error);
        
        // Show error notification
        const errorNotification = document.createElement('div');
        errorNotification.innerHTML = `
            <div style="
                position: fixed; 
                top: 20px; 
                right: 20px; 
                background: #f44336; 
                color: white; 
                padding: 15px 20px; 
                border-radius: 8px; 
                box-shadow: 0 4px 12px rgba(0,0,0,0.2);
                z-index: 10000;
                font-family: 'Segoe UI', sans-serif;
                max-width: 300px;
            ">
                ❌ UI Trigger Failed<br>
                <small>${error.message}</small>
            </div>
        `;
        document.body.appendChild(errorNotification);
        
        setTimeout(() => {
            if (errorNotification.parentNode) {
                errorNotification.parentNode.removeChild(errorNotification);
            }
        }, 8000);
        
        return {
            success: false,
            error: error.message,
            stack: error.stack
        };
    }
})();
