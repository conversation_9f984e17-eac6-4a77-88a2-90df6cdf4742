<!DOCTYPE html>
<html>
<head>
    <title>Test Pre-Reporting UI</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        button { padding: 10px 20px; margin: 10px; font-size: 16px; }
        .test-container { border: 1px solid #ccc; padding: 20px; margin: 20px 0; }
        #pre-reporting-content { min-height: 400px; border: 2px dashed #ccc; padding: 20px; }
    </style>
</head>
<body>
    <h1>🧪 Pre-Reporting UI Test</h1>
    
    <div class="test-container">
        <h2>Test Controls</h2>
        <button onclick="testPreReportingUI()">🎯 Test Pre-Reporting UI</button>
        <button onclick="testAPICall()">🔍 Test API Call</button>
        <button onclick="clearContainer()">🧹 Clear Container</button>
    </div>
    
    <div class="test-container">
        <h2>Pre-Reporting UI Container</h2>
        <div id="pre-reporting-content">
            <p>Pre-reporting UI will appear here...</p>
        </div>
    </div>
    
    <div class="test-container">
        <h2>Debug Output</h2>
        <div id="debug-output" style="background: #f0f0f0; padding: 10px; font-family: monospace; white-space: pre-wrap;"></div>
    </div>

    <script>
        function log(message) {
            const debugOutput = document.getElementById('debug-output');
            debugOutput.textContent += new Date().toLocaleTimeString() + ': ' + message + '\n';
            console.log(message);
        }

        async function testAPICall() {
            log('🔍 Testing API call...');
            
            try {
                if (!window.api) {
                    log('❌ window.api not available');
                    return;
                }
                
                if (!window.api.getPreReportingData) {
                    log('❌ window.api.getPreReportingData not available');
                    return;
                }
                
                log('✅ API available, calling getPreReportingData...');
                const result = await window.api.getPreReportingData();
                
                log('📊 API Result:');
                log('  Success: ' + result.success);
                log('  Data Count: ' + (result.data ? result.data.length : 0));
                log('  Total Changes: ' + result.total_changes);
                
                if (result.success && result.data && result.data.length > 0) {
                    log('✅ API working! Sample data:');
                    const sample = result.data[0];
                    log('  Employee: ' + sample.employee_name + ' (' + sample.employee_id + ')');
                    log('  Change: ' + sample.section_name + ' - ' + sample.item_label);
                    log('  Type: ' + sample.change_type + ' (' + sample.priority + ')');
                } else {
                    log('⚠️ No data returned from API');
                }
                
            } catch (error) {
                log('❌ API call failed: ' + error.message);
            }
        }

        async function testPreReportingUI() {
            log('🎯 Testing Pre-Reporting UI...');
            
            try {
                // Check if InteractivePreReporting class is available
                if (!window.InteractivePreReporting) {
                    log('❌ InteractivePreReporting class not available');
                    log('🔄 Attempting to load script...');
                    
                    // Try to load the script
                    const script = document.createElement('script');
                    script.src = 'ui/interactive_pre_reporting.js';
                    script.onload = () => {
                        log('✅ Script loaded, retrying...');
                        testPreReportingUI();
                    };
                    script.onerror = () => {
                        log('❌ Failed to load script');
                    };
                    document.head.appendChild(script);
                    return;
                }
                
                log('✅ InteractivePreReporting class available');
                
                // Get container
                const container = document.getElementById('pre-reporting-content');
                if (!container) {
                    log('❌ Container not found');
                    return;
                }
                
                log('✅ Container found');
                
                // Clear container
                container.innerHTML = '<p>Loading pre-reporting UI...</p>';
                
                // Create InteractivePreReporting instance
                log('🎨 Creating InteractivePreReporting instance...');
                const preReporting = new window.InteractivePreReporting(container);
                
                // Initialize (this should load data from database)
                log('🎨 Initializing pre-reporting UI...');
                preReporting.initialize();
                
                log('✅ Pre-reporting UI initialization complete');
                
            } catch (error) {
                log('❌ Pre-reporting UI test failed: ' + error.message);
                console.error(error);
            }
        }

        function clearContainer() {
            const container = document.getElementById('pre-reporting-content');
            container.innerHTML = '<p>Pre-reporting UI will appear here...</p>';
            
            const debugOutput = document.getElementById('debug-output');
            debugOutput.textContent = '';
            
            log('🧹 Container cleared');
        }

        // Auto-test on load
        window.addEventListener('load', () => {
            log('🚀 Page loaded, ready for testing');
            log('📊 Available globals:');
            log('  window.api: ' + !!window.api);
            log('  window.InteractivePreReporting: ' + !!window.InteractivePreReporting);
        });
    </script>
</body>
</html>
