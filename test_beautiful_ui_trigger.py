#!/usr/bin/env python3
"""
Test Beautiful UI Trigger
Simulates the PRE_REPORTING phase completion to trigger the beautiful UI
"""

import os
import sys
import sqlite3
from pathlib import Path

def get_database_path():
    """Get the database path"""
    db_path = Path(__file__).parent / "data" / "templar_payroll_auditor.db"
    if db_path.exists():
        return str(db_path)
    return None

def trigger_pre_reporting_ui():
    """Trigger the pre-reporting UI by simulating phase completion"""
    
    print("🧪 TESTING BEAUTIFUL PRE-REPORTING UI TRIGGER")
    print("=" * 50)
    
    try:
        db_path = get_database_path()
        if not db_path:
            print("❌ Database file not found")
            return
        
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 1. Find session with data
        print("\n1. 📊 FINDING SESSION WITH DATA:")
        
        cursor.execute("""
            SELECT session_id, 
                   (SELECT COUNT(*) FROM comparison_results WHERE session_id = s.session_id) as comp_count,
                   (SELECT COUNT(*) FROM pre_reporting_results WHERE session_id = s.session_id) as pre_count
            FROM audit_sessions s
            WHERE (SELECT COUNT(*) FROM comparison_results WHERE session_id = s.session_id) > 0
            ORDER BY created_at DESC
            LIMIT 1
        """)
        
        session_data = cursor.fetchone()
        
        if not session_data:
            print("   ❌ No session with data found")
            return
        
        session_id, comp_count, pre_count = session_data
        print(f"   ✅ Found session: {session_id}")
        print(f"   📊 Comparison results: {comp_count}")
        print(f"   📊 Pre-reporting results: {pre_count}")
        
        # 2. Set this as current session
        print("\n2. 🔄 SETTING AS CURRENT SESSION:")
        
        # Clear current flags
        cursor.execute("UPDATE audit_sessions SET is_current = 0")
        
        # Set this session as current
        cursor.execute("UPDATE audit_sessions SET is_current = 1 WHERE session_id = ?", (session_id,))
        conn.commit()
        
        print(f"   ✅ Session {session_id} set as current")
        
        # 3. Simulate PRE_REPORTING phase completion
        print("\n3. 🎯 SIMULATING PRE_REPORTING PHASE COMPLETION:")
        
        try:
            sys.path.append(os.path.dirname(__file__))
            from core.phased_process_manager import PhasedProcessManager
            
            manager = PhasedProcessManager()
            manager.session_id = session_id
            
            # Test the get_pre_reporting_data method
            result = manager.get_pre_reporting_data()
            
            if result.get('success'):
                print(f"   ✅ Pre-reporting data ready: {len(result.get('data', []))} changes")
                
                # This would normally be called by the phase completion handler
                print("   🎨 UI should now trigger automatically when PRE_REPORTING completes")
                print("   📋 Expected UI elements:")
                print("     - Beautiful green container with gradient header")
                print("     - 'Pre-Reporting: Change Review & Selection' title")
                print("     - Categorized change display (Individual/Small/Medium/Large bulk)")
                print("     - Interactive selection checkboxes")
                print("     - 'Generate Final Report' button")
                
            else:
                print(f"   ❌ Pre-reporting data not ready: {result}")
        
        except Exception as e:
            print(f"   ❌ Error testing pre-reporting: {e}")
        
        # 4. Instructions for manual testing
        print("\n4. 📋 MANUAL TESTING INSTRUCTIONS:")
        print("   1. Run a payroll audit in the app")
        print("   2. Let it complete through all phases")
        print("   3. When PRE_REPORTING phase completes, the beautiful UI should appear")
        print("   4. Look for:")
        print("      - Green gradient header")
        print("      - Professional styling with shadows")
        print("      - Categorized change display")
        print("      - Interactive controls")
        
        conn.close()
        
        print("\n🎯 BEAUTIFUL UI TRIGGER TEST COMPLETE!")
        print("   The system is ready - run a full audit to see the beautiful UI!")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    trigger_pre_reporting_ui()
