<!DOCTYPE html>
<html>
<head>
    <title>Trigger Beautiful UI</title>
    <style>
        body {
            font-family: 'Segoe UI', sans-serif;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .trigger-container {
            background: white;
            padding: 40px;
            border-radius: 12px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            text-align: center;
            max-width: 500px;
        }
        .trigger-btn {
            background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
            color: white;
            border: none;
            padding: 20px 40px;
            font-size: 18px;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 10px;
            min-width: 200px;
        }
        .trigger-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(0,0,0,0.2);
        }
        .status {
            margin: 20px 0;
            padding: 15px;
            border-radius: 8px;
            display: none;
        }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
    </style>
</head>
<body>
    <div class="trigger-container">
        <h1>🎨 Beautiful Pre-Reporting UI Trigger</h1>
        <p>Click the button below to trigger the beautiful pre-reporting UI in your main application.</p>
        
        <button class="trigger-btn" onclick="triggerBeautifulUI()">
            🚀 Trigger Beautiful UI
        </button>
        
        <button class="trigger-btn" onclick="copyToClipboard()" style="background: linear-gradient(135deg, #2196F3 0%, #1976D2 100%);">
            📋 Copy Script to Clipboard
        </button>
        
        <div id="status" class="status"></div>
        
        <div style="margin-top: 30px; font-size: 14px; color: #666;">
            <p><strong>Instructions:</strong></p>
            <ol style="text-align: left;">
                <li>Make sure your Payroll Auditor app is running</li>
                <li>Click "Trigger Beautiful UI" or copy the script</li>
                <li>If copying, paste into the app's developer console (F12)</li>
                <li>The beautiful UI will appear with your real data</li>
            </ol>
        </div>
    </div>

    <script>
        const triggerScript = `
(async function triggerRealPreReportingUI() {
    try {
        console.log('🎯 TRIGGERING REAL PRE-REPORTING UI...');
        
        // Load pre-reporting data using the real API
        console.log('📊 Loading real pre-reporting data...');
        
        if (!window.api || !window.api.getLatestPreReportingData) {
            throw new Error('API not available. Make sure you are running this in the Electron app.');
        }
        
        const preReportingData = await window.api.getLatestPreReportingData();
        
        if (!preReportingData || !preReportingData.success) {
            throw new Error('Failed to load pre-reporting data: ' + (preReportingData?.error || 'Unknown error'));
        }
        
        console.log('✅ Pre-reporting data loaded:', preReportingData.data.length, 'changes');
        
        // Find or create container
        let container = document.getElementById('pre-reporting-container') || 
                       document.getElementById('pre-reporting-interactive-ui') ||
                       document.getElementById('processing-pre-reporting-container');
        
        if (!container) {
            container = document.createElement('div');
            container.id = 'pre-reporting-container';
            container.style.cssText = 'margin: 20px; padding: 20px; background: white; border-radius: 12px; box-shadow: 0 4px 20px rgba(0,0,0,0.1); border: 2px solid #4CAF50;';
            
            const header = document.createElement('div');
            header.innerHTML = '<div style="background: linear-gradient(135deg, #4CAF50, #45a049); color: white; padding: 15px; margin: -20px -20px 20px -20px; border-radius: 10px 10px 0 0;"><h2 style="margin: 0;">🎨 Beautiful Pre-Reporting UI (LIVE)</h2><p style="margin: 5px 0 0 0;">Real data from your payroll audit session</p></div>';
            container.appendChild(header);
            
            const mainContent = document.getElementById('main-content') || document.querySelector('.tab-content.active') || document.body;
            mainContent.appendChild(container);
        } else {
            container.innerHTML = '';
        }
        
        // Load InteractivePreReporting if needed
        if (!window.InteractivePreReporting) {
            const script = document.createElement('script');
            script.src = 'ui/interactive_pre_reporting.js';
            document.head.appendChild(script);
            await new Promise((resolve, reject) => {
                script.onload = resolve;
                script.onerror = () => reject(new Error('Failed to load script'));
                setTimeout(() => reject(new Error('Timeout')), 5000);
            });
        }
        
        // Create content container and render
        const contentContainer = document.createElement('div');
        container.appendChild(contentContainer);
        
        window.interactivePreReporting = new window.InteractivePreReporting(contentContainer);
        await window.interactivePreReporting.render(preReportingData.data);
        
        // Scroll to view
        container.scrollIntoView({ behavior: 'smooth', block: 'start' });
        
        // Success notification
        const notification = document.createElement('div');
        notification.innerHTML = '<div style="position: fixed; top: 20px; right: 20px; background: #4CAF50; color: white; padding: 15px 20px; border-radius: 8px; z-index: 10000;">🎉 Beautiful UI Active! Showing ' + preReportingData.data.length + ' changes</div>';
        document.body.appendChild(notification);
        setTimeout(() => notification.remove(), 5000);
        
        console.log('🎉 SUCCESS! Beautiful UI is now active!');
        return { success: true, changesCount: preReportingData.data.length };
        
    } catch (error) {
        console.error('❌ Error:', error);
        const errorNotification = document.createElement('div');
        errorNotification.innerHTML = '<div style="position: fixed; top: 20px; right: 20px; background: #f44336; color: white; padding: 15px 20px; border-radius: 8px; z-index: 10000;">❌ Failed: ' + error.message + '</div>';
        document.body.appendChild(errorNotification);
        setTimeout(() => errorNotification.remove(), 8000);
        return { success: false, error: error.message };
    }
})();
        `;

        function showStatus(message, type) {
            const status = document.getElementById('status');
            status.className = 'status ' + type;
            status.innerHTML = message;
            status.style.display = 'block';
        }

        async function triggerBeautifulUI() {
            try {
                showStatus('🔄 Attempting to trigger beautiful UI...', 'info');
                
                // Try to execute in the main app (if this page is loaded in Electron)
                if (typeof window.api !== 'undefined') {
                    // We're in Electron, execute directly
                    eval(triggerScript);
                    showStatus('✅ Beautiful UI trigger executed! Check your main app.', 'success');
                } else {
                    // We're in a regular browser, show instructions
                    showStatus('⚠️ Please copy the script and paste it into your Payroll Auditor app console (F12).', 'info');
                }
            } catch (error) {
                showStatus('❌ Error: ' + error.message, 'error');
            }
        }

        function copyToClipboard() {
            navigator.clipboard.writeText(triggerScript).then(() => {
                showStatus('📋 Script copied to clipboard! Paste it into your app console (F12).', 'success');
            }).catch(() => {
                // Fallback for older browsers
                const textArea = document.createElement('textarea');
                textArea.value = triggerScript;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);
                showStatus('📋 Script copied to clipboard! Paste it into your app console (F12).', 'success');
            });
        }
    </script>
</body>
</html>
