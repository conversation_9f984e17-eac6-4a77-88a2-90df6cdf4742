// THE PAYROLL AUDITOR - Payroll Comparison System

// CRITICAL: Initialize ContentSwitchingManager IMMEDIATELY at the very top
console.log('🔧 Early ContentSwitchingManager initialization...');

// Ensure ContentSwitchingManager is available from the start
function ensureContentSwitchingManager() {
  if (!window.ContentSwitchingManager) {
    console.log('🔧 Creating ContentSwitchingManager class early...');
    window.ContentSwitchingManager = class ContentSwitchingManager {
      constructor() {
        this.initialized = false;
        this.phases = ['extraction', 'comparison', 'auto-learning', 'tracker-feeding', 'pre-reporting', 'report-generation'];
        this.currentPhase = null;
        console.log('✅ Early ContentSwitchingManager created');
      }

      initialize() {
        this.initialized = true;
        console.log('✅ Early ContentSwitchingManager initialized');
        return true;
      }

      switchToPhase(phase, data = {}) {
        console.log(`🔄 Early ContentSwitchingManager switching to: ${phase}`);
        this.currentPhase = phase;
        return true;
      }
    };
  }

  if (!window.contentSwitchingManager) {
    console.log('🔧 Creating ContentSwitchingManager instance early...');
    window.contentSwitchingManager = new window.ContentSwitchingManager();
    window.contentSwitchingManager.initialize();
    console.log('✅ Early ContentSwitchingManager instance ready');
  }
}

// Initialize immediately
ensureContentSwitchingManager();

// CRITICAL: Define startPayrollAuditProcess IMMEDIATELY at the very top
// This MUST be the first thing that happens to ensure availability
window.startPayrollAuditProcess = async function() {
  console.log('🚀🚀🚀 START PAYROLL AUDIT PROCESS CALLED! 🚀🚀🚀');

  try {
    // STEP 1: Clean up any duplicate or problematic audit sessions first
    console.log('🧹 Cleaning duplicate audit sessions before starting...');
    const cleanupSuccess = await cleanDuplicateSessions();
    if (!cleanupSuccess) {
      console.warn('⚠️ Session cleanup failed, but continuing with audit...');
    }

    // Get file paths from global variables
    const auditCurrentPdfPath = window.auditCurrentPdfPath;
    const auditPreviousPdfPath = window.auditPreviousPdfPath;

    console.log('Current PDF:', auditCurrentPdfPath);
    console.log('Previous PDF:', auditPreviousPdfPath);
    console.log('Window Current PDF:', window.auditCurrentPdfPath);
    console.log('Window Previous PDF:', window.auditPreviousPdfPath);
    console.log('Local auditCurrentPdfPath variable:', auditCurrentPdfPath);
    console.log('Local auditPreviousPdfPath variable:', auditPreviousPdfPath);

    if (!auditCurrentPdfPath || !auditPreviousPdfPath) {
      alert('Please select both current and previous payroll files before starting the audit process');
      return;
    }
  } catch (error) {
    console.error('❌ Error during initial setup:', error);
    showNotification('Error during setup: ' + error.message, 'error');
    return;
  }

  // Get signature information
  const signatureNameElement = document.getElementById('signature-name');
  const signatureDesignationElement = document.getElementById('signature-designation');
  const reportTypeElement = document.getElementById('report-type-select');

  const signatureName = signatureNameElement ? signatureNameElement.value : '';
  const signatureDesignation = signatureDesignationElement ? signatureDesignationElement.value : '';
  const reportType = reportTypeElement ? reportTypeElement.value : 'traditional';

  if (!signatureName || !signatureDesignation) {
    alert('Please enter your name and designation for the report signature');
    return;
  }

  // Get selected months and years
  const currentMonth = document.getElementById('current-month')?.value || '';
  const currentYear = document.getElementById('current-year')?.value || '';
  const previousMonth = document.getElementById('previous-month')?.value || '';
  const previousYear = document.getElementById('previous-year')?.value || '';

  try {
    console.log('Starting backend processing...');

    // CRITICAL: Set process start time for timer
    processStartTime = Date.now();
    enhancedProcessActive = true;

    // CRITICAL: Transform UI to processing view FIRST
    transformToProcessingView(currentMonth, currentYear, previousMonth, previousYear, signatureName, signatureDesignation);

    // Hide start button
    const startBtn = document.getElementById('start-payroll-audit');
    if (startBtn) startBtn.style.display = 'none';

    // Create audit data object
    const auditData = {
      currentPayroll: auditCurrentPdfPath,
      previousPayroll: auditPreviousPdfPath,
      currentMonth: currentMonth,
      currentYear: currentYear,
      previousMonth: previousMonth,
      previousYear: previousYear,
      signatureName: signatureName || 'System Administrator',
      signatureDesignation: signatureDesignation || 'Payroll Auditor',
      reportType: reportType
    };

    // Call the enhanced backend API
    const result = await window.api.enhancedPayrollAudit(
      auditCurrentPdfPath,
      auditPreviousPdfPath,
      auditData
    );

    console.log('🎉 Enhanced audit result:', result);

    if (result.success) {
      console.log('✅ Payroll audit completed successfully');
      // Handle success - could switch to results view here
    } else {
      throw new Error(result.error);
    }

  } catch (error) {
    console.error('❌ Error during payroll audit:', error);
    alert('Error during payroll audit: ' + error.message);

    // Restore start button
    const startBtn = document.getElementById('start-payroll-audit');
    if (startBtn) startBtn.style.display = 'block';
  }
};

console.log('✅ IMMEDIATE: startPayrollAuditProcess defined and available globally');

// CRITICAL: Also make file path variables globally accessible immediately
window.auditCurrentPdfPath = null;
window.auditPreviousPdfPath = null;

// Application state
let currentPayrollPath = null;
let previousPayrollPath = null;
let comparisonResults = null;

// File dialog state to prevent multiple dialogs
window.fileDialogOpen = false;

// Enhanced features state
// Removed contentSwitchingManager - using direct phase transitions

// Track if UI is fully initialized
let uiInitialized = false;
let enhancedProcessActive = false;
let currentProcessPhase = null;
let processPaused = false;
let currentProcessId = null;
let processStartTime = null;
let timerInterval = null;
let lastEnhancedAuditResult = null; // Store the last audit result for report generation
let preReportingSelectedChanges = []; // Store selected changes for final report

// Payroll Audit tab state
let payrollAuditTabInitialized = false;
let auditCurrentPdfPath = null;
let auditPreviousPdfPath = null;

// Duplicate function definition removed - using the one at the top of the file

// Simple event system for pre-reporting communication
window.appEvents = {
  listeners: {},

  on: function(event, callback) {
    if (!this.listeners[event]) {
      this.listeners[event] = [];
    }
    this.listeners[event].push(callback);
  },

  emit: function(event, data) {
    if (this.listeners[event]) {
      this.listeners[event].forEach(callback => callback(data));
    }
  },

  off: function(event, callback) {
    if (this.listeners[event]) {
      this.listeners[event] = this.listeners[event].filter(cb => cb !== callback);
    }
  }
};

// Add pre-reporting event handling to app events system
window.appEvents.on('pre-reporting-selection-complete', function(selectedChanges) {
  console.log('📋 Pre-reporting selection complete, selected changes:', selectedChanges.length);
  preReportingSelectedChanges = selectedChanges;
});

/**
 * Filters out routine bulk changes that are not significant for reporting
 * Implements business logic to identify normal/expected changes vs. truly anomalous changes
 * 
 * @param {Array} changes - The array of payroll changes from comparison data
 * @returns {Array} - Filtered array of significant changes
 */
function filterRoutineBulkChanges(changes) {
  console.log('🔍 Filtering routine bulk changes from', changes.length, 'total changes');
  
  if (!changes || !Array.isArray(changes) || changes.length === 0) {
    return [];
  }
  
  // Group changes by type for analysis
  const changesByType = {};
  const totalEmployees = getEstimatedTotalEmployees(changes);
  
  // First pass: categorize changes and identify patterns
  changes.forEach(change => {
    const changeType = change.item_name || 'UNKNOWN';
    if (!changesByType[changeType]) {
      changesByType[changeType] = [];
    }
    changesByType[changeType].push(change);
  });
  
  // Filter out routine changes based on business rules
  return changes.filter(change => {
    // Rule 1: Explicitly marked routine changes are filtered out
    if (change.is_routine_change === true) {
      return false;
    }
    
    // Rule 2: Filter monthly recurring changes that affect most employees (like allowances)
    const changeType = change.item_name || 'UNKNOWN';
    const affectedCount = change.affected_employees || 1;
    const affectedPercentage = totalEmployees > 0 ? (affectedCount / totalEmployees) * 100 : 0;
    
    // Changes affecting more than 50% of employees might be routine bulk updates
    if (affectedPercentage > 50) {
      // But keep high priority ones regardless of bulk size
      if (change.priority === 'HIGH') {
        return true;
      }
      
      // Check if it's a known routine bulk change category
      const routineCategories = [
        'STAFF CREDIT UNION',
        'PENSION CONTRIBUTION',
        'STANDARD ALLOWANCE',
        'MONTHLY ADJUSTMENT'
      ];
      
      // If it's a routine category AND affects many employees, filter it out
      if (routineCategories.some(category => 
          changeType.toUpperCase().includes(category))) {
        return false;
      }
    }
    
    // Rule 3: Keep changes with significant financial impact regardless of other factors
    if (change.previous_value && change.current_value) {
      const prevValue = parseFloat(change.previous_value);
      const currValue = parseFloat(change.current_value);
      
      if (!isNaN(prevValue) && !isNaN(currValue)) {
        const difference = Math.abs(currValue - prevValue);
        const percentChange = prevValue !== 0 ? (difference / prevValue) * 100 : 100;
        
        // Significant financial changes (>10% change) should always be kept
        if (percentChange > 10) {
          return true;
        }
      }
    }
    
    // Default: Keep the change unless filtered by specific rules above
    return true;
  });
}

/**
 * Helper function to estimate total employees based on change data
 * @param {Array} changes - All payroll changes
 * @returns {number} - Estimated total employee count
 */
function getEstimatedTotalEmployees(changes) {
  // Get unique employee IDs to estimate total headcount
  const employeeIds = new Set();
  
  changes.forEach(change => {
    if (change.employee_id) {
      employeeIds.add(change.employee_id);
    }
  });
  
  return employeeIds.size > 0 ? employeeIds.size : 100; // Default to 100 if unknown
}

// Function to load comparison data from the database for pre-reporting phase
async function loadComparisonDataForPreReporting() {
  try {
    console.log('💾 Loading comparison data from database for pre-reporting UI...');

    // Show loading spinner
    const preReportingSpinner = document.getElementById('pre-reporting-loading-spinner');
    const preReportingInteractiveUI = document.getElementById('pre-reporting-interactive-ui');

    if (preReportingSpinner) preReportingSpinner.style.display = 'flex';
    if (preReportingInteractiveUI) preReportingInteractiveUI.style.display = 'none';

    // Get the current process ID to fetch the right data
    if (!currentProcessId) {
      console.error('❌ No active process ID found for loading comparison data');
      return;
    }

    // Fetch comparison data from the database using the backend API
    const comparisonData = await window.api.getComparisonDataFromDatabase(currentProcessId);

    if (!comparisonData || !comparisonData.success) {
      throw new Error('Failed to load comparison data: ' + (comparisonData?.error || 'Unknown error'));
    }

    console.log('📊 Comparison data loaded successfully:', comparisonData.changes?.length || 0, 'changes found');

    // Apply business rules - filter out routine bulk changes
    const filteredChanges = filterRoutineBulkChanges(comparisonData.changes || []);
    console.log('📏 After filtering routine bulk changes:', filteredChanges.length, 'significant changes remain');

    // Initialize the interactive pre-reporting UI with the comparison data
    if (window.InteractivePreReporting) {
      const preReportingUI = document.getElementById('pre-reporting-interactive-ui');
      if (preReportingUI) {
        // Hide loading spinner and show UI
        if (preReportingSpinner) preReportingSpinner.style.display = 'none';
        preReportingUI.style.display = 'block';

        // Initialize the interactive UI with data
        // Note: Passing the filtered changes as the analyzedChanges parameter
        window.interactivePreReporting = new window.InteractivePreReporting(preReportingUI, filteredChanges);
        window.interactivePreReporting.initialize();
      }
    } else {
      console.error('❌ InteractivePreReporting class not found');
    }
  } catch (error) {
    console.error('❌ Error loading comparison data for pre-reporting:', error);
    showNotification('Error loading comparison data: ' + error.message, 'error');

    // Display error in the pre-reporting UI area
    const preReportingUI = document.getElementById('pre-reporting-interactive-ui');
    if (preReportingUI) {
      preReportingUI.innerHTML = `
        <div class="error-message">
          <i class="fas fa-exclamation-triangle"></i>
          <h4>Error Loading Comparison Data</h4>
          <p>${error.message || 'Failed to load comparison data from the database.'}</p>
          <button class="btn primary" onclick="resetWorkflow()">
            <i class="fas fa-redo"></i> Reset Workflow
          </button>
        </div>
      `;
    }
  }
}

// Function to apply business rules to filter out routine bulk changes
function filterRoutineBulkChanges(changes) {
  // If no changes, return empty array
  if (!changes || !Array.isArray(changes)) return [];

  // Filter out routine bulk changes that don't represent actual payroll issues
  return changes.filter(change => {
    // Skip changes that affect more than 50% of employees (likely routine bulk changes)
    if (change.affected_percentage && change.affected_percentage > 50) {
      console.log('📊 Filtering out bulk change:', change.description,
                 '(affects', change.affected_percentage + '% of employees)');
      return false;
    }

    // Skip routine annual/periodic changes if marked as such
    if (change.is_routine_periodic === true) {
      console.log('📊 Filtering out routine periodic change:', change.description);
      return false;
    }

    // Keep changes that are flagged as significant
    if (change.is_significant === true) {
      return true;
    }

    // By default, include the change unless it's specifically filtered out above
    return true;
  });
}

// Function to set up event listeners for pre-reporting UI actions
function setupPreReportingEventListeners() {
  console.log('🔊 Setting up event listeners for pre-reporting UI...');

  // Back to Comparison button
  const backToComparisonBtn = document.getElementById('back-to-comparison-btn');
  if (backToComparisonBtn) {
    backToComparisonBtn.addEventListener('click', () => {
      console.log('🔙 Going back to comparison phase...');

      // Store any selections made so far
      if (window.interactivePreReporting) {
        const selectedChanges = window.interactivePreReporting.getSelectedChanges();
        preReportingSelectedChanges = selectedChanges;
      }

      // Show the enhanced progress panel again and return to comparison phase
      const enhancedPanel = document.getElementById('enhanced-progress-panel');
      if (enhancedPanel) {
        enhancedPanel.style.display = 'block';
      }

      // Hide the pre-reporting content
      const preReportingContent = document.getElementById('pre-reporting-content');
      if (preReportingContent) {
        preReportingContent.classList.remove('active');
      }

      // Update phase info to reflect we're back to the comparison phase
      updateEnhancedPhaseInfo('COMPARISON', 'Reviewing comparison results');
    });
  }

  // Generate Final Report button
  const generateFinalReportBtn = document.getElementById('generate-final-report-btn');
  if (generateFinalReportBtn) {
    generateFinalReportBtn.addEventListener('click', () => {
      console.log('📃 Generating final report with selected changes...');

      // Get the selected changes from the interactive UI
      let selectedChanges = [];
      if (window.interactivePreReporting) {
        selectedChanges = window.interactivePreReporting.getSelectedChanges();
        preReportingSelectedChanges = selectedChanges;

        // Trigger the pre-reporting-selection-complete event
        window.appEvents.emit('pre-reporting-selection-complete', selectedChanges);
      }

      // Show a confirmation if no changes are selected
      if (selectedChanges.length === 0) {
        if (!confirm('No changes have been selected. Generate an empty report?')) {
          return;
        }
      }

      // Proceed to the report generation phase
      proceedToReportGeneration(selectedChanges);
    });
  }
}

// Function to transition to the report generation phase with selected changes
function proceedToReportGeneration(selectedChanges) {
  // Hide pre-reporting content and show enhanced progress panel
  const preReportingContent = document.getElementById('pre-reporting-content');
  if (preReportingContent) {
    preReportingContent.classList.remove('active');
  }

  // Update phase to report generation
  showReportGenerationPhase('Generating final report with selected changes');

  // Call the backend API to generate the final report with selected changes
  generateFinalReportWithSelectedChanges(selectedChanges);
}

// Function to generate the final report using the selected changes
async function generateFinalReportWithSelectedChanges(selectedChanges) {
  try {
    console.log('📄 Starting final report generation with', selectedChanges.length, 'selected changes...');

    // Update progress panel
    updateCurrentOperation('Preparing report data', 'Organizing selected payroll changes');

    // Prepare report generation options with the selected changes
    const reportOptions = {
      processId: currentProcessId,
      selectedChanges: selectedChanges,
      includeAllEmployees: selectedChanges.length === 0, // If no changes selected, include all employees
      generateSignaturePage: true,
      includeComparisonSummary: true
    };

    // Generate reports with progress tracking
    await generateReportsWithProgress(lastEnhancedAuditResult, reportOptions);

    console.log('🗳️ Final report generation completed successfully!');

    // Show success notification
    showNotification('Final report generated successfully with selected changes!', 'success');

  } catch (error) {
    console.error('❌ Error generating final report:', error);
    showNotification('Error generating final report: ' + error.message, 'error');
  }
}

// Payroll Audit tab state variables already declared at top of file

// CRITICAL: Make audit file paths globally accessible for content switching manager
window.auditCurrentPdfPath = auditCurrentPdfPath;
window.auditPreviousPdfPath = auditPreviousPdfPath;

// CRITICAL: startPayrollAuditProcess is already defined at the top of the file
// No need to set it to null here - it's already available globally

// ... (rest of the code remains the same)

function showPreReportingPhase(description) {
  // Pre-reporting phase should show the interactive pre-reporting UI
  console.log('📊 Pre-reporting Phase - switching to interactive pre-reporting UI');

  // Update enhanced progress panel with pre-reporting phase info
  updateEnhancedPhaseInfo('PRE_REPORTING', description);

  // Hide enhanced progress panel and show pre-reporting interface
  const enhancedPanel = document.getElementById('enhanced-progress-panel');
  if (enhancedPanel) {
    enhancedPanel.style.display = 'none';
  }

  // Show pre-reporting content div and hide other phase content
  const allPhaseContent = document.querySelectorAll('.phase-content');
  allPhaseContent.forEach(content => content.classList.remove('active'));
  
  const preReportingContent = document.getElementById('pre-reporting-content');
  if (preReportingContent) {
    preReportingContent.classList.add('active');
  }
  
  // Load the latest comparison data from the database and initialize the UI
  loadComparisonDataForPreReporting();
  
  // Set up event listeners for the pre-reporting actions
  setupPreReportingEventListeners();
}

function showReportGenerationPhase(description) {
  // No longer create duplicate progress UI - enhanced progress panel (A2) handles all progress display
  console.log('📊 Report Generation Phase - using enhanced progress panel only');

  // Update enhanced progress panel with report generation phase info
  updateEnhancedPhaseInfo('REPORT_GENERATION', description);

  // Ensure enhanced progress panel is visible
  const enhancedPanel = document.getElementById('enhanced-progress-panel');
  if (enhancedPanel) {
    enhancedPanel.style.display = 'block';
  }
}

// Helper functions for enhanced UI
function updateComparisonStats(employeesCompared, changesFound) {
  const employeesElement = document.getElementById('employees-compared');
  const changesElement = document.getElementById('changes-found');

  if (employeesElement) {
    employeesElement.textContent = employeesCompared;
  }

  if (changesElement) {
    changesElement.textContent = changesFound;
  }
}

// REMOVED: simulateTrackerProgress - Real tracker progress comes from backend

// REMOVED: simulateReportProgress - Real report progress comes from backend

async function selectAuditFile(type) {
  try {
    console.log(`📂 Opening file dialog for ${type} payroll...`);

    // Prevent multiple simultaneous file dialogs
    if (window.fileDialogOpen) {
      console.log('⚠️ File dialog already open, ignoring request');
      return;
    }

    window.fileDialogOpen = true;

    // Show immediate visual feedback
    const buttonId = type === 'current' ? 'browse-current-payroll' : 'browse-previous-payroll';
    const button = document.getElementById(buttonId);

    if (button) {
      // Add loading state
      const originalText = button.innerHTML;
      button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Opening...';
      button.disabled = true;

      try {
        console.log(`🔄 Calling window.api.selectPdfFile() for ${type}...`);
        const filePath = await window.api.selectPdfFile();
        console.log(`📁 File dialog result for ${type}:`, filePath);

        if (filePath) {
          console.log(`✅ Processing selected file for ${type}: ${filePath}`);
          handleAuditFileSelection(filePath, type);
        } else {
          console.log(`ℹ️ No file selected for ${type}`);
        }
      } finally {
        // Restore button state
        button.innerHTML = originalText;
        button.disabled = false;
        window.fileDialogOpen = false;
      }
    } else {
      // Fallback if button not found
      try {
        console.log(`🔄 Fallback: Calling window.api.selectPdfFile() for ${type}...`);
        const filePath = await window.api.selectPdfFile();
        console.log(`📁 Fallback file dialog result for ${type}:`, filePath);

        if (filePath) {
          console.log(`✅ Fallback: Processing selected file for ${type}: ${filePath}`);
          handleAuditFileSelection(filePath, type);
        }
      } finally {
        window.fileDialogOpen = false;
      }
    }
  } catch (error) {
    console.error('File selection error:', error);
    showNotification('Error selecting file: ' + error.message, 'error');
    window.fileDialogOpen = false;
  }
}

function updateAuditFileInfo(elementId, filePath) {
  const infoElement = document.getElementById(elementId);
  if (infoElement) {
    const fileName = filePath.split('\\').pop().split('/').pop();
    infoElement.innerHTML = `<i class="fas fa-check-circle text-success"></i> ${fileName}`;
    infoElement.style.display = 'block';
  }
}

function displayAuditResults(comparisonResult, reportResult) {
  const resultsContainer = document.getElementById('audit-results');
  const auditContent = document.getElementById('audit-content');

  if (!resultsContainer || !auditContent) return;

  resultsContainer.style.display = 'block';

  const summary = comparisonResult.summary || {};
  auditContent.innerHTML = `
    <div class="audit-summary">
      <h4>Audit Summary</h4>
      <div class="summary-grid">
        <div class="summary-item">
          <span class="summary-label">Total Employees:</span>
          <span class="summary-value">${summary.total_employees || 0}</span>
        </div>
        <div class="summary-item">
          <span class="summary-label">Employees with Changes:</span>
          <span class="summary-value">${summary.employees_with_changes || 0}</span>
        </div>
        <div class="summary-item">
          <span class="summary-label">Total Changes Detected:</span>
          <span class="summary-value">${summary.total_changes || 0}</span>
        </div>
      </div>
    </div>

    <div class="report-files">
      <h4>Generated Reports</h4>
      <div class="report-links">
        ${reportResult.reports ? Object.entries(reportResult.reports).map(([format, path]) =>
          path ? `<a href="#" onclick="openReport('${path}')" class="report-link">
            <i class="fas fa-file-${getFileIcon(format)}"></i> ${format.toUpperCase()} Report
          </a>` : ''
        ).join('') : ''}
      </div>
    </div>
  `;
}

function getFileIcon(format) {
  const icons = {
    excel: 'excel',
    csv: 'csv',
    json: 'code',
    word: 'word',
    pdf: 'pdf'
  };
  return icons[format] || 'file';
}

async function openReport(reportPath) {
  try {
    await window.api.openFile(reportPath);
  } catch (error) {
    showNotification('Error opening report: ' + error.message, 'error');
  }
}

// REMOVED: Drag and drop functionality - Users should only use Browse Files buttons

function handleAuditFileSelection(filePath, type) {
  try {
    console.log(`📋 Processing file selection: ${type} = ${filePath}`);

    if (!filePath) {
      console.warn(`⚠️ No file path provided for ${type}`);
      return;
    }

    if (type === 'current') {
      auditCurrentPdfPath = filePath;
      window.auditCurrentPdfPath = filePath; // CRITICAL: Update window property
      console.log(`✅ Current payroll path set: ${filePath}`);
      updateAuditFileInfo('current-payroll-info', filePath);
    } else if (type === 'previous') {
      auditPreviousPdfPath = filePath;
      window.auditPreviousPdfPath = filePath; // CRITICAL: Update window property
      console.log(`✅ Previous payroll path set: ${filePath}`);
      updateAuditFileInfo('previous-payroll-info', filePath);
    } else {
      console.error(`❌ Invalid file type: ${type}`);
      return;
    }

    // Check if audit button should be enabled
    console.log(`🔍 Checking audit button state after ${type} file selection...`);
    checkAuditButtonState();

    console.log(`✅ File selection completed for ${type}`);
    showNotification(`${type === 'current' ? 'Current' : 'Previous'} payroll file selected`, 'success');
  } catch (error) {
    console.error(`❌ Error handling file selection for ${type}:`, error);
    showNotification(`Error processing ${type} file selection: ${error.message}`, 'error');
  }
}

function transformToProcessingView(currentMonth, currentYear, previousMonth, previousYear, signatureName, signatureDesignation) {
  console.log('🔄 TRANSFORMING TO PROCESSING VIEW...');
  console.log('Parameters:', { currentMonth, currentYear, previousMonth, previousYear, signatureName, signatureDesignation });

  // Use the existing enhanced progress panel instead of creating a new one
  console.log('✅ Using existing enhanced progress panel');

  // Show the enhanced progress panel
  showEnhancedProgressPanel();

  // Initialize phase indicators
  initializePhaseIndicators();

  // Update phase info for extraction phase
  updateEnhancedPhaseInfo('EXTRACTION', 'Starting Perfect Section-Aware extraction...');

  // Update phase indicator to show extraction as active
  updatePhaseIndicator('extraction', 'active');

  // Start the processing timer
  startProcessingTimer();

  console.log('✅ Enhanced progress panel transformation complete');
}

// Test function to verify the enhanced progress panel transformation
function testEnhancedProgressTransformation() {
  console.log('🧪 Testing enhanced progress panel transformation...');

  // Test the transformation with sample data
  transformToProcessingView('December', '2024', 'November', '2024', 'Test User', 'Test Designation');

  // Check if enhanced progress panel is visible
  const enhancedPanel = document.getElementById('enhanced-progress-panel');
  if (enhancedPanel && enhancedPanel.style.display === 'block') {
    console.log('✅ Enhanced progress panel is visible');

    // Check if phase indicators are initialized
    const phaseIndicators = document.querySelectorAll('.phase-indicator');
    console.log(`✅ Found ${phaseIndicators.length} phase indicators`);

    // Check if extraction phase is active
    const extractionPhase = document.getElementById('phase-extraction');
    if (extractionPhase && extractionPhase.classList.contains('active')) {
      console.log('✅ Extraction phase is active');
    } else {
      console.log('❌ Extraction phase is not active');
    }

    // Check if timer is running
    const timerDisplay = document.getElementById('timer-display');
    if (timerDisplay) {
      console.log('✅ Timer display found');
    } else {
      console.log('❌ Timer display not found');
    }

    return true;
  } else {
    console.log('❌ Enhanced progress panel is not visible');
    return false;
  }
}

// Make test function available globally for console testing
window.testEnhancedProgressTransformation = testEnhancedProgressTransformation;

function updateProcessingStepDetailed(stepNumber, status, statusText) {
  const stepElement = document.getElementById(`step-${stepNumber}`);
  if (stepElement) {
    // Remove all status classes
    stepElement.classList.remove('pending', 'active', 'completed', 'error');

    // Add new status class
    stepElement.classList.add(status);

    // Update status text
    const statusElement = stepElement.querySelector('.step-status');
    if (statusElement) {
      statusElement.textContent = statusText;
    }

    // Update icon based on status
    const iconElement = stepElement.querySelector('.step-icon i');
    if (iconElement) {
      if (status === 'active') {
        iconElement.className = 'fas fa-spinner fa-spin';
      } else if (status === 'completed') {
        iconElement.className = 'fas fa-check-circle';
      } else if (status === 'error') {
        iconElement.className = 'fas fa-exclamation-circle';
      } else if (status === 'pending') {
        // Reset to original icons for pending state
        if (stepNumber === 1) {
          iconElement.className = 'fas fa-download';
        } else if (stepNumber === 2) {
          iconElement.className = 'fas fa-balance-scale';
        } else if (stepNumber === 3) {
          iconElement.className = 'fas fa-database';
        } else if (stepNumber === 4) {
          iconElement.className = 'fas fa-user-check';
        } else if (stepNumber === 5) {
          iconElement.className = 'fas fa-chart-bar';
        }
      }
    }
  }
}

function resetAllStepsToPending() {
  for (let i = 1; i <= 5; i++) {
    updateProcessingStepDetailed(i, 'pending', 'Pending');
  }
}

function updateProgress(percentage, statusText) {
  // PRODUCTION FIX: Enhanced progress update with better UI synchronization
  const safePercentage = Math.max(0, Math.min(100, parseInt(percentage) || 0));

  // PRODUCTION FIX: Update the CORRECT progress bar elements (enhanced version)
  const progressElements = [
    document.getElementById('progress-fill-enhanced'), // Main enhanced progress bar
    document.getElementById('progress-fill'), // Legacy fallback
    document.querySelector('.progress-bar .progress'),
    document.querySelector('#main-progress-bar'),
    document.getElementById('extraction-progress'),
    document.querySelector('.progress-fill')
  ];

  progressElements.forEach(element => {
    if (element) {
      element.style.width = `${safePercentage}%`;
      element.style.transition = 'width 0.3s ease';
    }
  });

  // PRODUCTION FIX: Update the CORRECT percentage text elements (enhanced version)
  const percentageElements = [
    document.getElementById('progress-percentage-enhanced'), // Main enhanced percentage
    document.getElementById('progress-percentage'), // Legacy fallback
    document.querySelector('.progress-percentage'),
    document.querySelector('[data-progress="percentage"]')
  ];

  percentageElements.forEach(element => {
    if (element) {
      element.textContent = `${safePercentage}%`;
    }
  });

  // PRODUCTION FIX: Update the CORRECT status message elements (enhanced version)
  const statusElements = [
    document.getElementById('progress-status-enhanced'), // Main enhanced status
    document.getElementById('progress-status'), // Legacy fallback
    document.querySelector('.progress-status'),
    document.querySelector('[data-progress="status"]')
  ];

  statusElements.forEach(element => {
    if (element && statusText) {
      element.textContent = statusText || 'Processing...';
    }
  });

  console.log(`📊 Progress updated: ${safePercentage}% - ${statusText || 'No message'}`);

  // PRODUCTION GUARD: Verify progress accuracy for critical phases
  if (safePercentage >= 80 && statusText && statusText.toLowerCase().includes('completed')) {
    verifyProgressAccuracy(statusText, safePercentage);
  }
}

/**
 * PRODUCTION GUARD: Verify that progress reports match database reality
 * Prevents false completion reports and alerts users to inconsistencies
 */
async function verifyProgressAccuracy(statusText, percentage) {
  try {
    // Extract phase from status text
    const phaseMatches = statusText.match(/(EXTRACTION|COMPARISON|TRACKER_FEEDING|PRE_REPORTING|AUTO_LEARNING)/i);
    if (!phaseMatches) return;

    const phase = phaseMatches[1].toUpperCase();

    // Only verify for phases that should have database data
    const phasesToVerify = ['COMPARISON', 'TRACKER_FEEDING', 'PRE_REPORTING'];

    if (!phasesToVerify.includes(phase)) {
      return; // Skip verification for phases that don't need it
    }

    console.log(`🔍 GUARD: Verifying ${phase} completion accuracy...`);

    // Call backend to verify phase has actual data
    if (window.api && window.api.verifyPhaseData) {
      try {
        const hasData = await window.api.verifyPhaseData(phase);

        if (!hasData) {
          console.warn(`⚠️ PROGRESS GUARD ALERT: ${phase} shows ${percentage}% completed but no data found in database`);
          console.warn(`⚠️ This indicates a false completion report - phase may have failed silently`);

          // Show user warning
          const warningMsg = `⚠️ Warning: ${phase} phase reports completion but no data was found. This may indicate a processing error.`;

          // Add warning to activity log
          if (window.addRealTimeActivity) {
            window.addRealTimeActivity('warning', warningMsg, null, 'warning');
          }

          // Update UI to show warning state
          const phaseElement = document.getElementById(`phase-${phase.toLowerCase().replace('_', '-')}`);
          if (phaseElement) {
            phaseElement.classList.add('warning');
            phaseElement.title = 'Phase completion verification failed';
          }
        } else {
          console.log(`✅ GUARD PASSED: ${phase} completion verified with database data`);
        }
      } catch (apiError) {
        console.warn(`⚠️ Could not verify ${phase} data via API:`, apiError);
      }
    }

  } catch (error) {
    console.warn(`⚠️ Progress verification failed:`, error);
  }
}

// RELIABLE: Event-driven UI phase transition - more reliable than content switching
function updateUIPhase(phase, message, percentage) {
  console.log(`🔄 Updating UI for phase: ${phase} (${percentage}%)`);

  // RELIABLE: Direct UI updates with proper error handling
  try {
    // 1. Update phase indicators first (immediate visual feedback)
    updatePhaseIndicators(phase);

    // 2. Update progress (user sees progress immediately)
    if (percentage !== undefined) {
      updateProgress(percentage, message);
    }

    // 3. Update phase-specific UI elements
    updatePhaseSpecificUI(phase, message, percentage);

    // 4. Handle special phase transitions
    handleSpecialPhaseTransitions(phase, message, percentage);

  } catch (error) {
    console.error('❌ Error updating UI phase:', error);
    // Fallback to basic update
    updateCurrentOperation(phase, message || 'Processing...');
  }

  // Update the main phase header and description
  const phaseHeader = document.getElementById('current-phase-title');
  const phaseDescription = document.getElementById('current-phase-description');
  const phaseIcon = document.querySelector('#current-phase-icon i');

  // UPDATED PHASE CONFIGURATION - Zero Data Loss Architecture
  const phaseConfig = {
    'EXTRACTION': {
      title: 'Data Extraction Phase',
      description: 'Extracting payroll data from PDF documents...',
      icon: 'fas fa-file-upload'
    },
    'COMPARISON': {
      title: 'Comparison Phase',
      description: 'Comparing current and previous payroll data...',
      icon: 'fas fa-balance-scale'
    },
    'AUTO_LEARNING': {
      title: 'Auto Learning Phase',
      description: 'Learning new patterns and auto-approving items...',
      icon: 'fas fa-brain'
    },
    'TRACKER_FEEDING': {
      title: 'Tracker Feeding Phase',
      description: 'Feeding NEW items to tracker tables...',
      icon: 'fas fa-database'
    },
    'PRE_REPORTING': {
      title: 'Pre-Reporting Phase',
      description: 'Categorizing changes for user selection...',
      icon: 'fas fa-filter'
    },
    'REPORT_GENERATION': {
      title: 'Report Generation Phase',
      description: 'Generating Word, PDF, and Excel reports...',
      icon: 'fas fa-file-alt'
    },
    'REPORT_GENERATION': {
      title: 'Report Generation Phase',
      description: 'Generating final audit reports...',
      icon: 'fas fa-file-contract'
    }
  };

  const config = phaseConfig[phase];
  if (config && phaseHeader) {
    phaseHeader.textContent = config.title;
  }

  if (config && phaseDescription) {
    phaseDescription.textContent = config.description;
  }

  if (config && phaseIcon) {
    phaseIcon.className = config.icon;
  }

  // 🎯 Update phase indicators directly (no dependencies)
  const normalizedPhase = normalizePhaseNameForUI(phase);
  updatePhaseIndicatorsDirect(normalizedPhase);

  // Update current operation text
  if (message) {
    updateCurrentOperation(config?.title || phase, message);
  }

  // 🎯 BULLETPROOF: Handle PRE_REPORTING phase - direct UI switching
  if (phase === 'PRE_REPORTING' || phase === 'pre-reporting') {
    console.log('🔄 PRE_REPORTING phase detected - loading interactive UI via direct method');
    setTimeout(() => {
      loadPreReportingUIFromDatabase();
    }, 1000); // Small delay to ensure phase transition is complete
  }

  // 🎯 COMPLETE REDESIGN: Direct Pre-Reporting Activation
  if (message && message.includes && message.includes('waiting_for_user')) {
    console.log('🎯 COMPLETE REDESIGN: Direct pre-reporting activation triggered');

    // 🚨 NUCLEAR OPTION: Kill ALL processes immediately
    if (window.api && window.api.stopPayrollAudit) {
      console.log('🛑 NUCLEAR: Stopping ALL processes...');
      window.api.stopPayrollAudit();
    }

    // 🎯 IMMEDIATE REDESIGNED SOLUTION
    activateRedesignedPreReporting();
  }

  // 🎯 REDESIGNED: Single detection point for PRE_REPORTING
  if (phase === 'PRE_REPORTING' || phase === 'pre-reporting') {
    if (message && (message.includes('ready for user interaction') || message.includes('data ready'))) {
      console.log('🎯 REDESIGNED: PRE_REPORTING completion detected');

      // 🚨 NUCLEAR: Stop everything immediately
      if (window.api && window.api.stopPayrollAudit) {
        console.log('🛑 NUCLEAR: Stopping all processes...');
        window.api.stopPayrollAudit();
      }

      // 🎯 ACTIVATE REDESIGNED SOLUTION
      activateRedesignedPreReporting();
    }
  }

  // 🎯 REDESIGNED: Direct activation on TRACKER_FEEDING completion
  if (phase === 'TRACKER_FEEDING' || phase === 'tracker-feeding') {
    console.log('📊 REDESIGNED: Tracker feeding completed - direct pre-reporting activation');

    // 🎯 DIRECT ACTIVATION: Skip all intermediate steps
    setTimeout(() => {
      console.log('🎯 DIRECT: Activating pre-reporting after tracker feeding...');
      activateRedesignedPreReporting();
    }, 3000); // Give time for tracker feeding to complete
  }

  // 🚨 EMERGENCY: Detect infinite loops and break them
  if (message && message.includes && message.includes('phase_complete')) {
    // Count phase completions to detect loops
    window.phaseCompletionCount = (window.phaseCompletionCount || 0) + 1;

    if (window.phaseCompletionCount > 10) {
      console.log('🚨 EMERGENCY: Infinite loop detected - activating emergency pre-reporting');
      window.phaseCompletionCount = 0; // Reset counter

      // Emergency activation
      setTimeout(() => {
        activateRedesignedPreReporting();
      }, 1000);
    }
  }

  // Handle report generation phase
  if (phase === 'REPORT_GENERATION' || phase === 'report-generation') {
    console.log('📊 Report generation phase started - using direct method');
    setTimeout(() => {
      switchToReportGenerationPhaseDirect();
    }, 500);
  }

  console.log(`✅ UI updated for phase: ${phase}`);
}

// RELIABLE: Update phase-specific UI elements
function updatePhaseSpecificUI(phase, message, percentage) {
  // Update the main phase header and description
  const phaseHeader = document.getElementById('current-phase-title');
  const phaseDescription = document.getElementById('current-phase-description');
  const phaseIcon = document.querySelector('#current-phase-icon i');

  // UPDATED PHASE CONFIGURATION - Zero Data Loss Architecture
  const phaseConfig = {
    'EXTRACTION': {
      title: 'Data Extraction Phase',
      description: 'Extracting payroll data from PDF documents...',
      icon: 'fas fa-file-upload'
    },
    'COMPARISON': {
      title: 'Comparison Phase',
      description: 'Comparing current and previous payroll data...',
      icon: 'fas fa-balance-scale'
    },
    'AUTO_LEARNING': {
      title: 'Auto Learning Phase',
      description: 'Learning new patterns and auto-approving items...',
      icon: 'fas fa-brain'
    },
    'TRACKER_FEEDING': {
      title: 'Tracker Feeding Phase',
      description: 'Feeding NEW items to tracker tables...',
      icon: 'fas fa-database'
    },
    'PRE_REPORTING': {
      title: 'Pre-Reporting Phase',
      description: 'Categorizing changes for user selection...',
      icon: 'fas fa-filter'
    },
    'REPORT_GENERATION': {
      title: 'Report Generation Phase',
      description: 'Generating Word, PDF, and Excel reports...',
      icon: 'fas fa-file-alt'
    }
  };

  const config = phaseConfig[phase];
  if (config) {
    if (phaseHeader) phaseHeader.textContent = config.title;
    if (phaseDescription) phaseDescription.textContent = message || config.description;
    if (phaseIcon) phaseIcon.className = config.icon;
  }

  // Update current operation text
  if (message) {
    updateCurrentOperation(config?.title || phase, message);
  }
}

// RELIABLE: Handle special phase transitions
function handleSpecialPhaseTransitions(phase, message, percentage) {
  // CRITICAL: Handle PRE_REPORTING phase - show interactive UI
  if (phase === 'PRE_REPORTING' || phase === 'pre-reporting') {
    console.log('🔄 PRE_REPORTING phase detected - loading interactive UI');
    setTimeout(() => {
      loadPreReportingUIFromDatabase();
    }, 1000); // Small delay to ensure phase transition is complete
  }

  // Handle phase completion and transitions
  if (phase === 'TRACKER_FEEDING' || phase === 'tracker-feeding') {
    console.log('📊 Tracker feeding completed - preparing for pre-reporting');
  }

  // Handle report generation completion
  if (phase === 'REPORT_GENERATION' && percentage >= 100) {
    console.log('📄 Report generation completed');
    setTimeout(() => {
      updateCurrentOperation('Audit Complete', 'All reports have been generated successfully');
    }, 500);
  }
}

// RELIABLE: Phase state manager for consistent UI state
class PhaseStateManager {
  constructor() {
    this.currentPhase = null;
    this.phaseHistory = [];
    this.phaseData = {};
    this.isTransitioning = false;
  }

  updatePhase(phase, data = {}) {
    if (this.isTransitioning) {
      console.warn('⚠️ Phase transition already in progress, queuing update');
      setTimeout(() => this.updatePhase(phase, data), 100);
      return;
    }

    this.isTransitioning = true;

    try {
      // Store previous phase
      if (this.currentPhase) {
        this.phaseHistory.push(this.currentPhase);
      }

      // Update current phase
      this.currentPhase = phase;
      this.phaseData[phase] = { ...this.phaseData[phase], ...data, timestamp: Date.now() };

      // Trigger UI update
      updateUIPhase(phase, data.message, data.percentage);

      console.log(`✅ Phase state updated: ${phase}`);

    } catch (error) {
      console.error('❌ Error updating phase state:', error);
    } finally {
      this.isTransitioning = false;
    }
  }

  getCurrentPhase() {
    return this.currentPhase;
  }

  getPhaseData(phase) {
    return this.phaseData[phase] || {};
  }

  isPhaseComplete(phase) {
    const data = this.phaseData[phase];
    return data && (data.percentage >= 100 || data.completed === true);
  }
}

// Initialize global phase state manager
window.phaseStateManager = new PhaseStateManager();

// 🎯 100% BULLETPROOF: Direct DOM Pre-reporting UI Loader WITH BEAUTIFUL UI INTEGRATION
async function loadPreReportingUIFromDatabase() {
  console.log('🎨 Loading BEAUTIFUL pre-reporting UI from database...');

  try {
    // Get the latest pre-reporting data from our new phased process manager
    const response = await window.api.getLatestPreReportingData();

    if (!response || !response.success || !response.data || response.data.length === 0) {
      console.warn('❌ No pre-reporting data found in database');
      updateCurrentOperation('Pre-Reporting Error', 'No pre-reporting data found - please run the audit process first');
      return;
    }

    console.log(`🎨 Loaded ${response.data.length} pre-reporting records from database`);

    // Load the interactive pre-reporting script if not already loaded
    if (!window.InteractivePreReporting) {
      console.log('📋 Loading interactive pre-reporting script...');
      await loadInteractivePreReportingScript();
    }

    // 🎯 BULLETPROOF DIRECT UI SWITCHING - NO DEPENDENCIES
    console.log('🔄 Switching to pre-reporting phase via direct DOM manipulation');
    switchToPreReportingPhaseDirect();

    // 🎨 BEAUTIFUL UI INTEGRATION: Create and render the beautiful interface
    console.log('🎨 INITIALIZING BEAUTIFUL PRE-REPORTING UI...');

    // Find or create the beautiful UI container
    let beautifulContainer = document.getElementById('beautiful-pre-reporting-container');
    if (!beautifulContainer) {
      beautifulContainer = document.createElement('div');
      beautifulContainer.id = 'beautiful-pre-reporting-container';
      beautifulContainer.style.cssText = `
        margin: 20px;
        padding: 0;
        background: white;
        border-radius: 12px;
        box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        border: 2px solid #4CAF50;
        overflow: hidden;
        position: relative;
        z-index: 1000;
      `;

      // Add beautiful header
      const header = document.createElement('div');
      header.innerHTML = `
        <div style="
          background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
          color: white;
          padding: 20px;
          text-align: center;
          position: relative;
        ">
          <h2 style="margin: 0; font-size: 28px; font-weight: 600;">
            🎨 Beautiful Pre-Reporting Interface
          </h2>
          <p style="margin: 8px 0 0 0; opacity: 0.95; font-size: 16px;">
            Review and select ${response.data.length} detected changes from session ${response.session_id}
          </p>
          <div style="
            position: absolute;
            top: 10px;
            right: 15px;
            background: rgba(255,255,255,0.2);
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 12px;
          ">
            LIVE DATA
          </div>
        </div>
      `;
      beautifulContainer.appendChild(header);

      // Find the best place to insert the beautiful container
      const targetContainer = document.getElementById('pre-reporting-content') ||
                             document.getElementById('main-content') ||
                             document.querySelector('.tab-content.active') ||
                             document.body;

      // Clear existing content and add beautiful container
      if (targetContainer.id === 'pre-reporting-content') {
        targetContainer.innerHTML = '';
      }
      targetContainer.appendChild(beautifulContainer);

      console.log('🎨 Beautiful container created and inserted');
    }

    // 🔍 DIAGNOSTIC: Check data and dependencies before initialization
    console.log('🔍 DIAGNOSTIC INFO:');
    console.log('📊 Response data:', response);
    console.log('📊 Data array:', response.data);
    console.log('📊 Data length:', response.data ? response.data.length : 'undefined');
    console.log('📊 InteractivePreReporting class available:', !!window.InteractivePreReporting);
    console.log('📊 Beautiful container exists:', !!beautifulContainer);

    // Initialize the ACTUAL existing pre-reporting UI automatically (database-only)
    initializeInteractivePreReporting();

  } catch (error) {
    console.error('❌ Error loading pre-reporting UI:', error);
    updateCurrentOperation('Pre-Reporting Error', 'Failed to load pre-reporting interface');
  }
}

// 🎯 Simple notification for UI status
function showUINotification(message, type = 'info') {
  console.log(`🔔 ${message}`);

  // Create simple notification
  const notification = document.createElement('div');
  const bgColor = type === 'success' ? '#4CAF50' : type === 'error' ? '#f44336' : '#2196F3';

  notification.innerHTML = `
    <div style="
      position: fixed; top: 20px; right: 20px; background: ${bgColor};
      color: white; padding: 15px 20px; border-radius: 8px;
      box-shadow: 0 4px 12px rgba(0,0,0,0.2); z-index: 10000;
      font-family: 'Segoe UI', sans-serif;
    ">
      ${message}
    </div>
  `;

  document.body.appendChild(notification);
  setTimeout(() => notification.remove(), 4000);
}

// 🎨 Beautiful Notification System
function showBeautifulNotification(title, message, type = 'info') {
  console.log(`🔔 Showing notification: ${title} - ${message}`);

  // Remove any existing notifications
  const existingNotifications = document.querySelectorAll('.beautiful-notification');
  existingNotifications.forEach(notification => notification.remove());

  // Create notification element
  const notification = document.createElement('div');
  notification.className = 'beautiful-notification';

  const bgColor = type === 'success' ? '#4CAF50' :
                  type === 'error' ? '#f44336' :
                  type === 'warning' ? '#ff9800' : '#2196F3';

  notification.innerHTML = `
    <div style="
      position: fixed;
      top: 20px;
      right: 20px;
      background: ${bgColor};
      color: white;
      padding: 15px 20px;
      border-radius: 8px;
      box-shadow: 0 4px 12px rgba(0,0,0,0.2);
      z-index: 10000;
      font-family: 'Segoe UI', sans-serif;
      max-width: 350px;
      animation: slideInRight 0.3s ease-out;
    ">
      <div style="font-weight: bold; margin-bottom: 5px;">${title}</div>
      <div style="font-size: 14px; opacity: 0.9;">${message}</div>
    </div>
  `;

  // Add CSS animation if not already present
  if (!document.getElementById('notification-animations')) {
    const style = document.createElement('style');
    style.id = 'notification-animations';
    style.textContent = `
      @keyframes slideInRight {
        from {
          opacity: 0;
          transform: translateX(100%);
        }
        to {
          opacity: 1;
          transform: translateX(0);
        }
      }
    `;
    document.head.appendChild(style);
  }

  document.body.appendChild(notification);

  // Auto-remove after 5 seconds
  setTimeout(() => {
    if (notification.parentNode) {
      notification.style.animation = 'slideInRight 0.3s ease-out reverse';
      setTimeout(() => notification.remove(), 300);
    }
  }, 5000);
}

// 🎯 100% RELIABLE: Direct Phase Switching Function
function switchToPreReportingPhaseDirect() {
  console.log('🎯 Direct phase switching to pre-reporting - 100% reliable');

  // STEP 1: Hide all phase content panels
  const allPhaseContent = document.querySelectorAll('.phase-content, .content-panel');
  allPhaseContent.forEach(el => {
    el.style.display = 'none';
    el.classList.remove('active');
  });

  // STEP 2: Show pre-reporting panel
  const preReportingPanel = document.getElementById('pre-reporting-panel');
  if (preReportingPanel) {
    preReportingPanel.style.display = 'block';
    preReportingPanel.classList.add('active');
    console.log('✅ Pre-reporting panel made visible');
  }

  // STEP 3: Update phase indicators directly
  updatePhaseIndicatorsDirect('pre-reporting');

  // STEP 4: Update phase status
  updatePreReportingPhaseStatus();

  // STEP 5: Initialize phase-specific UI elements
  initializePreReportingUIElements();

  console.log('✅ Direct phase switching completed successfully');
}

// 🎯 EXTRACTED: Direct Phase Indicator Updates
function updatePhaseIndicatorsDirect(activePhase) {
  console.log(`📊 Updating phase indicators directly for: ${activePhase}`);

  const phases = ['extraction', 'comparison', 'auto-learning', 'tracker-feeding', 'pre-reporting', 'report-generation'];
  const currentIndex = phases.indexOf(activePhase);

  if (currentIndex === -1) {
    console.warn(`⚠️ Invalid phase: ${activePhase}`);
    return;
  }

  // Update each phase indicator
  phases.forEach((phase, index) => {
    const phaseElement = document.getElementById(`phase-${phase}`) ||
                        document.querySelector(`[data-phase="${phase}"]`);

    if (phaseElement) {
      // Remove all status classes
      phaseElement.classList.remove('pending', 'active', 'completed', 'in-progress');

      // Add appropriate status class
      if (index < currentIndex) {
        phaseElement.classList.add('completed');
        updatePhaseElementStatus(phaseElement, 'Completed', '✅');
      } else if (index === currentIndex) {
        phaseElement.classList.add('active', 'in-progress');
        updatePhaseElementStatus(phaseElement, 'Processing', '⚙️');
      } else {
        phaseElement.classList.add('pending');
        updatePhaseElementStatus(phaseElement, 'Pending', '⏱️');
      }
    }
  });

  console.log(`✅ Phase indicators updated - ${activePhase} is now active`);
}

// 🎯 HELPER: Update individual phase element
function updatePhaseElementStatus(phaseElement, statusText, icon) {
  // Update status text
  const statusElement = phaseElement.querySelector('.phase-status');
  if (statusElement) {
    statusElement.textContent = statusText;
  }

  // Update icon
  const iconElement = phaseElement.querySelector('.phase-icon i') ||
                     phaseElement.querySelector('.phase-dot i');
  if (iconElement) {
    iconElement.textContent = icon;
  }
}

// 🎯 EXTRACTED: Pre-reporting Phase Status Updates
function updatePreReportingPhaseStatus() {
  console.log('📋 Updating pre-reporting phase status');

  // Update panel status
  const panelStatus = document.querySelector('#pre-reporting-panel .panel-status');
  if (panelStatus) {
    panelStatus.textContent = 'Ready for Review';
    panelStatus.className = 'panel-status status-active';
  }

  // Update current phase title
  const phaseTitle = document.getElementById('current-phase-title');
  if (phaseTitle) {
    phaseTitle.textContent = 'Pre-Reporting Phase';
  }

  // Update current phase description
  const phaseDescription = document.getElementById('current-phase-description');
  if (phaseDescription) {
    phaseDescription.textContent = 'Review and select changes for final report generation';
  }

  // Update current phase icon
  const phaseIcon = document.querySelector('#current-phase-icon i');
  if (phaseIcon) {
    phaseIcon.className = 'fas fa-filter';
  }

  console.log('✅ Pre-reporting phase status updated');
}

// 🎯 EXTRACTED: Initialize Pre-reporting UI Elements
function initializePreReportingUIElements() {
  console.log('🔧 Initializing pre-reporting UI elements');

  // Re-attach event listeners for pre-reporting buttons
  const continueButton = document.getElementById('continue-to-report-generation');
  if (continueButton) {
    // Remove existing listeners
    continueButton.replaceWith(continueButton.cloneNode(true));
    const newContinueButton = document.getElementById('continue-to-report-generation');

    newContinueButton.addEventListener('click', () => {
      console.log('📊 Continue to report generation clicked');
      proceedToReportGenerationDirect();
    });
  }

  // Initialize back button
  const backButton = document.getElementById('back-to-comparison-btn');
  if (backButton) {
    backButton.replaceWith(backButton.cloneNode(true));
    const newBackButton = document.getElementById('back-to-comparison-btn');

    newBackButton.addEventListener('click', () => {
      console.log('🔄 Back to comparison clicked');
      switchToComparisonPhaseDirect();
    });
  }

  console.log('✅ Pre-reporting UI elements initialized');
}

// 🎯 BULLETPROOF: Direct Report Generation Transition
function proceedToReportGenerationDirect() {
  console.log('📊 Proceeding to report generation via direct method');

  // Get selected changes from interactive pre-reporting
  let selectedChanges = [];
  if (window.interactivePreReporting && typeof window.interactivePreReporting.getSelectedChanges === 'function') {
    selectedChanges = window.interactivePreReporting.getSelectedChanges();
    console.log(`📋 Retrieved ${selectedChanges.length} selected changes`);
  }

  // Switch to report generation phase
  switchToReportGenerationPhaseDirect();

  // Trigger report generation with selected changes
  if (window.api && window.api.generateFinalReport) {
    window.api.generateFinalReport({
      selected_changes: selectedChanges,
      phase: 'REPORT_GENERATION'
    });
  }
}

// 🎯 BULLETPROOF: Direct Report Generation Phase Switch
function switchToReportGenerationPhaseDirect() {
  console.log('🎯 Switching to report generation phase directly');

  // Hide all phase content panels
  const allPhaseContent = document.querySelectorAll('.phase-content, .content-panel');
  allPhaseContent.forEach(el => {
    el.style.display = 'none';
    el.classList.remove('active');
  });

  // Show report generation panel
  const reportPanel = document.getElementById('report-generation-panel') ||
                     document.getElementById('report-panel');
  if (reportPanel) {
    reportPanel.style.display = 'block';
    reportPanel.classList.add('active');
    console.log('✅ Report generation panel made visible');
  }

  // Update phase indicators
  updatePhaseIndicatorsDirect('report-generation');

  // Update phase status
  updateReportGenerationPhaseStatus();
}

// 🎯 BULLETPROOF: Direct Comparison Phase Switch (for back button)
function switchToComparisonPhaseDirect() {
  console.log('🎯 Switching back to comparison phase directly');

  // Hide all phase content panels
  const allPhaseContent = document.querySelectorAll('.phase-content, .content-panel');
  allPhaseContent.forEach(el => {
    el.style.display = 'none';
    el.classList.remove('active');
  });

  // Show comparison panel
  const comparisonPanel = document.getElementById('comparison-panel') ||
                         document.getElementById('comparison-content');
  if (comparisonPanel) {
    comparisonPanel.style.display = 'block';
    comparisonPanel.classList.add('active');
    console.log('✅ Comparison panel made visible');
  }

  // Update phase indicators
  updatePhaseIndicatorsDirect('comparison');

  // Update phase status
  updateComparisonPhaseStatus();
}

// 🎯 HELPER: Update Report Generation Phase Status
function updateReportGenerationPhaseStatus() {
  console.log('📊 Updating report generation phase status');

  // Update current phase title
  const phaseTitle = document.getElementById('current-phase-title');
  if (phaseTitle) {
    phaseTitle.textContent = 'Report Generation Phase';
  }

  // Update current phase description
  const phaseDescription = document.getElementById('current-phase-description');
  if (phaseDescription) {
    phaseDescription.textContent = 'Generating final payroll audit reports';
  }

  // Update current phase icon
  const phaseIcon = document.querySelector('#current-phase-icon i');
  if (phaseIcon) {
    phaseIcon.className = 'fas fa-file-alt';
  }

  console.log('✅ Report generation phase status updated');
}

// 🎯 HELPER: Update Comparison Phase Status
function updateComparisonPhaseStatus() {
  console.log('📊 Updating comparison phase status');

  // Update current phase title
  const phaseTitle = document.getElementById('current-phase-title');
  if (phaseTitle) {
    phaseTitle.textContent = 'Comparison Phase';
  }

  // Update current phase description
  const phaseDescription = document.getElementById('current-phase-description');
  if (phaseDescription) {
    phaseDescription.textContent = 'Comparing payroll data between periods';
  }

  // Update current phase icon
  const phaseIcon = document.querySelector('#current-phase-icon i');
  if (phaseIcon) {
    phaseIcon.className = 'fas fa-balance-scale';
  }

  console.log('✅ Comparison phase status updated');
}

// 🎯 HELPER: Normalize phase names for consistent UI handling
function normalizePhaseNameForUI(phase) {
  const phaseMap = {
    'EXTRACTION': 'extraction',
    'COMPARISON': 'comparison',
    'AUTO_LEARNING': 'auto-learning',
    'TRACKER_FEEDING': 'tracker-feeding',
    'PRE_REPORTING': 'pre-reporting',
    'REPORT_GENERATION': 'report-generation'
  };

  return phaseMap[phase] || phase.toLowerCase().replace(/_/g, '-');
}

// 🎯 DATABASE-ONLY: Initialize Interactive Pre-Reporting UI
function initializeInteractivePreReporting() {
  console.log('🎨 Initializing InteractivePreReporting UI (database-only mode)...');

  try {
    // Find the pre-reporting container with multiple fallbacks
    let container = document.getElementById('pre-reporting-content');

    if (!container) {
      // Try to find the panel content area
      const panel = document.getElementById('pre-reporting-panel');
      if (panel) {
        container = panel.querySelector('.panel-content') || panel;
      }
    }

    if (!container) {
      console.error('❌ Pre-reporting container not found in DOM');
      console.log('Available elements:', document.querySelectorAll('[id*="pre-reporting"], [class*="pre-reporting"]'));
      return;
    }

    console.log('✅ Container found:', container.id || container.className);

    // Clear any existing loading content
    container.innerHTML = '';

    // Check if InteractivePreReporting class is available
    if (!window.InteractivePreReporting) {
      console.log('⚠️ InteractivePreReporting class not loaded, attempting to load...');

      // Show loading state while we load the script
      container.innerHTML = `
        <div class="pre-reporting-loading">
          <div class="loading-spinner"></div>
          <h4>📋 Loading Interactive Interface...</h4>
          <p>Please wait while the pre-reporting interface loads...</p>
        </div>
      `;

      // Load the script and retry
      loadInteractivePreReportingScript().then(() => {
        console.log('✅ Script loaded, retrying initialization...');
        initializeInteractivePreReporting();
      }).catch(error => {
        console.error('❌ Failed to load interactive script:', error);
        showPreReportingError(container);
      });
      return;
    }

    // 🎯 DATABASE-ONLY: No data validation needed - UI will load from database

    // 🎨 BEAUTIFUL UI INTEGRATION: Create and use the beautiful container
    let beautifulContainer = document.getElementById('beautiful-pre-reporting-container');
    if (!beautifulContainer) {
      // Create the beautiful container
      console.log('🎨 Creating beautiful pre-reporting container...');
      beautifulContainer = document.createElement('div');
      beautifulContainer.id = 'beautiful-pre-reporting-container';
      beautifulContainer.style.cssText = `
        margin: 20px;
        padding: 0;
        background: white;
        border-radius: 12px;
        box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        border: 2px solid #4CAF50;
        overflow: hidden;
        position: relative;
        z-index: 1000;
      `;

      // Create the beautiful header
      const beautifulHeader = document.createElement('div');
      beautifulHeader.style.cssText = `
        background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
        color: white;
        padding: 20px;
        text-align: center;
        font-family: 'Segoe UI', sans-serif;
      `;
      beautifulHeader.innerHTML = `
        <h2 style="margin: 0; font-size: 24px; font-weight: 600;">
          📋 Pre-Reporting: Change Review & Selection
        </h2>
        <p style="margin: 10px 0 0 0; opacity: 0.9; font-size: 16px;">
          Review and select changes for final report generation
        </p>
      `;

      // Create the content area
      const contentArea = document.createElement('div');
      contentArea.id = 'beautiful-ui-content';
      contentArea.style.cssText = `
        padding: 0;
        min-height: 400px;
        background: #f8f9fa;
      `;

      // Assemble the beautiful container
      beautifulContainer.appendChild(beautifulHeader);
      beautifulContainer.appendChild(contentArea);

      // Insert into the page
      const targetContainer = container.parentElement ||
                             document.getElementById('pre-reporting-content') ||
                             document.getElementById('main-content') ||
                             document.querySelector('.tab-content.active') ||
                             document.body;

      if (targetContainer.id === 'pre-reporting-content') {
        targetContainer.innerHTML = '';
      }
      targetContainer.appendChild(beautifulContainer);

      console.log('🎨 Beautiful container created and inserted');
    }

    // Use the beautiful container content area
    const contentContainer = beautifulContainer.querySelector('#beautiful-ui-content');
    if (contentContainer) {
      container = contentContainer;
      console.log('🎨 Using beautiful container for rendering');
    }

    // 🎯 DATABASE-ONLY FIX: Initialize InteractivePreReporting without data
    // Let it load data from database automatically using window.api.getPreReportingData()
    console.log('✅ Creating InteractivePreReporting instance (database-only mode)');
    console.log('🔍 DEBUG: InteractivePreReporting class available:', !!window.InteractivePreReporting);
    console.log('🔍 DEBUG: window.api available:', !!window.api);
    console.log('🔍 DEBUG: window.api.getPreReportingData available:', !!(window.api && window.api.getPreReportingData));

    window.interactivePreReporting = new window.InteractivePreReporting(container);

    // 🎨 RENDER THE ACTUAL PRE-REPORTING UI WITH DATABASE DATA
    try {
      console.log('🎨 Initializing pre-reporting UI (will load from database)...');

      // CRITICAL FIX: Use initialize() method which will call loadDataFromDatabase()
      // This is the correct database-only approach
      if (typeof window.interactivePreReporting.initialize === 'function') {
        console.log('✅ Using initialize method (loads from database)');
        window.interactivePreReporting.initialize();
      } else {
        console.error('❌ InteractivePreReporting.initialize method not found');
        console.log('🔍 DEBUG: Available methods:', Object.getOwnPropertyNames(window.interactivePreReporting));
        throw new Error('InteractivePreReporting.initialize method not found');
      }

      console.log('🎉 ACTUAL Pre-reporting UI initialized successfully!');

      // Show success notification
      showUINotification('🎉 Pre-Reporting UI Active! Loading data from database...', 'success');

      // Scroll to the UI
      if (beautifulContainer) {
        beautifulContainer.scrollIntoView({ behavior: 'smooth', block: 'start' });
      } else if (container) {
        container.scrollIntoView({ behavior: 'smooth', block: 'start' });
      }

      // 🎯 DATABASE-ONLY COMPLETION: Simple, reliable completion
      setTimeout(() => {
        console.log('🎯 DATABASE-ONLY: Checking UI completion...');

        // Hide loading states
        if (window.interactivePreReporting && typeof window.interactivePreReporting.hideLoadingState === 'function') {
          window.interactivePreReporting.hideLoadingState();
        }

        // Hide the enhanced progress panel
        const enhancedPanel = document.getElementById('enhanced-progress-panel');
        if (enhancedPanel) {
          enhancedPanel.style.display = 'none';
          console.log('✅ Enhanced progress panel hidden');
        }

        // Show the pre-reporting panel
        const preReportingPanel = document.getElementById('pre-reporting-panel');
        if (preReportingPanel) {
          preReportingPanel.style.display = 'block';
          preReportingPanel.classList.add('active');
          console.log('✅ Pre-reporting panel shown');
        }

        console.log('✅ DATABASE-ONLY: UI completion handled cleanly');
      }, 2000);

      // Update phase info
      updateCurrentOperation('Pre-Reporting', `Review ${preReportingData.length} changes for final report`);

    } catch (initError) {
      console.error('❌ Error during InteractivePreReporting initialization:', initError);
      showPreReportingError(container);
    }

  } catch (error) {
    console.error('❌ Error initializing InteractivePreReporting:', error);
    const container = document.getElementById('pre-reporting-content') ||
                     document.getElementById('pre-reporting-panel');
    if (container) {
      showPreReportingError(container);
    }
  }
}

// 🚫 DISABLED FALLBACK: This simple fallback is replaced by enhanced InteractivePreReporting class
function forceRenderBeautifulPreReportingUI(container, preReportingData) {
  console.log('🚫 FALLBACK DISABLED: Using enhanced InteractivePreReporting class instead');
  console.log('🎯 Redirecting to enhanced interactive pre-reporting...');

  // Instead of rendering the simple fallback, use the enhanced InteractivePreReporting class
  if (window.InteractivePreReporting && container) {
    console.log('✅ Creating enhanced InteractivePreReporting instance...');
    window.interactivePreReporting = new window.InteractivePreReporting(container);
    window.interactivePreReporting.initialize();
    return;
  }

  console.log('❌ Enhanced InteractivePreReporting not available, showing error...');
  container.innerHTML = `
    <div class="pre-reporting-error">
      <h3>🔧 Enhanced Pre-Reporting Loading...</h3>
      <p>Please wait while the enhanced interactive interface loads.</p>
    </div>
  `;
  return;

  // OLD FALLBACK CODE DISABLED BELOW:
  /*

  // Categorize changes by bulk size
  const categorized = {
    individual: [],
    small_bulk: [],
    medium_bulk: [],
    large_bulk: []
  };

  // Simple categorization logic
  const changesByEmployee = {};
  preReportingData.forEach(change => {
    const empKey = change.employee_no || change.employee_id || 'UNKNOWN';
    if (!changesByEmployee[empKey]) changesByEmployee[empKey] = [];
    changesByEmployee[empKey].push(change);
  });

  Object.entries(changesByEmployee).forEach(([empKey, changes]) => {
    const count = changes.length;
    if (count <= 3) categorized.individual.push(...changes);
    else if (count <= 16) categorized.small_bulk.push(...changes);
    else if (count <= 32) categorized.medium_bulk.push(...changes);
    else categorized.large_bulk.push(...changes);
  });

  const totalChanges = preReportingData.length;
  const autoSelected = categorized.individual.length + categorized.small_bulk.filter(c =>
    ['Personal Details', 'Earnings', 'Deductions', 'Bank Details'].includes(c.section)
  ).length;

  // Render beautiful UI
  container.innerHTML = `
    <div class="pre-reporting-interface" style="padding: 20px; font-family: 'Segoe UI', sans-serif;">
      <div class="pre-reporting-header" style="background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%); color: white; padding: 30px; border-radius: 15px; margin-bottom: 25px; text-align: center;">
        <h3 style="margin: 0 0 10px 0; font-size: 24px;">📋 Pre-Reporting: Change Review & Selection</h3>
        <div class="summary-stats" style="display: flex; justify-content: center; gap: 30px; margin-top: 15px;">
          <span class="stat-item" style="background: rgba(255,255,255,0.1); padding: 10px 20px; border-radius: 8px;">
            <strong>${totalChanges}</strong> Total Changes
          </span>
          <span class="stat-item" style="background: rgba(255,255,255,0.1); padding: 10px 20px; border-radius: 8px;">
            <strong>${autoSelected}</strong> Auto-Selected
          </span>
          <span class="stat-item" style="background: rgba(255,255,255,0.1); padding: 10px 20px; border-radius: 8px;">
            <strong>${totalChanges - autoSelected}</strong> Pending Review
          </span>
        </div>
      </div>

      <div class="pre-reporting-actions" style="background: #f8f9fa; padding: 25px; border-radius: 15px; margin-top: 25px; text-align: center;">
        <button class="btn secondary" onclick="selectAllHighPriority()" style="background: #6c757d; color: white; border: none; padding: 12px 25px; border-radius: 8px; margin: 0 10px; cursor: pointer; font-size: 14px;">
          Select All High Priority
        </button>
        <button class="btn secondary" onclick="clearAllSelections()" style="background: #6c757d; color: white; border: none; padding: 12px 25px; border-radius: 8px; margin: 0 10px; cursor: pointer; font-size: 14px;">
          Clear All Selections
        </button>
        <button class="btn primary large" onclick="proceedToReportGenerationDirect()" style="background: linear-gradient(135deg, #007bff 0%, #0056b3 100%); color: white; border: none; padding: 15px 40px; border-radius: 8px; margin: 0 10px; cursor: pointer; font-size: 16px; font-weight: bold;">
          Generate Final Report (${autoSelected} changes)
        </button>
      </div>

      <div style="margin-top: 20px; padding: 15px; background: #e8f5e8; border-radius: 8px; text-align: center;">
        <p style="margin: 0; color: #2d5a2d; font-weight: 500;">
          📊 <strong>${totalChanges}</strong> payroll changes loaded and categorized for review
        </p>
      </div>
    </div>
  `;

  console.log('✅ Beautiful pre-reporting UI rendered successfully!');
  */ // END OF DISABLED FALLBACK CODE
}

// 🎯 COMPLETE REDESIGN: Single Function Solution
async function activateRedesignedPreReporting() {
  console.log('🎯 COMPLETE REDESIGN: Activating redesigned pre-reporting system...');

  try {
    // Step 1: NUCLEAR - Stop all processes
    console.log('🛑 STEP 1: Nuclear process termination...');
    if (window.api && window.api.stopPayrollAudit) {
      await window.api.stopPayrollAudit();
    }

    // Step 2: FORCE UI transition
    console.log('🎯 STEP 2: Force UI transition...');

    // Hide ALL competing panels
    const enhancedPanel = document.getElementById('enhanced-progress-panel');
    const auditPanel = document.getElementById('audit-panel');
    const progressPanel = document.querySelector('.progress-panel');

    if (enhancedPanel) {
      enhancedPanel.style.display = 'none';
      enhancedPanel.style.visibility = 'hidden';
      console.log('✅ Enhanced progress panel NUKED');
    }

    if (auditPanel) {
      auditPanel.style.display = 'none';
      console.log('✅ Audit panel hidden');
    }

    if (progressPanel) {
      progressPanel.style.display = 'none';
      console.log('✅ Progress panel hidden');
    }

    // Show ONLY pre-reporting panel
    const preReportingPanel = document.getElementById('pre-reporting-panel');
    if (preReportingPanel) {
      preReportingPanel.style.display = 'block';
      preReportingPanel.style.visibility = 'visible';
      preReportingPanel.classList.add('active');
      console.log('✅ Pre-reporting panel ACTIVATED');
    }

    // Step 3: POPULATE tracker tables
    console.log('🎯 STEP 3: Populate tracker tables...');
    await populateTrackerTablesRedesigned();

    // Step 4: LOAD beautiful UI
    console.log('🎯 STEP 4: Load beautiful UI...');
    await loadPreReportingUIFromDatabase();

    console.log('🎯 REDESIGNED PRE-REPORTING ACTIVATED SUCCESSFULLY!');
    showNotification('Pre-reporting interface activated!', 'success');

  } catch (error) {
    console.error('❌ Error in redesigned pre-reporting activation:', error);
    showNotification('Failed to activate pre-reporting interface', 'error');
  }
}

// 🎯 REDESIGNED: Tracker Table Population
async function populateTrackerTablesRedesigned() {
  console.log('🔄 REDESIGNED: Populating Bank Adviser tables...');

  try {
    const result = await window.api.populateTrackerTables();

    if (result && result.success) {
      console.log('✅ Tracker tables populated:', result);
      showNotification(`Tracker tables: ${result.total} records populated`, 'success');
    } else {
      console.warn('⚠️ Tracker population issues:', result);
      showNotification('Tracker tables populated with warnings', 'warning');
    }
  } catch (error) {
    console.error('❌ Tracker population error:', error);
    showNotification('Tracker table population failed', 'error');
  }
}

// 🎯 HELPER FUNCTIONS: For beautiful UI button actions
function selectAllHighPriority() {
  console.log('📋 Select All High Priority clicked');
  showNotification('High priority changes selected for report generation', 'success');
}

function clearAllSelections() {
  console.log('📋 Clear All Selections clicked');
  showNotification('All selections cleared', 'info');
}

// 🎯 HELPER: Show pre-reporting error state
function showPreReportingError(container) {
  container.innerHTML = `
    <div class="pre-reporting-error">
      <h3>❌ Pre-reporting Interface Error</h3>
      <p>Unable to load the interactive pre-reporting interface.</p>
      <div class="error-actions">
        <button class="btn secondary" onclick="location.reload()">
          🔄 Refresh Page
        </button>
        <button class="btn primary" onclick="proceedToReportGenerationDirect()">
          📊 Skip to Report Generation
        </button>
      </div>
    </div>
  `;
}

// Load the interactive pre-reporting script dynamically
function loadInteractivePreReportingScript() {
  return new Promise((resolve, reject) => {
    if (window.InteractivePreReporting) {
      resolve();
      return;
    }

    const script = document.createElement('script');
    script.src = './ui/interactive_pre_reporting.js';
    script.onload = () => {
      console.log('✅ Interactive pre-reporting script loaded');
      resolve();
    };
    script.onerror = () => {
      console.error('❌ Failed to load interactive pre-reporting script');
      reject(new Error('Failed to load interactive pre-reporting script'));
    };
    document.head.appendChild(script);
  });
}

// Show the pre-reporting interface with comparison data
function showPreReportingInterface(comparisonData) {
  console.log('📋 Showing pre-reporting interface with', comparisonData.length, 'changes');

  // Find or create the pre-reporting container
  let preReportingContainer = document.getElementById('pre-reporting-interactive-ui');

  if (!preReportingContainer) {
    // Create the container if it doesn't exist
    preReportingContainer = document.createElement('div');
    preReportingContainer.id = 'pre-reporting-interactive-ui';
    preReportingContainer.className = 'pre-reporting-container';

    // Insert it into the main content area
    const mainContent = document.querySelector('.enhanced-progress-panel') || document.body;
    mainContent.appendChild(preReportingContainer);
  }

  // Hide the progress panel and show the pre-reporting UI
  const progressPanel = document.querySelector('.enhanced-progress-panel');
  if (progressPanel) {
    progressPanel.style.display = 'none';
  }

  preReportingContainer.style.display = 'block';

  // Initialize the interactive pre-reporting UI
  if (window.InteractivePreReporting) {
    console.log('📋 Initializing InteractivePreReporting with', comparisonData.length, 'changes');
    window.interactivePreReporting = new window.InteractivePreReporting(preReportingContainer, comparisonData);
    window.interactivePreReporting.initialize();

    // Update the operation status
    updateCurrentOperation('Pre-Reporting Ready', 'Please review and select changes for the final report');

    console.log('✅ Pre-reporting interface ready for user interaction');
  } else {
    console.error('❌ InteractivePreReporting class not available');
    updateCurrentOperation('Pre-Reporting Error', 'Interactive pre-reporting interface not available');
  }
}

// Update phase indicators in the top navigation
function updatePhaseIndicators(currentPhase) {
  const phaseIndicators = document.querySelectorAll('.phase-indicator');

  // OPTIMIZED: Map backend phase names to UI data-phase values - aligned with phased process manager
  const phaseMapping = {
    'EXTRACTION': 'extraction',
    'COMPARISON': 'comparison',
    'AUTO_LEARNING': 'auto-learning',
    'TRACKER_FEEDING': 'tracker-feeding',
    'PRE_REPORTING': 'pre-reporting',
    'REPORT_GENERATION': 'report-generation'
  };

  const phaseOrder = [
    'extraction',
    'comparison',
    'auto-learning',
    'tracker-feeding',
    'pre-reporting',
    'report-generation'
  ];

  const uiPhase = phaseMapping[currentPhase] || currentPhase.toLowerCase();
  const currentIndex = phaseOrder.indexOf(uiPhase);

  phaseIndicators.forEach((indicator) => {
    const indicatorPhase = indicator.getAttribute('data-phase');
    const indicatorIndex = phaseOrder.indexOf(indicatorPhase);
    const statusElement = indicator.querySelector('.phase-status');

    // Remove all status classes
    indicator.classList.remove('active', 'completed', 'pending');

    // Add appropriate status class and update status text
    if (indicatorIndex === currentIndex) {
      indicator.classList.add('active');
      if (statusElement) statusElement.textContent = 'Active';
    } else if (indicatorIndex < currentIndex) {
      indicator.classList.add('completed');
      if (statusElement) statusElement.textContent = 'Completed';
    } else {
      indicator.classList.add('pending');
      if (statusElement) statusElement.textContent = 'Pending';
    }
  });

  console.log(`📊 Phase indicators updated for: ${currentPhase} -> ${uiPhase}`);
}

function updateCurrentOperation(operation, details) {
  const currentOperation = document.getElementById('current-operation');
  const operationDetails = document.getElementById('operation-details');

  if (currentOperation) {
    currentOperation.textContent = operation;
  }

  if (operationDetails) {
    operationDetails.textContent = details;
  }

  // CRITICAL: Add manual pre-reporting trigger button when needed
  if (operation === 'Auto-Learning Phase' && details && details.includes('completed')) {
    addManualPreReportingButton();
  }
}

// Add a manual button to trigger pre-reporting UI if it doesn't appear automatically
function addManualPreReportingButton() {
  // Check if button already exists
  if (document.getElementById('manual-pre-reporting-btn')) {
    return;
  }

  // Find a good place to add the button (near the operation details)
  const operationDetails = document.getElementById('operation-details');
  if (operationDetails && operationDetails.parentNode) {
    const buttonContainer = document.createElement('div');
    buttonContainer.style.marginTop = '10px';
    buttonContainer.style.textAlign = 'center';

    const button = document.createElement('button');
    button.id = 'manual-pre-reporting-btn';
    button.className = 'btn btn-primary';
    button.innerHTML = '<i class="fas fa-filter"></i> Load Pre-Reporting Interface';
    button.onclick = () => {
      console.log('🔄 Manual pre-reporting button clicked');
      button.disabled = true;
      button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Loading...';
      loadPreReportingUIFromDatabase();
      // Remove button after clicking
      setTimeout(() => {
        if (buttonContainer.parentNode) {
          buttonContainer.parentNode.removeChild(buttonContainer);
        }
      }, 1000);
    };

    buttonContainer.appendChild(button);
    operationDetails.parentNode.appendChild(buttonContainer);
    console.log('✅ Manual pre-reporting button added');
  }
}

let processingStartTime;
let processingTimer;

// Timer functionality consolidated - using single timer system from line 6994

// Debounced update function to prevent UI freezing
let updateStatsTimeout = null;
let lastStatsUpdate = 0;

function updateProcessingStats(employeesProcessed, changesDetected) {
  const now = Date.now();

  // Throttle updates to max 10 per second to prevent UI freezing
  if (now - lastStatsUpdate < 100) {
    clearTimeout(updateStatsTimeout);
    updateStatsTimeout = setTimeout(() => {
      updateProcessingStatsImmediate(employeesProcessed, changesDetected);
    }, 100);
    return;
  }

  updateProcessingStatsImmediate(employeesProcessed, changesDetected);
  lastStatsUpdate = now;
}

function updateProcessingStatsImmediate(employeesProcessed, changesDetected) {
  // Use requestAnimationFrame for smooth UI updates during heavy processing
  requestAnimationFrame(() => {
    try {
      // PRODUCTION FIX: Update both old and new stats elements
      const employeesElement = document.getElementById('employees-processed');
      const changesElement = document.getElementById('changes-detected');

      if (employeesElement && employeesElement.textContent !== employeesProcessed.toString()) {
        employeesElement.textContent = employeesProcessed;
      }

      if (changesElement && changesElement.textContent !== changesDetected.toString()) {
        changesElement.textContent = changesDetected;
      }

      // PRODUCTION FIX: Update enhanced stats panel elements
      const enhancedEmployeesElementByClass = document.querySelector('.stat-value[data-stat="employees"]');
      const enhancedChangesElementByClass = document.querySelector('.stat-value[data-stat="changes"]');

      if (enhancedEmployeesElementByClass && enhancedEmployeesElementByClass.textContent !== employeesProcessed.toString()) {
        enhancedEmployeesElementByClass.textContent = employeesProcessed;
      }

      if (enhancedChangesElementByClass && enhancedChangesElementByClass.textContent !== changesDetected.toString()) {
        enhancedChangesElementByClass.textContent = changesDetected;
      }

      // Update enhanced stats elements (primary)
      const enhancedEmployeesElement = document.getElementById('employees-processed-enhanced');
      const enhancedChangesElement = document.getElementById('changes-detected-enhanced');

      if (enhancedEmployeesElement && enhancedEmployeesElement.textContent !== employeesProcessed.toString()) {
        enhancedEmployeesElement.textContent = employeesProcessed;
        // Add animation effect
        enhancedEmployeesElement.style.transform = 'scale(1.1)';
        setTimeout(() => { enhancedEmployeesElement.style.transform = 'scale(1)'; }, 200);
      }

      if (enhancedChangesElement && enhancedChangesElement.textContent !== changesDetected.toString()) {
        enhancedChangesElement.textContent = changesDetected;
        // Add animation effect
        enhancedChangesElement.style.transform = 'scale(1.1)';
        setTimeout(() => { enhancedChangesElement.style.transform = 'scale(1)'; }, 200);
      }

      // Also update processing speed and estimated time
      const processingSpeed = calculateProcessingSpeed(employeesProcessed);
      const estimatedTime = estimateRemainingTime(50); // Use 50% as default if no percentage available

      const speedElement = document.getElementById('processing-speed');
      const timeElement = document.getElementById('estimated-time');

      if (speedElement) {
        speedElement.textContent = `${processingSpeed}/min`;
      }

      if (timeElement) {
        timeElement.textContent = estimatedTime;
      }

      console.log('📊 Stats updated:', {
        employees: employeesProcessed,
        changes: changesDetected,
        speed: processingSpeed,
        timeRemaining: estimatedTime
      });

    } catch (error) {
      console.warn('Error updating processing stats:', error);
    }
  });
}

// Real-time calculation functions
function calculateProcessingSpeed(employeesProcessed) {
  if (!processingStartTime || employeesProcessed === 0) {
    return '0';
  }

  const elapsedMinutes = (Date.now() - processingStartTime) / (1000 * 60);
  if (elapsedMinutes === 0) return '0';

  const speed = Math.round(employeesProcessed / elapsedMinutes);
  return speed.toString();
}

function estimateRemainingTime(currentPercentage) {
  if (!processingStartTime || currentPercentage === 0 || currentPercentage >= 100) {
    return '--:--';
  }

  const elapsedMs = Date.now() - processingStartTime;
  const totalEstimatedMs = (elapsedMs / currentPercentage) * 100;
  const remainingMs = totalEstimatedMs - elapsedMs;

  if (remainingMs <= 0) return '00:00';

  const minutes = Math.floor(remainingMs / (1000 * 60));
  const seconds = Math.floor((remainingMs % (1000 * 60)) / 1000);

  return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
}

function getCurrentActiveStep() {
  const steps = document.querySelectorAll('.step-item');
  for (let i = 0; i < steps.length; i++) {
    if (steps[i].classList.contains('active')) {
      return i + 1;
    }
  }
  return 1; // Default to first step
}

// Stop payroll audit process
async function stopPayrollAuditProcess() {
  console.log('🛑 Stopping payroll audit process...');

  try {
    // Call backend to stop all running processes
    const stopResult = await window.api.stopPayrollAudit();

    if (stopResult.success) {
      console.log(`✅ Successfully stopped ${stopResult.stopped_processes} processes`);
      showNotification(`Stopped ${stopResult.stopped_processes} running processes`, 'success');
    } else {
      console.error('❌ Failed to stop processes:', stopResult.error);
      showNotification('Failed to stop some processes', 'warning');
    }
  } catch (error) {
    console.error('❌ Error stopping processes:', error);
    showNotification('Error stopping audit process', 'error');
  }

  // Stop processing timer
  stopProcessTimer();

  // Update current operation
  updateCurrentOperation('Audit Stopped', 'Payroll audit was stopped by user');
  updateProcessingStepDetailed(getCurrentActiveStep(), 'error', 'Stopped');

  // Show notification
  showNotification('Payroll audit stopped by user', 'warning');

  // Restore buttons
  restoreAuditButtons();

  // Restore original view after 2 seconds
  setTimeout(() => {
    restoreOriginalView();
  }, 2000);
}

// Restore audit buttons to original state
function restoreAuditButtons() {
  const startBtn = document.getElementById('start-payroll-audit');

  if (startBtn) startBtn.style.display = 'inline-block';

  // Hide enhanced progress panel and show file upload panels
  hideProcessingControls();
  if (progressSection) progressSection.style.display = 'none';
}

// Restore original view
function restoreOriginalView() {
  console.log('🔄 Restoring original view...');

  // Hide enhanced progress panel
  hideEnhancedProgressPanel();

  // Reset initialization flag and reinitialize the tab
  payrollAuditTabInitialized = false;
  initializePayrollAuditTab();

  console.log('✅ Original view restored');
}

// REMOVED: Duplicate restoreOriginalView function

// NON-BLOCKING PROCESSING WITH YIELDING CONTROL
async function processWithRealTimeProgress(currentPdf, previousPdf) {
  return new Promise(async (resolve, reject) => {
    try {
      // Reset counters
      employeeCount = 0;
      changeCount = 0;

      // Reset all steps to ensure proper synchronization
      resetAllStepsToPending();

      // Step 1: Start processing current payroll
      updateProcessingStepDetailed(1, 'active', 'Processing...');
      updateProgress(10, 'Extracting current payroll data...');
      updateCurrentOperation('Extracting Current Payroll', 'Processing current period payroll data...');

      // Real progress updates will come from backend

      // Step 2: Complete current, start previous
      updateProcessingStepDetailed(1, 'completed', 'Completed');
      updateProcessingStepDetailed(2, 'active', 'Processing...');
      updateProgress(35, 'Extracting previous payroll data...');
      updateCurrentOperation('Extracting Previous Payroll', 'Processing previous period payroll data...');

      // Real progress updates will come from backend

      // Step 3: Start comparison
      updateProcessingStepDetailed(2, 'completed', 'Completed');
      updateProcessingStepDetailed(3, 'active', 'Processing...');
      updateProgress(60, 'Comparing payroll periods...');
      updateCurrentOperation('Comparing Payroll Periods', 'Analyzing changes and discrepancies...');

      // NON-BLOCKING backend call with timeout and yielding
      const result = await callBackendNonBlocking(() =>
        window.api.comparePayrolls(currentPdf, previousPdf)
      );

      if (result.success) {
        // Update stats with real data NON-BLOCKING
        const summary = result.summary || {};
        const totalEmployees = summary.total_employees || 0;
        const totalChanges = summary.total_changes || 0;

        // Real stats updates will come from backend

        // Step 3: Comparison complete
        updateProcessingStepDetailed(3, 'completed', 'Completed');
        updateProgress(80, 'Comparison completed successfully');
        updateCurrentOperation('Comparison Complete', 'Payroll comparison finished successfully');

        // Final yield before resolving
        await yieldToEventLoop();

        resolve(result);
      } else {
        throw new Error(result.error);
      }

    } catch (error) {
      reject(error);
    }
  });
}

async function generateReportsWithProgress(reportData, options) {
  return new Promise(async (resolve, reject) => {
    try {
      // Step 4: Start report generation (80% -> 100%)
      updateProcessingStepDetailed(4, 'active', 'Processing...');
      updateProgress(85, 'Generating Excel reports...');
      updateCurrentOperation('Generating Reports', 'Creating Excel, PDF, and Word reports...');

      await new Promise(resolve => setTimeout(resolve, 800));

      updateProgress(90, 'Generating PDF reports...');
      await new Promise(resolve => setTimeout(resolve, 600));

      updateProgress(95, 'Generating Word reports...');
      await new Promise(resolve => setTimeout(resolve, 400));

      // Call the actual backend API
      const result = await window.api.generateReport(reportData, options);

      if (result.success) {
        updateProgress(100, 'All reports generated successfully!');
        updateProcessingStepDetailed(4, 'completed', 'Completed');
        updateCurrentOperation('Reports Complete!', 'All reports have been generated successfully.');

        resolve(result);
      } else {
        throw new Error(result.error);
      }

    } catch (error) {
      reject(error);
    }
  });
}

// Real-time progress tracking
let employeeCount = 0;
let changeCount = 0;

function initializeProgressListener() {
  console.log('🎧 Initializing progress listeners...');

  // Clear any existing listeners first
  if (window.api && window.api.clearEnhancedListeners) {
    window.api.clearEnhancedListeners();
    console.log('🧹 Cleared existing enhanced listeners');
  }

  // Listen for enhanced progress updates (primary)
  if (window.api && window.api.onEnhancedProgressUpdate) {
    window.api.onEnhancedProgressUpdate((data) => {
      console.log(`📊 Enhanced Progress: [${data.type || data.phase}] ${data.percentage || 'N/A'}% - ${data.message}`);
      handleEnhancedProgressUpdate(data);
    });
    console.log('✅ Enhanced progress listener attached');
  }

  // Listen for backend progress updates (legacy)
  if (window.api && window.api.onBackendProgress) {
    window.api.onBackendProgress((progressData) => {
      handleBackendProgress(progressData);
    });
    console.log('✅ Backend progress listener attached');
  }

  // Listen for real-time extraction updates
  if (window.api && window.api.onRealTimeExtractionUpdate) {
    window.api.onRealTimeExtractionUpdate((updateData) => {
      handleRealTimeExtractionProgress(updateData);
    });
    console.log('✅ Real-time extraction listener attached');
  }

  // Listen for enhanced log updates (for processing logs)
  if (window.api && window.api.onEnhancedLogUpdate) {
    window.api.onEnhancedLogUpdate((logData) => {
      console.log('📝 Enhanced Log Update:', logData);
      updateProcessingLogs(logData.message, logData.type);
    });
    console.log('✅ Enhanced log listener attached');
  }
}

function handleEnhancedProgressUpdate(data) {
  console.log('🚀 Enhanced Progress Update:', data);

  // Check if this is pre-reporting data
  if (data.type === 'pre_reporting_data_ready') {
    console.log('📊 Pre-reporting data received from backend:', data);
    
    // Emit event for pre-reporting system
    if (window.appEvents) {
      window.appEvents.emit('pre-reporting-data-ready', data);
      updateProcessingLogs('Pre-reporting interface ready with change data', 'success');
    }
    
    // Explicitly trigger transition to pre-reporting phase
    updateUIPhase('PRE_REPORTING', 'Pre-reporting data ready for review', 64);
    console.log('📋 Activated pre-reporting phase');
    return; // Skip regular progress handling
  }

  // Regular progress updates
  if (data.message) {
    const logType = data.type === 'error' ? 'error' :
                   data.type === 'completion' ? 'success' : 'info';
    updateProcessingLogs(data.message, logType);
  }

  // ENHANCED REAL-TIME PROGRESS PANEL (A2) - COMPLETELY REPLACE OLD SYSTEM
  // Hide old progress panel and show enhanced version
  const oldProgressSection = document.getElementById('processing-progress-section');
  if (oldProgressSection) {
    oldProgressSection.style.display = 'none';
  }

  // Ensure enhanced progress panel exists and is visible
  const enhancedProgressPanel = document.getElementById('enhanced-progress-panel');
  if (enhancedProgressPanel) {
    enhancedProgressPanel.style.display = 'block';

    // Update enhanced progress bar with real-time data
    const progressFillEnhanced = document.getElementById('progress-fill-enhanced');
    const progressPercentageEnhanced = document.getElementById('progress-percentage-enhanced');
    const progressStatusEnhanced = document.getElementById('progress-status-enhanced');

    if (progressFillEnhanced && data.percentage !== undefined) {
      progressFillEnhanced.style.width = `${data.percentage}%`;
    }

    if (progressPercentageEnhanced && data.percentage !== undefined) {
      progressPercentageEnhanced.textContent = `${data.percentage}%`;
    }

    if (progressStatusEnhanced && data.message) {
      progressStatusEnhanced.textContent = data.message;
    }

    // Update phase information
    updateEnhancedPhaseInfo(data.phase, data.message);

    // Add real-time activity
    addRealTimeActivity(data.phase, data.message, data.percentage);
  }
  
  // Remove ALL old progress panels - we only use the enhanced version now
  document.querySelectorAll('.progress-panel, .extraction-panel, .extraction-progress, [id^="extraction"], [class*="extraction"]').forEach(panel => {
    if (panel && panel.id !== 'enhanced-progress-panel') {
      console.log(`Removing old progress panel: ${panel.id || 'unnamed panel'}`);
      if (panel.parentNode) {
        panel.parentNode.removeChild(panel);
      }
    }
  });
  
  // Update the phase indicators based on current phase and completion status
  if (data.phase && document.querySelector(`.phase-step[data-phase="${data.phase}"]`)) {
    const phaseStep = document.querySelector(`.phase-step[data-phase="${data.phase}"]`);
    
    // Mark the current phase as active
    document.querySelectorAll('.phase-step').forEach(step => {
      if (step !== phaseStep) {
        step.classList.remove('active');
      }
    });
    
    phaseStep.classList.add('active');
    
    // If phase is complete, mark it as completed
    if (data.phase_complete === true) {
      phaseStep.classList.add('completed');
      phaseStep.querySelector('.step-status').textContent = 'Completed';
      console.log(`🏁 Phase ${data.phase} marked as completed`);
      
      // Find and activate next phase if available
      const nextPhase = phaseStep.nextElementSibling;
      if (nextPhase && nextPhase.classList.contains('phase-step')) {
        nextPhase.classList.add('active');
        nextPhase.querySelector('.step-status').textContent = 'In Progress';
      }
    }
  }
  
  // Check if this is a final completion signal (100% in report generation phase)
  const isCompletionSignal = data.percentage === 100 && 
    ((data.phase === 'report-generation' && data.phase_complete === true) || 
     data.message.toLowerCase().includes('completed successfully'));
    
  if (isCompletionSignal) {
    console.log('🏁 Final process completion detected', data);
    // Handle process completion directly
    updateUIPhase('REPORT_GENERATION', data.message || 'Payroll audit completed successfully!', 100);
    
    // Log the completion with important details about reports
    updateProcessingLogs('Process complete. The system has generated both Comparison and Final reports.', 'success');
    updateProcessingLogs('Final Report contains only High and Moderate priority changes, filtering out routine bulk changes.', 'info');
    return; // Exit early as completion is now handled by the content switching manager
  }

  // Handle different progress update types
  if (data.type === 'extraction_progress') {
    updateExtractionProgress(data.percentage, data.message, data.employees_extracted || data.employee_count || 0);
  } else if (data.type === 'phase_progress') {
    // CRITICAL: Handle phase transitions from NEW phased process manager
    console.log('🔄 Phase progress update received from phased process manager:', data);

    // Extract phase from message if not in data.phase
    let currentPhase = data.phase;
    if (!currentPhase && data.message) {
      // Parse phase from messages like "EXTRACTION phase completed" or "Starting COMPARISON phase..."
      const phaseMatch = data.message.match(/(EXTRACTION|COMPARISON|PRE_AUDITING|TRACKER_FEEDING|TRACKER_LEARNING|AUTO_LEARNING|PRE_REPORTING|REPORT_GENERATION)/i);
      if (phaseMatch) {
        currentPhase = phaseMatch[1].toUpperCase();
        // Map TRACKER_LEARNING to AUTO_LEARNING for UI consistency
        if (currentPhase === 'TRACKER_LEARNING') {
          currentPhase = 'AUTO_LEARNING';
        }
        // Map COMPARISON to PRE_AUDITING for UI consistency
        if (currentPhase === 'COMPARISON') {
          currentPhase = 'PRE_AUDITING';
        }
      }
    }

    // Update UI phase if we have a valid phase
    if (currentPhase) {
      console.log(`🔄 Transitioning UI to phase: ${currentPhase}`);
      updateUIPhase(currentPhase, data.message, data.percentage);

      // CRITICAL: Additional trigger for PRE_REPORTING phase
      if (currentPhase === 'PRE_REPORTING') {
        console.log('🔄 PRE_REPORTING phase detected in phase_progress - ensuring UI loads');
        setTimeout(() => {
          loadPreReportingUIFromDatabase();
        }, 1500);
      }
    }

    // Update progress bar and current operation
    if (data.percentage !== undefined) {
      updateProgress(data.percentage, data.message);
    }
  } else if (data.type === 'phase_start') {
    // RELIABLE: Handle phase start notifications with state management
    console.log(`🚀 Phase starting: ${data.phase}`);

    if (window.phaseStateManager) {
      window.phaseStateManager.updatePhase(data.phase, {
        message: data.message,
        percentage: 0,
        status: 'starting'
      });
    } else {
      updateUIPhase(data.phase, data.message, 0);
    }

    if (window.addRealTimeActivity) {
      window.addRealTimeActivity('system', `Starting ${data.phase} phase`, 0, 'info');
    }

  } else if (data.type === 'phase_complete') {
    // RELIABLE: Handle phase completion notifications with state management
    console.log(`✅ Phase completed: ${data.phase}`, data);

    if (window.phaseStateManager) {
      window.phaseStateManager.updatePhase(data.phase, {
        message: data.message,
        percentage: 100,
        status: 'completed',
        completed: true,
        ...data
      });
    }

    if (window.addRealTimeActivity) {
      window.addRealTimeActivity('system', `${data.phase} phase completed`, data.percentage || 100, 'success');
    }

    // Update phase indicator to completed
    if (window.updatePhaseIndicator) {
      const phaseMap = {
        'EXTRACTION': 'extraction',
        'COMPARISON': 'comparison',
        'AUTO_LEARNING': 'auto-learning',
        'TRACKER_FEEDING': 'tracker-feeding',
        'PRE_REPORTING': 'pre-reporting',
        'REPORT_GENERATION': 'report-generation'
      };
      const uiPhase = phaseMap[data.phase] || data.phase.toLowerCase();
      window.updatePhaseIndicator(uiPhase, 'completed');
    }

    // Special handling for PRE_REPORTING completion after user interaction
    if (data.phase === 'PRE_REPORTING' && data.user_interaction_completed) {
      console.log('✅ Pre-reporting completed after user interaction - proceeding to report generation');
      // The workflow will automatically proceed to report generation
    }

  } else if (data.type === 'phase_waiting_user') {
    // PRODUCTION: Handle phase waiting for user interaction
    console.log(`⏳ Phase waiting for user: ${data.phase}`, data);

    if (window.phaseStateManager) {
      window.phaseStateManager.updatePhase(data.phase, {
        message: data.message,
        percentage: 80,
        status: 'waiting_for_user',
        waiting_for_user: true,
        ...data
      });
    }

    if (window.addRealTimeActivity) {
      window.addRealTimeActivity('system', `${data.phase} phase waiting for user interaction`, 80, 'warning');
    }

    // Update phase indicator to in-progress (waiting for user)
    if (window.updatePhaseIndicator) {
      const phaseMap = {
        'PRE_REPORTING': 'pre-reporting'
      };
      const uiPhase = phaseMap[data.phase] || data.phase.toLowerCase();
      window.updatePhaseIndicator(uiPhase, 'in-progress');
    }

    // Load interactive UI for PRE_REPORTING
    if (data.phase === 'PRE_REPORTING' && data.ready_for_review) {
      console.log('📋 Pre-reporting ready for user review - loading interactive UI');
      setTimeout(() => {
        loadPreReportingUIFromDatabase();
      }, 1000);
    }

  } else if (data.type === 'employee_progress') {
    // Handle employee-level progress updates
    console.log(`👤 Employee progress: ${data.current_employee}/${data.total_employees} - ${data.employee_name}`);

    if (window.addRealTimeActivity) {
      window.addRealTimeActivity('processing', data.message, data.percentage, 'info');
    }

    // Update progress with employee details
    updateProgress(data.percentage, data.message);

  } else if (data.type === 'enhanced_progress') {
    // Handle enhanced progress updates
    console.log(`📊 Enhanced progress: ${data.phase} - ${data.percentage}%`);
    updateProgress(data.percentage, data.message);

    if (window.addRealTimeActivity) {
      window.addRealTimeActivity(data.phase.toLowerCase(), data.message, data.percentage, 'info');
    }

  } else if (data.type === 'process_paused') {
    // Handle process pause notifications
    console.log('⏸️ Process paused by user');

    if (window.addRealTimeActivity) {
      window.addRealTimeActivity('paused', data.message, data.percentage, 'warning');
    }

    // Update UI to show paused state
    updateCurrentOperation('Process Paused', data.message);

  } else if (data.type === 'process_resumed') {
    // Handle process resume notifications
    console.log('▶️ Process resumed by user');

    if (window.addRealTimeActivity) {
      window.addRealTimeActivity('resumed', data.message, data.percentage, 'success');
    }

    // Update UI to show resumed state
    updateCurrentOperation('Process Resumed', data.message);
    updateCurrentOperation(currentPhase || 'Processing', data.message);

    // SIMPLIFIED: Direct UI phase transitions without content switching manager
    if (data.phase) {
      console.log(`🔄 Transitioning UI to phase: ${data.phase}`);
      updateUIPhase(data.phase, data.message, data.percentage);
    }

    // FIXED: Update step status based on CORRECTED phase sequence
    if (data.phase === 'EXTRACTION') {
      updateProcessingStepDetailed(1, 'active', 'Processing...');
    } else if (data.phase === 'COMPARISON') {
      updateProcessingStepDetailed(1, 'completed', 'Completed');
      updateProcessingStepDetailed(2, 'active', 'Processing...');
    } else if (data.phase === 'TRACKER_FEEDING') {
      updateProcessingStepDetailed(2, 'completed', 'Completed');
      updateProcessingStepDetailed(3, 'active', 'Processing...');
    } else if (data.phase === 'TRACKER_LEARNING') {
      updateProcessingStepDetailed(3, 'completed', 'Completed');
      updateProcessingStepDetailed(4, 'active', 'Processing...');
    } else if (data.phase === 'PRE_REPORTING') {
      updateProcessingStepDetailed(4, 'completed', 'Completed');
      updateProcessingStepDetailed(5, 'active', 'Processing...');
    } else if (data.phase === 'REPORT_GENERATION') {
      updateProcessingStepDetailed(5, 'completed', 'Completed');
      updateProcessingStepDetailed(6, 'active', 'Processing...');
    }
  } else if (data.type === 'completion') {
    // Mark all steps as completed
    for (let i = 1; i <= 5; i++) {
      updateProcessingStepDetailed(i, 'completed', 'Completed');
    }
    updateProgress(100, 'Processing completed successfully!');
    updateCurrentOperation('Complete', 'All processing phases completed successfully');
  } else if (data.type === 'error') {
    // Mark current step as error
    const currentStep = getCurrentActiveStep();
    updateProcessingStepDetailed(currentStep, 'error', 'Error');
    updateCurrentOperation('Error', data.message);
  }

  // Update employee and change counts if available
  if (data.employees_processed !== undefined || data.changes_detected !== undefined) {
    const currentEmployees = data.employees_processed || employeeCount;
    const currentChanges = data.changes_detected || changeCount;

    updateProcessingStats(currentEmployees, currentChanges);

    // Calculate real-time processing speed and time estimates
    const processingSpeed = calculateProcessingSpeed(currentEmployees);
    const estimatedTime = estimateRemainingTime(data.percentage);

    // Update enhanced stats with real calculations
    updateEnhancedStats(
      currentEmployees,
      currentChanges,
      processingSpeed,
      estimatedTime
    );

    // Update global counters
    employeeCount = currentEmployees;
    changeCount = currentChanges;
  }
}

function getCurrentActiveStep() {
  // Find the currently active step by looking for the 'active' class
  const steps = document.querySelectorAll('.processing-step');
  for (let i = 0; i < steps.length; i++) {
    if (steps[i].classList.contains('active')) {
      return i + 1; // Return 1-based step number
    }
  }
  return 1; // Default to first step if none found
}

// Enhanced Progress Panel Functions (A2)
// Note: processingStartTime and processingTimer are already declared above
let activityCounter = 0;

function updateEnhancedPhaseInfo(phase, message) {
  const phaseIcon = document.getElementById('current-phase-icon');
  const phaseTitle = document.getElementById('current-phase-title');
  const phaseDescription = document.getElementById('current-phase-description');

  if (!phaseIcon || !phaseTitle || !phaseDescription) return;

  // UPDATED PHASE CONFIGURATION - Zero Data Loss Architecture
  const phaseConfig = {
    'EXTRACTION': {
      icon: 'fas fa-file-upload',
      title: 'Data Extraction Phase',
      description: 'Extracting payroll data from PDF files...'
    },
    'COMPARISON': {
      icon: 'fas fa-balance-scale',
      title: 'Comparison Phase',
      description: 'Comparing current and previous payroll data...'
    },
    'AUTO_LEARNING': {
      icon: 'fas fa-brain',
      title: 'Auto Learning Phase',
      description: 'Learning new patterns and auto-approving items...'
    },
    'TRACKER_FEEDING': {
      icon: 'fas fa-database',
      title: 'Tracker Feeding Phase',
      description: 'Feeding NEW items to tracker tables...'
    },
    'PRE_REPORTING': {
      icon: 'fas fa-filter',
      title: 'Pre-Reporting Phase',
      description: 'Categorizing changes for user selection...'
    },
    'REPORT_GENERATION': {
      icon: 'fas fa-file-alt',
      title: 'Report Generation Phase',
      description: 'Generating Word, PDF, and Excel reports...'
    }
  };

  const config = phaseConfig[phase] || phaseConfig['EXTRACTION'];

  phaseIcon.innerHTML = `<i class="${config.icon}"></i>`;
  phaseTitle.textContent = config.title;
  phaseDescription.textContent = message || config.description;

  // Update phase indicators
  updatePhaseIndicators(phase);
}

function updatePhaseIndicators(currentPhase) {
  console.log(`Updating phase indicators for phase: ${currentPhase}`);
  
  // FIXED: Consistent phase mapping aligned with backend and UI
  const phaseOrder = ['extraction', 'comparison', 'auto-learning', 'tracker-feeding', 'pre-reporting', 'report-generation'];
  const phaseMap = {
    'EXTRACTION': 'extraction',
    'COMPARISON': 'comparison',
    'AUTO_LEARNING': 'auto-learning',
    'TRACKER_FEEDING': 'tracker-feeding',
    'PRE_REPORTING': 'pre-reporting',
    'REPORT_GENERATION': 'report-generation'
  };

  const currentPhaseKey = phaseMap[currentPhase];
  console.log(`Current phase key: ${currentPhaseKey}, from phase: ${currentPhase}`);
  
  // Default to first phase if not found (important fix for extraction!)
  const currentIndex = currentPhaseKey ? phaseOrder.indexOf(currentPhaseKey) : 0;
  console.log(`Current phase index: ${currentIndex}`);
  
  // First make sure all phase indicators are displayed
  document.querySelectorAll('.phase-indicator').forEach(indicator => {
    indicator.style.display = 'flex';
  });
  
  // Get extraction phase element specifically
  const extractionIndicator = document.querySelector('.phase-indicator[data-phase="extraction"]');
  if (extractionIndicator) {
    console.log('Extraction indicator found, ensuring it\'s visible');
    extractionIndicator.style.display = 'flex';
  } else {
    console.warn('⚠️ Extraction indicator not found in DOM');
  }
  
  // Update all phase indicators
  const allIndicators = document.querySelectorAll('.phase-indicator');
  console.log(`Found ${allIndicators.length} phase indicators to update`);
  
  allIndicators.forEach(indicator => {
    const phase = indicator.getAttribute('data-phase');
    const phaseIndex = phaseOrder.indexOf(phase);
    const statusElement = indicator.querySelector('.phase-status');
    
    indicator.classList.remove('active', 'completed', 'pending');
    
    if (phaseIndex < currentIndex) {
      indicator.classList.add('completed');
      if (statusElement) statusElement.textContent = 'Completed';
    } else if (phaseIndex === currentIndex) {
      indicator.classList.add('active');
      if (statusElement) statusElement.textContent = 'Processing';
    } else {
      indicator.classList.add('pending');
      if (statusElement) statusElement.textContent = 'Pending';
    }
    
    console.log(`Updated indicator ${phase} with status: ${statusElement ? statusElement.textContent : 'unknown'}`);
  });
  
  // Handle special case for extraction phase
  if (currentPhase === 'EXTRACTION') {
    if (extractionIndicator) {
      extractionIndicator.classList.remove('pending');
      extractionIndicator.classList.add('active');
      const statusElement = extractionIndicator.querySelector('.phase-status');
      if (statusElement) statusElement.textContent = 'Processing';
      console.log('Explicitly activated extraction phase indicator');
      
      // Add specific debugging for extraction phase
      console.log('Extraction phase indicator properties:');
      console.log(' - Display:', extractionIndicator.style.display);
      console.log(' - Classes:', extractionIndicator.className);
      console.log(' - Visibility:', window.getComputedStyle(extractionIndicator).visibility);
      console.log(' - Opacity:', window.getComputedStyle(extractionIndicator).opacity);
    }
  }
  
  console.log(`🎯 Phase indicators updated for: ${currentPhase} (${currentPhaseKey})`);
}

function addRealTimeActivity(phase, message, percentage, activityType = null) {
  const activityList = document.getElementById('activity-list');
  if (!activityList || !message) return;

  activityCounter++;
  const timestamp = new Date().toLocaleTimeString();

  // PRODUCTION FIX: Enhanced activity type detection
  if (!activityType) {
    if (message.toLowerCase().includes('error') || message.toLowerCase().includes('failed') || message.toLowerCase().includes('timeout')) {
      activityType = 'error';
    } else if (message.toLowerCase().includes('completed') || message.toLowerCase().includes('success') || message.toLowerCase().includes('complete')) {
      activityType = 'success';
    } else if (message.toLowerCase().includes('warning') || message.toLowerCase().includes('detected')) {
      activityType = 'warning';
    } else if (message.toLowerCase().includes('processing') || message.toLowerCase().includes('extracting')) {
      activityType = 'processing';
    } else {
      activityType = 'info';
    }
  }

  const activityItem = document.createElement('div');
  activityItem.className = 'activity-item';
  activityItem.innerHTML = `
    <div class="activity-icon ${activityType}">
      <i class="fas fa-${activityType === 'info' ? 'info' : activityType === 'success' ? 'check' : activityType === 'warning' ? 'exclamation' : 'times'}"></i>
    </div>
    <div class="activity-content">
      <div class="activity-message">${message}</div>
      <div class="activity-time">${timestamp} ${percentage !== undefined ? `(${percentage}%)` : ''}</div>
    </div>
  `;

  // Add to top of list
  activityList.insertBefore(activityItem, activityList.firstChild);

  // Keep only last 50 activities
  while (activityList.children.length > 50) {
    activityList.removeChild(activityList.lastChild);
  }

  // Auto-scroll to top
  activityList.scrollTop = 0;
}

function startProcessingTimer() {
  processingStartTime = Date.now();
  const timerDisplay = document.getElementById('timer-display');

  if (processingTimer) {
    clearInterval(processingTimer);
  }

  processingTimer = setInterval(() => {
    if (!processingStartTime) return;

    const elapsed = Date.now() - processingStartTime;
    const minutes = Math.floor(elapsed / 60000);
    const seconds = Math.floor((elapsed % 60000) / 1000);

    if (timerDisplay) {
      timerDisplay.textContent = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
    }
  }, 1000);
}

function stopProcessingTimer() {
  if (processingTimer) {
    clearInterval(processingTimer);
    processingTimer = null;
  }
  processingStartTime = null;
}

function stopRunningProcess() {
  if (runningProcessId) {
    window.api.stopProcess(runningProcessId);
    runningProcessId = null;
    animatePayrollProcessing(false);
    return true;
  }
  return false;
}

// REMOVED: Duplicate functions - using the enhanced versions below

function clearActivityFeed() {
  const activityList = document.getElementById('activity-list');
  if (activityList) {
    activityList.innerHTML = '';
    activityCounter = 0;
  }
}

// Removed duplicate functions - using the ones defined above

function initializeEnhancedProgressPanel() {
  console.log('🔧 Initializing enhanced progress panel');
  
  // Make sure the enhanced panel is hidden at startup
  const enhancedPanel = document.getElementById('enhanced-progress-panel');
  if (enhancedPanel) {
    enhancedPanel.style.display = 'none';
  }
  
  // Ensure file selection panel (phase-1) is shown at startup
  const fileSelectionPanel = document.getElementById('phase-1-content');
  if (fileSelectionPanel) {
    fileSelectionPanel.classList.add('active');
    fileSelectionPanel.style.display = 'block';
  }
  
  // Initialize event listeners for enhanced progress panel
  const pauseBtn = document.getElementById('pause-processing-btn');
  const stopBtn = document.getElementById('stop-processing-btn');
  const clearActivityBtn = document.getElementById('clear-activity-btn');

  if (pauseBtn) {
    pauseBtn.addEventListener('click', () => {
      console.log('🔄 Pause processing requested');
      addRealTimeActivity('SYSTEM', 'Processing paused by user', null);
      // Add pause logic here
    });
  }

  if (stopBtn) {
    stopBtn.addEventListener('click', () => {
      console.log('🛑 Stop processing requested');
      addRealTimeActivity('SYSTEM', 'Processing stopped by user', null);
      stopProcessingTimer();
      // Add stop logic here
    });
  }

  if (clearActivityBtn) {
    clearActivityBtn.addEventListener('click', clearActivityFeed);
  }
}

function showEnhancedProgressPanel() {
  console.log('🔄 Showing enhanced progress panel');
  
  // Get references to all relevant panels
  const enhancedPanel = document.getElementById('enhanced-progress-panel');
  const oldPanel = document.getElementById('processing-progress-section');
  const fileSelectionPanel = document.getElementById('phase-1-content');
  const contentSwitchingContainer = document.getElementById('content-switching-container');

  // Hide old panel completely
  if (oldPanel) {
    oldPanel.style.display = 'none';
  }
  
  // Hide the file selection panel but keep its parent container
  if (fileSelectionPanel) {
    fileSelectionPanel.classList.remove('active');
    fileSelectionPanel.style.display = 'none';
  }
  
  // Make sure the content switching container is visible
  if (contentSwitchingContainer) {
    contentSwitchingContainer.style.display = 'block';
  }

  // Show enhanced panel
  if (enhancedPanel) {
    enhancedPanel.style.display = 'block';
    startProcessingTimer();
    addRealTimeActivity('SYSTEM', 'Enhanced payroll audit process started', 0);

    // Initialize phase indicators
    initializePhaseIndicators();

    // Initialize real stats tracking
    initializeRealStatsTracking();
  }
}

// Real stats tracking initialization
function initializeRealStatsTracking() {
  // Reset counters to zero
  employeeCount = 0;
  changeCount = 0;

  // Update display to show zero initially
  updateProcessingStatsImmediate(0, 0);

  // Initialize processing speed tracking
  processingStartTime = Date.now();

  console.log('📊 Real stats tracking initialized');
}

// Enhanced stats update function with real calculations
function updateEnhancedStats(employeesProcessed, changesDetected, processingSpeed, estimatedTime) {
  requestAnimationFrame(() => {
    try {
      // Update all enhanced stat elements
      const elements = {
        employees: document.getElementById('employees-processed-enhanced'),
        changes: document.getElementById('changes-detected-enhanced'),
        speed: document.getElementById('processing-speed'),
        time: document.getElementById('estimated-time')
      };

      // Update employees with animation
      if (elements.employees && elements.employees.textContent !== employeesProcessed.toString()) {
        elements.employees.textContent = employeesProcessed;
        elements.employees.style.transform = 'scale(1.1)';
        setTimeout(() => { elements.employees.style.transform = 'scale(1)'; }, 200);
      }

      // Update changes with animation
      if (elements.changes && elements.changes.textContent !== changesDetected.toString()) {
        elements.changes.textContent = changesDetected;
        elements.changes.style.transform = 'scale(1.1)';
        setTimeout(() => { elements.changes.style.transform = 'scale(1)'; }, 200);
      }

      // Update processing speed
      if (elements.speed) {
        elements.speed.textContent = processingSpeed;
      }

      // Update estimated time
      if (elements.time) {
        elements.time.textContent = estimatedTime;
      }

      console.log('📊 Enhanced stats updated:', {
        employees: employeesProcessed,
        changes: changesDetected,
        speed: processingSpeed,
        time: estimatedTime
      });

    } catch (error) {
      console.warn('Error updating enhanced stats:', error);
    }
  });
}

// Initialize phase indicators to pending state - OPTIMIZED
function initializePhaseIndicators() {
  // UPDATED: Align with new phased process manager sequence
  const phases = ['extraction', 'comparison', 'auto-learning', 'tracker-feeding', 'pre-reporting', 'report-generation'];

  phases.forEach(phase => {
    updatePhaseIndicator(phase, 'pending');
  });

  console.log('🎯 Phase indicators initialized with optimized sequence');
}

// Update phase indicator status
function updatePhaseIndicator(phaseName, status) {
  const phaseElement = document.getElementById(`phase-${phaseName}`);
  if (!phaseElement) return;

  // Remove all status classes
  phaseElement.classList.remove('pending', 'active', 'completed');

  // Add new status class
  phaseElement.classList.add(status);

  // Update status text
  const statusElement = phaseElement.querySelector('.phase-status');
  if (statusElement) {
    switch(status) {
      case 'pending':
        statusElement.textContent = 'Pending';
        break;
      case 'active':
        statusElement.textContent = 'Processing';
        break;
      case 'completed':
        statusElement.textContent = 'Completed';
        break;
    }
  }

  console.log(`🎯 Phase ${phaseName} updated to ${status}`);
}

function hideEnhancedProgressPanel() {
  console.log('🔒 Hiding enhanced progress panel');
  
  const enhancedPanel = document.getElementById('enhanced-progress-panel');
  if (enhancedPanel) {
    enhancedPanel.style.display = 'none';
  }
  
  // Restore the file selection panel
  const fileSelectionPanel = document.getElementById('phase-1-content');
  if (fileSelectionPanel) {
    fileSelectionPanel.classList.add('active');
    fileSelectionPanel.style.display = 'block';
  }
  
  stopProcessingTimer();
}

function handleBackendProgress(progressData) {
  console.log('🔄 Real Backend Progress:', progressData);

  // Parse PROGRESS_UPDATE messages from backend
  if (progressData.message && progressData.message.includes('PROGRESS_UPDATE:')) {
    const progressMatch = progressData.message.match(/PROGRESS_UPDATE:\s*(\d+\.?\d*)%/);
    if (progressMatch) {
      const percentage = parseFloat(progressMatch[1]);
      console.log(`📊 Real progress: ${percentage}%`);

      // Update progress bar with REAL backend percentage
      updateProgress(percentage, `Processing... ${percentage.toFixed(1)}%`);

      // Update steps based on REAL progress
      if (percentage <= 50) {
        updateProcessingStepDetailed(1, 'active', 'Processing...');
        updateProcessingStepDetailed(2, 'pending', 'Pending');
        updateProcessingStepDetailed(3, 'pending', 'Pending');
        updateProcessingStepDetailed(4, 'pending', 'Pending');
        updateCurrentOperation('Extracting Current Payroll', `${percentage.toFixed(1)}% complete`);
      } else if (percentage <= 90) {
        updateProcessingStepDetailed(1, 'completed', 'Completed');
        updateProcessingStepDetailed(2, 'active', 'Processing...');
        updateProcessingStepDetailed(3, 'pending', 'Pending');
        updateProcessingStepDetailed(4, 'pending', 'Pending');
        updateCurrentOperation('Extracting Previous Payroll', `${percentage.toFixed(1)}% complete`);
      } else if (percentage < 100) {
        updateProcessingStepDetailed(1, 'completed', 'Completed');
        updateProcessingStepDetailed(2, 'completed', 'Completed');
        updateProcessingStepDetailed(3, 'active', 'Processing...');
        updateProcessingStepDetailed(4, 'pending', 'Pending');
        updateCurrentOperation('Comparing Payroll Periods', `${percentage.toFixed(1)}% complete`);
      } else {
        updateProcessingStepDetailed(1, 'completed', 'Completed');
        updateProcessingStepDetailed(2, 'completed', 'Completed');
        updateProcessingStepDetailed(3, 'completed', 'Completed');
        updateProcessingStepDetailed(4, 'active', 'Processing...');
        updateCurrentOperation('Generating Reports', 'Finalizing...');
      }
    }
  }

  // Parse employee processing updates
  if (progressData.message && progressData.message.includes('Employee No.:')) {
    employeeCount++;
    updateProcessingStats(employeeCount, changeCount);
    console.log(`👤 Employees processed: ${employeeCount}`);
  }

  // Parse changes detected
  if (progressData.message && progressData.message.includes('changes detected')) {
    const changesMatch = progressData.message.match(/(\d+)\s+changes detected/);
    if (changesMatch) {
      changeCount = parseInt(changesMatch[1]);
      updateProcessingStats(employeeCount, changeCount);
      console.log(`🔄 Changes detected: ${changeCount}`);
    }
  }

  // Parse batch processing updates
  if (progressData.message && progressData.message.includes('Processed') && progressData.message.includes('pages')) {
    const batchMatch = progressData.message.match(/Processed (\d+)\/(\d+) pages/);
    if (batchMatch) {
      const [, current, total] = batchMatch;
      const percentage = (parseInt(current) / parseInt(total)) * 100;
      updateProgress(percentage, `Processed ${current}/${total} pages`);
      console.log(`📄 Batch progress: ${current}/${total} pages (${percentage.toFixed(1)}%)`);
    }
  }
}

function handleRealTimeExtractionProgress(updateData) {
  console.log('🔄 Real-time Extraction Progress:', updateData);

  // PRODUCTION FIX: Enhanced real-time update handling with better UI synchronization
  switch (updateData.type) {
    case 'pdf_pages':
    case 'pdf_info':
      // Update initial PDF information
      if (updateData.total_pages) {
        updateCurrentOperation('PDF Analysis', `PDF contains ${updateData.total_pages} pages`);
      }
      if (updateData.percentage !== undefined) {
        updateProgress(updateData.percentage, updateData.message);
      }
      addRealTimeActivity('extraction', updateData.message, updateData.percentage);
      break;

    case 'extraction_start':
      updateCurrentOperation('Starting Extraction', updateData.message);
      updateProgress(updateData.percentage || 15, updateData.message);
      addRealTimeActivity('extraction', updateData.message, updateData.percentage);
      break;

    case 'batch_progress':
    case 'batch_info':
      // Update batch processing progress
      updateCurrentOperation('Batch Processing', updateData.message);
      if (updateData.percentage !== undefined) {
        updateProgress(updateData.percentage, updateData.message);
      }
      addRealTimeActivity('extraction', updateData.message, updateData.percentage);
      break;

    case 'page_processed':
      // PRODUCTION FIX: Real-time employee count updates
      if (updateData.success && updateData.employees_extracted !== undefined) {
        employeeCount = updateData.employees_extracted;
        updateProcessingStats(employeeCount, changeCount);
      }

      if (updateData.percentage !== undefined) {
        updateProgress(updateData.percentage, updateData.message);
      }

      // Add activity for successful page processing
      if (updateData.success) {
        addRealTimeActivity('extraction', updateData.message, updateData.percentage);
      }
      break;

    case 'batch_completed':
      // Update total employee count from batch completion
      if (updateData.total_employees !== undefined) {
        employeeCount = updateData.total_employees;
        updateProcessingStats(employeeCount, changeCount);
      }

      if (updateData.percentage !== undefined) {
        updateProgress(updateData.percentage, updateData.message);
      }
      addRealTimeActivity('extraction', updateData.message, updateData.percentage);
      break;

    case 'comparison_start':
      updateCurrentOperation('Starting Payroll Comparison', updateData.message);
      updateProgress(updateData.percentage || 0, updateData.message);
      resetAllStepsToPending();
      break;

    case 'extraction_progress':
      // Update progress bar
      if (updateData.percentage !== undefined) {
        updateProgress(updateData.percentage, updateData.message);
      }

      // Update current operation
      updateCurrentOperation('Processing Payroll Data', updateData.message);

      // Update steps based on current step
      switch (updateData.current_step) {
        case 'extract_current':
          updateProcessingStepDetailed(1, 'active', 'Processing...');
          break;
        case 'extract_current_complete':
          updateProcessingStepDetailed(1, 'completed', 'Completed');
          break;
        case 'extract_previous':
          updateProcessingStepDetailed(2, 'active', 'Processing...');
          break;
        case 'extract_previous_complete':
          updateProcessingStepDetailed(2, 'completed', 'Completed');
          break;
        case 'comparison':
          updateProcessingStepDetailed(3, 'active', 'Processing...');
          break;
        case 'comparison_complete':
          updateProcessingStepDetailed(3, 'completed', 'Completed');
          updateProcessingStepDetailed(4, 'active', 'Processing...');
          // CRITICAL: Transition to tracker feeding phase after comparison completes
          console.log('🔄 Comparison complete - transitioning to TRACKER_FEEDING phase');
          updateUIPhase('TRACKER_FEEDING', 'Starting tracker feeding...', 40);
          break;
      }
      break;

    case 'extraction_error':
    case 'page_error':
    case 'page_timeout':
      updateCurrentOperation('Error Occurred', updateData.message);
      addRealTimeActivity('extraction', updateData.message, updateData.percentage, 'error');

      // Mark current step as error
      const currentStep = getCurrentActiveStep();
      updateProcessingStepDetailed(currentStep, 'error', 'Error');
      break;

    case 'section_start':
      updateCurrentOperation(`Processing ${updateData.section}`, updateData.message);
      break;

    case 'new_item_found':
    case 'new_item_discovered':
      // Update stats if we have item data
      if (updateData.item || updateData.discovery) {
        employeeCount++;
        updateProcessingStats(employeeCount, changeCount);
      }
      break;

    case 'extraction_complete':
      updateCurrentOperation('Extraction Complete', updateData.message);
      updateProgress(20, updateData.message); // FIXED: Don't jump to 100% - extraction is only 20%
      addRealTimeActivity('extraction', updateData.message, 20);
      // FIXED: Don't prematurely transition phases - let the backend control phase transitions
      console.log('✅ Extraction complete - waiting for backend to start comparison phase');
      break;

    default:
      // Handle generic progress updates
      if (updateData.message) {
        updateCurrentOperation('Processing...', updateData.message);
      }
      if (updateData.percentage !== undefined) {
        updateProgress(updateData.percentage, updateData.message || 'Processing...');
      }
      break;
  }
}

function updateProcessingStep(stepIndex, message) {
  // Legacy function - now redirects to detailed version
  updateProcessingStepDetailed(stepIndex + 1, 'active', 'Processing...');
  updateCurrentOperation(message, 'Please wait while we process your data...');
}

function checkAuditButtonState() {
  const auditBtn = document.getElementById('start-payroll-audit');
  const signatureNameEl = document.getElementById('signature-name');
  const signatureDesignationEl = document.getElementById('signature-designation');

  if (!auditBtn) {
    console.log('❌ Start audit button not found');
    return;
  }

  const signatureName = signatureNameEl ? signatureNameEl.value.trim() : '';
  const signatureDesignation = signatureDesignationEl ? signatureDesignationEl.value.trim() : '';

  // Simple check: all 4 requirements must be met
  const hasCurrentPdf = !!auditCurrentPdfPath;
  const hasPreviousPdf = !!auditPreviousPdfPath;
  const hasName = signatureName.length > 0;
  const hasDesignation = signatureDesignation.length > 0;

  const allRequirementsMet = hasCurrentPdf && hasPreviousPdf && hasName && hasDesignation;

  console.log('🔍 Button state check:');
  console.log('  Current PDF:', hasCurrentPdf);
  console.log('  Previous PDF:', hasPreviousPdf);
  console.log('  Name:', hasName);
  console.log('  Designation:', hasDesignation);
  console.log('  All met:', allRequirementsMet);

  // Enable/disable button
  auditBtn.disabled = !allRequirementsMet;

  if (allRequirementsMet) {
    auditBtn.style.opacity = '1';
    auditBtn.style.cursor = 'pointer';
    auditBtn.classList.remove('disabled');
    auditBtn.style.backgroundColor = '#007bff';
    auditBtn.style.color = 'white';
    console.log('✅ Button ENABLED');
  } else {
    auditBtn.style.opacity = '0.6';
    auditBtn.style.cursor = 'not-allowed';
    auditBtn.classList.add('disabled');
    auditBtn.style.backgroundColor = '#6c757d';
    auditBtn.style.color = '#fff';
    console.log('❌ Button DISABLED');
  }

  // Remove any user guide text
  removeUserGuideText();
}

function removeUserGuideText() {
  // Remove any existing requirements status or user guide text
  const statusEl = document.getElementById('requirements-status');
  if (statusEl) {
    statusEl.remove();
  }

  // Remove any other user guide elements
  const userGuides = document.querySelectorAll('.user-guide, .requirements-text, .instruction-text');
  userGuides.forEach(el => el.remove());
}

// Report Manager functionality
function initializeReportManagerTab() {
  console.log('Initializing Report Manager tab...');

  // Load saved reports
  loadSavedReports();

  // Initialize event listeners
  setupReportManagerEventListeners();
}

// Setup event listeners for Report Manager
function setupReportManagerEventListeners() {
  // Refresh button
  const refreshBtn = document.getElementById('refresh-reports');
  if (refreshBtn) {
    refreshBtn.addEventListener('click', loadSavedReports);
  }

  // Export all button
  const exportAllBtn = document.getElementById('export-all-reports');
  if (exportAllBtn) {
    exportAllBtn.addEventListener('click', exportAllReports);
  }

  // Search functionality
  const searchInput = document.getElementById('report-search');
  if (searchInput) {
    searchInput.addEventListener('input', filterReports);
  }

  // Date filter
  const dateFilter = document.getElementById('date-filter');
  if (dateFilter) {
    dateFilter.addEventListener('change', filterReports);
  }

  // Report type filter
  const typeFilter = document.getElementById('report-type-filter');
  if (typeFilter) {
    typeFilter.addEventListener('change', filterReports);
  }

  // Section tabs
  const sectionTabs = document.querySelectorAll('.section-tab');
  sectionTabs.forEach(tab => {
    tab.addEventListener('click', (e) => {
      // Remove active class from all tabs
      sectionTabs.forEach(t => t.classList.remove('active'));

      // Add active class to clicked tab
      e.target.classList.add('active');

      // Filter reports by tab
      const tab = e.target.dataset.section;
      filterReportsByTab(tab);
    });
  });
}

// Load saved reports from backend
async function loadSavedReports() {
  try {
    showReportsLoading(true);

    console.log('Loading saved reports...');

    // Debug: Check Bank Adviser reports specifically
    try {
      const debugResponse = await window.api.debugBankAdviserReports();
      if (debugResponse.success) {
        console.log(`[DEBUG] Bank Adviser reports in database: ${debugResponse.totalBankAdviserReports}`);
        console.log('[DEBUG] All report categories:', debugResponse.allReportCategories);
        if (debugResponse.bankAdviserReports.length > 0) {
          console.log('[DEBUG] Bank Adviser reports:', debugResponse.bankAdviserReports);
        }

        // Auto-migrate if no Bank Adviser reports found but files exist
        if (debugResponse.totalBankAdviserReports === 0) {
          console.log('[AUTO-MIGRATE] No Bank Adviser reports in database, checking for files to migrate...');
          try {
            const migrationResult = await window.api.migrateBankAdviserReports();
            if (migrationResult.success && migrationResult.migratedCount > 0) {
              console.log(`[AUTO-MIGRATE] ✅ Migrated ${migrationResult.migratedCount} Bank Adviser reports`);
              showNotification(`Migrated ${migrationResult.migratedCount} Bank Adviser reports to database`, 'success');
            }
          } catch (migrationError) {
            console.warn('[AUTO-MIGRATE] Migration failed:', migrationError);
          }
        }
      }
    } catch (debugError) {
      console.warn('[DEBUG] Could not check Bank Adviser reports:', debugError);
    }

    const response = await window.api.getSavedReports();

    if (response.success && response.reports) {
      console.log(`[REPORTS] Loaded ${response.reports.length} total reports`);
      console.log('[REPORTS] Report categories:', Object.keys(response.tabs || {}));

      // FIXED: Remove JSON parsing - response.reports is already an array from database
      // The old JSON parsing logic was a remnant from the JSON-based system
      // Now we use database-only approach, so response.reports is already parsed data

      displayReports(response.reports);
      updateReportsStatistics(response.reports);
      updateTabStatistics(response.tabs || {});
    } else {
      console.warn('No saved reports found');
      showEmptyState();
    }
  } catch (error) {
    console.error('Error loading reports:', error);
    showNotification('Failed to load reports: ' + error.message, 'error');
    showEmptyState();
  } finally {
    showReportsLoading(false);
  }
}

// Display reports in table
function displayReports(reports) {
  const tableBody = document.getElementById('reports-table-body');
  const emptyState = document.getElementById('reports-empty-state');
  const tableContainer = document.querySelector('.reports-table-container');

  if (!tableBody) return;

  if (!reports || reports.length === 0) {
    showEmptyState();
    return;
  }

  // Hide empty state and show table
  if (emptyState) emptyState.style.display = 'none';
  if (tableContainer) tableContainer.style.display = 'block';

  // Generate table rows
  tableBody.innerHTML = reports.map(report => {
    // Handle different date formats from different report types
    let date;
    if (report.generated_at) {
      // PDF Sorter reports use generated_at string format
      date = new Date(report.generated_at);
    } else if (report.timestamp) {
      // Other reports may use timestamp (Unix or ISO)
      date = new Date(typeof report.timestamp === 'number' ? report.timestamp * 1000 : report.timestamp);
    } else {
      // Fallback to current date
      date = new Date();
    }

    // Validate date
    if (isNaN(date.getTime())) {
      date = new Date(); // Fallback to current date if parsing fails
    }

    const formattedDate = date.toLocaleDateString() + ' ' + date.toLocaleTimeString();

    // Format tab name for display
    const tabName = report.tab_folder || 'Unknown';
    const displayTabName = tabName.replace(/_/g, ' ').replace(/Reports?$/, '').trim();

    return `
      <tr data-report-id="${report.id}" data-tab="${tabName}">
        <td>
          <div class="report-date">
            <strong>${formattedDate}</strong>
            <small>${getRelativeTime(date)}</small>
          </div>
        </td>
        <td>
          <div class="report-period">
            ${report.source_tab === 'pdf_sorter' ?
              `<strong>${report.result?.sort_criteria || 'PDF Sorting'}</strong>
               <small>${report.result?.total_payslips || 0} payslips sorted</small>` :
              `<strong>${report.current_month || 'Unknown'} ${report.current_year || 'N/A'}</strong>
               <small>vs ${report.previous_month || 'Unknown'} ${report.previous_year || 'N/A'}</small>`
            }
          </div>
        </td>
        <td>
          <div class="report-author">
            <strong>${report.report_name || 'Unknown'}</strong>
            <small>${report.report_designation || 'N/A'}</small>
          </div>
        </td>
        <td>
          <div class="tab-info">
            <span class="tab-badge">${displayTabName}</span>
            <small><i class="fas fa-folder"></i> ${tabName}</small>
          </div>
        </td>
        <td>
          <span class="employee-count">${report.source_tab === 'pdf_sorter' ? (report.result?.total_payslips || 0) : (report.total_employees || 0)}</span>
        </td>
        <td>
          <span class="changes-count ${(report.total_changes || 0) > 0 ? 'has-changes' : ''}">${report.source_tab === 'pdf_sorter' ? 'N/A' : (report.total_changes || 0)}</span>
        </td>
        <td>
          <span class="report-type-badge">${report.source_tab === 'pdf_sorter' ? 'PDF_SORTING_REPORT' : (report.report_type || 'Traditional')}</span>
        </td>
        <td>
          <span class="status-badge completed">Completed</span>
        </td>
        <td>
          <div class="report-actions">
            <button class="btn-icon view" onclick="viewReport('${report.id}')" title="View Report">
              <i class="fas fa-eye"></i>
            </button>
            <button class="btn-icon download" onclick="downloadReport('${report.id}')" title="Download Report">
              <i class="fas fa-download"></i>
            </button>
            <button class="btn-icon delete" onclick="deleteReport('${report.id}')" title="Delete Report">
              <i class="fas fa-trash"></i>
            </button>
          </div>
        </td>
      </tr>
    `;
  }).join('');
}

// Update statistics
function updateReportsStatistics(reports) {
  const totalCount = document.getElementById('total-reports-count');
  const thisMonthCount = document.getElementById('this-month-reports');
  const totalEmployees = document.getElementById('total-employees-audited');
  const totalChanges = document.getElementById('total-changes-detected');

  if (!reports || reports.length === 0) {
    if (totalCount) totalCount.textContent = '0';
    if (thisMonthCount) thisMonthCount.textContent = '0';
    if (totalEmployees) totalEmployees.textContent = '0';
    if (totalChanges) totalChanges.textContent = '0';
    return;
  }

  const currentMonth = new Date().getMonth();
  const currentYear = new Date().getFullYear();

  const thisMonthReports = reports.filter(report => {
    // Handle different date formats
    let reportDate;
    if (report.generated_at) {
      reportDate = new Date(report.generated_at);
    } else if (report.timestamp) {
      reportDate = new Date(typeof report.timestamp === 'number' ? report.timestamp * 1000 : report.timestamp);
    } else {
      reportDate = new Date();
    }

    return reportDate.getMonth() === currentMonth && reportDate.getFullYear() === currentYear;
  });

  const totalEmployeesAudited = reports.reduce((sum, report) => {
    if (report.source_tab === 'pdf_sorter') {
      return sum + (report.result?.total_payslips || 0);
    }
    return sum + (report.total_employees || 0);
  }, 0);

  const totalChangesDetected = reports.reduce((sum, report) => {
    // PDF Sorter reports don't have changes, only payroll audit reports do
    if (report.source_tab === 'pdf_sorter') {
      return sum;
    }
    return sum + (report.total_changes || 0);
  }, 0);

  if (totalCount) totalCount.textContent = reports.length;
  if (thisMonthCount) thisMonthCount.textContent = thisMonthReports.length;
  if (totalEmployees) totalEmployees.textContent = totalEmployeesAudited;
  if (totalChanges) totalChanges.textContent = totalChangesDetected;
}

// Update tab statistics and badges
function updateTabStatistics(tabs) {
  // Update tab badges with counts
  Object.entries(tabs).forEach(([tabName, reports]) => {
    const tabButton = document.querySelector(`[data-section="${tabName}"]`);
    if (tabButton && reports.length > 0) {
      // Add count badge to tab button
      const existingBadge = tabButton.querySelector('.tab-count');
      if (existingBadge) {
        existingBadge.remove();
      }

      const countBadge = document.createElement('span');
      countBadge.className = 'tab-count';
      countBadge.textContent = reports.length;
      tabButton.appendChild(countBadge);
    }
  });

  console.log('Tab statistics updated:', Object.keys(tabs).map(t => `${t}: ${tabs[t].length}`).join(', '));
}

// Show empty state
function showEmptyState() {
  const emptyState = document.getElementById('reports-empty-state');
  const tableContainer = document.querySelector('.reports-table-container');

  if (emptyState) emptyState.style.display = 'block';
  if (tableContainer) tableContainer.style.display = 'none';

  // Reset statistics
  updateReportsStatistics([]);
}

// Show/hide loading state
function showReportsLoading(show) {
  const loadingState = document.getElementById('reports-loading');
  const tableContainer = document.querySelector('.reports-table-container');
  const emptyState = document.getElementById('reports-empty-state');

  if (loadingState) {
    loadingState.style.display = show ? 'block' : 'none';
  }

  if (!show) return;

  if (tableContainer) tableContainer.style.display = 'none';
  if (emptyState) emptyState.style.display = 'none';
}

// Get relative time string
function getRelativeTime(date) {
  const now = new Date();
  const diffMs = now - date;
  const diffMins = Math.floor(diffMs / 60000);
  const diffHours = Math.floor(diffMs / 3600000);
  const diffDays = Math.floor(diffMs / 86400000);

  if (diffMins < 1) return 'Just now';
  if (diffMins < 60) return `${diffMins} minutes ago`;
  if (diffHours < 24) return `${diffHours} hours ago`;
  if (diffDays < 7) return `${diffDays} days ago`;
  return date.toLocaleDateString();
}

// Filter reports by tab
function filterReportsByTab(tab) {
  const tableRows = document.querySelectorAll('.reports-table tbody tr');

  tableRows.forEach(row => {
    const rowTab = row.dataset.tab;

    if (tab === 'all' || rowTab === tab) {
      row.style.display = '';
    } else {
      row.style.display = 'none';
    }
  });

  // Update visible count
  const visibleRows = document.querySelectorAll('.reports-table tbody tr[style=""], .reports-table tbody tr:not([style])');
  console.log(`Filtered to tab: ${tab}, showing ${visibleRows.length} reports`);
}

// Filter reports based on search and filters
function filterReports() {
  const searchTerm = document.getElementById('report-search')?.value.toLowerCase() || '';
  const dateFilter = document.getElementById('date-filter')?.value || 'all';
  const typeFilter = document.getElementById('report-type-filter')?.value || 'all';

  const tableRows = document.querySelectorAll('.reports-table tbody tr');

  tableRows.forEach(row => {
    let showRow = true;

    // Search filter
    if (searchTerm) {
      const rowText = row.textContent.toLowerCase();
      if (!rowText.includes(searchTerm)) {
        showRow = false;
      }
    }

    // Date filter
    if (dateFilter !== 'all' && showRow) {
      const reportDate = new Date(row.querySelector('.report-date strong')?.textContent);
      const now = new Date();

      switch (dateFilter) {
        case 'today':
          showRow = reportDate.toDateString() === now.toDateString();
          break;
        case 'week':
          const weekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
          showRow = reportDate >= weekAgo;
          break;
        case 'month':
          showRow = reportDate.getMonth() === now.getMonth() && reportDate.getFullYear() === now.getFullYear();
          break;
        case 'quarter':
          const currentQuarter = Math.floor(now.getMonth() / 3);
          const reportQuarter = Math.floor(reportDate.getMonth() / 3);
          showRow = reportQuarter === currentQuarter && reportDate.getFullYear() === now.getFullYear();
          break;
        case 'year':
          showRow = reportDate.getFullYear() === now.getFullYear();
          break;
      }
    }

    // Type filter
    if (typeFilter !== 'all' && showRow) {
      const reportType = row.querySelector('.report-type-badge')?.textContent.toLowerCase();
      if (reportType !== typeFilter) {
        showRow = false;
      }
    }

    row.style.display = showRow ? '' : 'none';
  });

  // Update visible count
  const visibleRows = document.querySelectorAll('.reports-table tbody tr[style=""], .reports-table tbody tr:not([style])');
  console.log(`Filtered reports: showing ${visibleRows.length} reports`);
}

// Export all reports
async function exportAllReports() {
  try {
    showNotification('Exporting all reports...', 'info');
    const result = await window.api.exportAllReports();

    if (result.success) {
      showNotification('All reports exported successfully!', 'success');
    } else {
      throw new Error(result.error);
    }
  } catch (error) {
    console.error('Export error:', error);
    showNotification('Failed to export reports: ' + error.message, 'error');
  }
}

// Migrate Bank Adviser reports manually
async function migrateBankAdviserReports() {
  try {
    showNotification('Migrating Bank Adviser reports...', 'info');
    const result = await window.api.migrateBankAdviserReports();

    if (result.success) {
      showNotification(`Successfully migrated ${result.migratedCount} Bank Adviser reports!`, 'success');
      // Reload reports to show the migrated ones
      loadSavedReports();
    } else {
      throw new Error(result.error);
    }
  } catch (error) {
    console.error('Migration error:', error);
    showNotification('Failed to migrate reports: ' + error.message, 'error');
  }
}

// Make function globally available
window.migrateBankAdviserReports = migrateBankAdviserReports;

/**
 * Set up all UI event listeners for interactive elements
 * This handles button clicks, tabs, and other interactive elements
 */
function setupUIEventListeners() {
  console.log('⚙️ Setting up UI event listeners');

  // Header buttons - Settings and Help
  const settingsBtn = document.getElementById('settings-btn');
  const helpBtn = document.getElementById('help-btn');

  if (settingsBtn) {
    console.log('⚙️ Attaching event listener to settings button');
    settingsBtn.addEventListener('click', openSettingsModal);
  } else {
    console.error('❌ Settings button not found');
  }

  if (helpBtn) {
    console.log('❓ Attaching event listener to help button');
    helpBtn.addEventListener('click', openHelpModal);
  } else {
    console.error('❌ Help button not found');
  }

  // File selection buttons in payroll audit tab
  const browseCurrentBtn = document.getElementById('browse-current-payroll');
  const browsePreviousBtn = document.getElementById('browse-previous-payroll');

  if (browseCurrentBtn) {
    console.log('📎 Attaching event listener to current payroll button');
    // Remove any existing listeners to prevent duplicates
    browseCurrentBtn.replaceWith(browseCurrentBtn.cloneNode(true));
    const newCurrentBtn = document.getElementById('browse-current-payroll');
    newCurrentBtn.addEventListener('click', (e) => {
      e.preventDefault();
      e.stopPropagation();
      console.log('🔔 Current payroll button clicked');
      selectAuditFile('current');
    });
  } else {
    console.error('❌ Current payroll button not found');
  }

  if (browsePreviousBtn) {
    console.log('📎 Attaching event listener to previous payroll button');
    // Remove any existing listeners to prevent duplicates
    browsePreviousBtn.replaceWith(browsePreviousBtn.cloneNode(true));
    const newPreviousBtn = document.getElementById('browse-previous-payroll');
    newPreviousBtn.addEventListener('click', (e) => {
      e.preventDefault();
      e.stopPropagation();
      console.log('🔔 Previous payroll button clicked');
      selectAuditFile('previous');
    });
  } else {
    console.error('❌ Previous payroll button not found');
  }
  
  // CRITICAL FIX: Add event listener for Enhanced Payroll Audit button
  const startPayrollAuditBtn = document.getElementById('start-payroll-audit');
  if (startPayrollAuditBtn) {
    console.log('🚀 Attaching event listener to start payroll audit button');
    startPayrollAuditBtn.addEventListener('click', function() {
      console.log('🔔 Start payroll audit button clicked');
      // FIXED: Always use window.startPayrollAuditProcess to access global function
      if (typeof window.startPayrollAuditProcess === 'function') {
        console.log('✅ Found window.startPayrollAuditProcess, calling it now');
        window.startPayrollAuditProcess();
      } else {
        console.error('❌ window.startPayrollAuditProcess function not available');
        // Attempt fallback to local function as last resort
        if (typeof startPayrollAuditProcess === 'function') {
          console.log('⚠️ Using fallback to local startPayrollAuditProcess function');
          startPayrollAuditProcess();
        } else {
          console.error('❌❌ CRITICAL: Both global and local startPayrollAuditProcess functions unavailable');
        }
      }
    });
  } else {
    console.error('❌ Start payroll audit button not found');
  }

  // CRITICAL FIX: Signature input fields - Add event listeners to check button state
  const signatureNameInput = document.getElementById('signature-name');
  const signatureDesignationInput = document.getElementById('signature-designation');

  if (signatureNameInput) {
    console.log('📝 Attaching event listener to signature name input');
    signatureNameInput.addEventListener('input', checkAuditButtonState);
    signatureNameInput.addEventListener('change', checkAuditButtonState);
    signatureNameInput.addEventListener('keyup', checkAuditButtonState);
  } else {
    console.error('❌ Signature name input not found');
  }

  if (signatureDesignationInput) {
    console.log('📝 Attaching event listener to signature designation input');
    signatureDesignationInput.addEventListener('input', checkAuditButtonState);
    signatureDesignationInput.addEventListener('change', checkAuditButtonState);
    signatureDesignationInput.addEventListener('keyup', checkAuditButtonState);
  } else {
    console.error('❌ Signature designation input not found');
  }

  // Also check button state immediately when UI is set up
  setTimeout(() => {
    checkAuditButtonState();
    console.log('🔍 Initial button state check completed');
  }, 500);

  // Dictionary tab section tabs
  const dictionarySectionTabs = document.querySelectorAll('.dictionary-section-tab');
  dictionarySectionTabs.forEach(tab => {
    tab.addEventListener('click', function() {
      const section = this.getAttribute('data-section');
      const allContent = document.querySelectorAll('.dict-section-content');
      const allTabs = document.querySelectorAll('.dictionary-section-tab');
      
      // Deactivate all
      allContent.forEach(content => content.classList.remove('active'));
      allTabs.forEach(t => t.classList.remove('active'));
      
      // Activate selected
      this.classList.add('active');
      const targetContent = document.getElementById(section.toLowerCase().replace(' ', '-') + '-content');
      if (targetContent) {
        targetContent.classList.add('active');
      }
    });
  });
  
  // Set flag that UI is initialized
  uiInitialized = true;
  console.log('✅ UI event listeners successfully initialized');
}

// View specific report
async function viewReport(reportId) {
  try {
    console.log('Viewing report:', reportId);
    const result = await window.api.viewReport(reportId);

    if (result.success) {
      // Open report in default application
      showNotification('Opening report...', 'info');
    } else {
      throw new Error(result.error);
    }
  } catch (error) {
    console.error('View error:', error);
    showNotification('Failed to open report: ' + error.message, 'error');
  }
}

// Download specific report
async function downloadReport(reportId) {
  try {
    console.log('Downloading report:', reportId);
    const result = await window.api.downloadReportById(reportId);

    if (result.success) {
      showNotification('Report downloaded successfully!', 'success');
    } else {
      throw new Error(result.error);
    }
  } catch (error) {
    console.error('Download error:', error);
    showNotification('Failed to download report: ' + error.message, 'error');
  }
}

// Delete specific report
async function deleteReport(reportId) {
  if (!confirm('Are you sure you want to delete this report? This action cannot be undone.')) {
    return;
  }

  try {
    console.log('Deleting report:', reportId);
    const result = await window.api.deleteReport(reportId);

    if (result.success) {
      showNotification('Report deleted successfully!', 'success');
      // Reload reports
      loadSavedReports();
    } else {
      throw new Error(result.error);
    }
  } catch (error) {
    console.error('Delete error:', error);
    showNotification('Failed to delete report: ' + error.message, 'error');
  }
}

// Switch to specific tab
function switchToTab(tabName) {
  // Remove active class from all tabs
  document.querySelectorAll('.nav-tab').forEach(tab => {
    tab.classList.remove('active');
  });

  document.querySelectorAll('.tab-content').forEach(content => {
    content.classList.remove('active');
  });

  // Add active class to selected tab
  const targetTab = document.querySelector(`[data-tab="${tabName}"]`);
  const targetContent = document.getElementById(`${tabName}-tab`);

  if (targetTab) targetTab.classList.add('active');
  if (targetContent) targetContent.classList.add('active');

  // Load tab content
  loadTabContent(tabName);
}

// Load tab content function
function loadTabContent(tabName) {
  console.log('🔧 Loading content for tab:', tabName);

  // Call the appropriate initialization function based on the tab
  switch(tabName) {
    case 'pdf-sorter':
      if (typeof initializePdfSorterTab === 'function') {
        initializePdfSorterTab();
      }
      break;

    case 'report-manager':
      if (typeof initializeReportManagerTab === 'function') {
        initializeReportManagerTab();
      }
      break;

    case 'data-builder':
      if (typeof initializeDataBuilderTab === 'function') {
        initializeDataBuilderTab();
      }
      // Initialize the Data Builder module
      if (typeof initializeDataBuilderModule === 'function') {
        initializeDataBuilderModule();
      } else if (window.dataBuilder && typeof window.dataBuilder.initializeModule === 'function') {
        window.dataBuilder.initializeModule();
      } else {
        console.warn('⚠️ Data Builder module not available');
      }
      break;

    case 'bank-adviser':
      if (typeof initializeBankAdviserTab === 'function') {
        initializeBankAdviserTab();
      }
      break;

    case 'dictionary':
      if (typeof initDictionaryManager === 'function') {
        initDictionaryManager();
      }
      break;

    case 'payroll-audit':
      // Payroll audit tab initialization is handled by content switching manager
      if (typeof initializeContentSwitching === 'function') {
        initializeContentSwitching();
      }
      // CRITICAL: Check audit button state when payroll audit tab is accessed
      setTimeout(() => {
        if (typeof checkAuditButtonState === 'function') {
          checkAuditButtonState();
          console.log('🔍 Audit button state checked for payroll audit tab');
        }
      }, 200);
      break;

    case 'home':
      // Home tab doesn't need special initialization
      console.log('🏠 Home tab activated');
      break;

    default:
      console.log('📋 No specific initialization needed for tab:', tabName);
  }
}

// PDF Sorter functionality
function initializePdfSorterTab() {
  console.log('🔄 Initializing PDF Sorter tab...');

  // Initialize all PDF sorter components
  initializePdfUpload();
  initializeSortingConfiguration();
  initializePdfSorterEventListeners();

  // Reset to initial state
  resetPdfSorterToInitialState();
}

function initializePdfUpload() {
  console.log('🔧 Initializing PDF upload components...');

  const uploadArea = document.getElementById('pdf-upload-area');
  const fileInput = document.getElementById('pdf-file-input');
  const selectBtn = document.getElementById('select-pdf-btn');
  const changeBtn = document.getElementById('change-file-btn');

  console.log('📋 PDF upload elements found:', {
    uploadArea: !!uploadArea,
    fileInput: !!fileInput,
    selectBtn: !!selectBtn,
    changeBtn: !!changeBtn
  });

  if (!uploadArea || !fileInput || !selectBtn) {
    console.error('❌ PDF upload elements not found');
    return;
  }

  // File selection button - PRIMARY METHOD
  selectBtn.addEventListener('click', async (e) => {
    e.stopPropagation(); // Prevent event bubbling
    try {
      console.log('🔄 PDF file selection button clicked');
      const filePath = await window.api.selectPdfFile();
      console.log('📁 Selected file path:', filePath);
      if (filePath) {
        handlePdfFileSelectionWithPath(filePath);
      }
    } catch (error) {
      console.error('❌ Error selecting PDF file:', error);
      showNotification('Error selecting file', 'error');
    }
  });

  // Change file button
  if (changeBtn) {
    changeBtn.addEventListener('click', async (e) => {
      e.stopPropagation(); // Prevent event bubbling
      try {
        const filePath = await window.api.selectPdfFile();
        if (filePath) {
          handlePdfFileSelectionWithPath(filePath);
        }
      } catch (error) {
        console.error('Error selecting PDF file:', error);
        showNotification('Error selecting file', 'error');
      }
    });
  }

  // File input change (for hidden file input if used)
  fileInput.addEventListener('change', handlePdfFileSelection);

  // REMOVED: Drag and drop functionality to prevent conflicts
  // REMOVED: Upload area click to prevent double file dialogs
  console.log('✅ PDF upload initialized - using button-only approach');
}

function initializeSortingConfiguration() {
  // Secondary sort toggle
  const secondaryToggle = document.getElementById('enable-secondary-sort');
  const secondaryOptions = document.getElementById('secondary-sort-options');

  if (secondaryToggle && secondaryOptions) {
    secondaryToggle.addEventListener('change', () => {
      secondaryOptions.style.display = secondaryToggle.checked ? 'block' : 'none';
      if (!secondaryToggle.checked) {
        document.getElementById('secondary-sort-select').value = '';
      }
    });
  }

  // Tertiary sort toggle
  const tertiaryToggle = document.getElementById('enable-tertiary-sort');
  const tertiaryOptions = document.getElementById('tertiary-sort-options');

  if (tertiaryToggle && tertiaryOptions) {
    tertiaryToggle.addEventListener('change', () => {
      tertiaryOptions.style.display = tertiaryToggle.checked ? 'block' : 'none';
      if (!tertiaryToggle.checked) {
        document.getElementById('tertiary-sort-select').value = '';
      }
    });
  }

  // Update secondary/tertiary options based on primary selection
  const primarySortInputs = document.querySelectorAll('input[name="primary-sort"]');
  primarySortInputs.forEach(input => {
    input.addEventListener('change', updateSecondaryTertiaryOptions);
  });
}

function initializePdfSorterEventListeners() {
  // Start sorting button
  const startBtn = document.getElementById('start-sorting-btn');
  if (startBtn) {
    startBtn.addEventListener('click', startPdfSorting);
  }

  // Reset configuration button
  const resetBtn = document.getElementById('reset-config-btn');
  if (resetBtn) {
    resetBtn.addEventListener('click', resetSortingConfiguration);
  }

  // Cancel processing button
  const cancelBtn = document.getElementById('cancel-sorting-btn');
  if (cancelBtn) {
    cancelBtn.addEventListener('click', cancelPdfSorting);
  }

  // Download sorted PDF button
  const downloadBtn = document.getElementById('download-sorted-pdf-btn');
  if (downloadBtn) {
    downloadBtn.addEventListener('click', downloadSortedPdf);
  }

  // Save to reports button
  const saveBtn = document.getElementById('save-to-reports-btn');
  if (saveBtn) {
    saveBtn.addEventListener('click', saveToReports);
  }

  // Sort another PDF button
  const sortAnotherBtn = document.getElementById('sort-another-btn');
  if (sortAnotherBtn) {
    sortAnotherBtn.addEventListener('click', resetPdfSorterToInitialState);
  }
}

async function handlePdfFileSelectionWithPath(filePath) {
  console.log('🔄 handlePdfFileSelectionWithPath called with:', filePath);

  const fileInfo = document.getElementById('pdf-file-info');
  const uploadArea = document.getElementById('pdf-upload-area');
  const sortingConfig = document.getElementById('sorting-config');

  if (!filePath) {
    console.error('❌ No file path provided');
    showNotification('No file selected', 'error');
    return;
  }

  try {
    // Show loading state
    console.log('📊 Analyzing PDF file...');
    showNotification('Analyzing PDF file...', 'info');

    // Get PDF information
    console.log('🔍 Calling getPdfInfo API...');
    const pdfInfo = await window.api.getPdfInfo(filePath);
    console.log('📄 PDF Info received:', pdfInfo);

    if (pdfInfo.success) {
      // Extract filename from path
      const fileName = filePath.split('\\').pop().split('/').pop();

      // Update file info display
      document.getElementById('selected-file-name').textContent = fileName;
      document.getElementById('file-size').textContent = `${pdfInfo.file_size_mb} MB`;
      document.getElementById('estimated-pages').textContent = `${pdfInfo.total_pages} pages`;

      // Show file info and hide upload area
      fileInfo.style.display = 'flex';
      uploadArea.style.display = 'none';

      // Show sorting configuration
      sortingConfig.style.display = 'block';

      // Store file path for later use
      window.selectedPdfPath = filePath;
      window.selectedPdfInfo = pdfInfo;

      showNotification(`PDF analyzed: ${pdfInfo.total_pages} pages, ${pdfInfo.file_size_mb} MB`, 'success');

      // Scroll to configuration section
      sortingConfig.scrollIntoView({ behavior: 'smooth' });

    } else {
      throw new Error(pdfInfo.error || 'Failed to analyze PDF');
    }

  } catch (error) {
    console.error('PDF analysis error:', error);
    showNotification(`Failed to analyze PDF: ${error.message}`, 'error');
  }
}

// Keep the old function for backward compatibility but make it use the new one
async function handlePdfFileSelection() {
  const fileInput = document.getElementById('pdf-file-input');

  if (!fileInput.files || fileInput.files.length === 0) {
    return;
  }

  const file = fileInput.files[0];

  // Validate file type
  if (file.type !== 'application/pdf') {
    showNotification('Please select a valid PDF file', 'error');
    return;
  }

  // Validate file size (max 500MB)
  const maxSize = 500 * 1024 * 1024; // 500MB
  if (file.size > maxSize) {
    showNotification('PDF file is too large (max 500MB)', 'error');
    return;
  }

  // For file input, we need to save the file and get its path
  try {
    showNotification('Processing selected file...', 'info');
    const arrayBuffer = await file.arrayBuffer();
    const tempPath = await window.api.saveTempFile(arrayBuffer, file.name);
    if (tempPath) {
      await handlePdfFileSelectionWithPath(tempPath);
    } else {
      throw new Error('Failed to process selected file');
    }
  } catch (error) {
    console.error('Error processing selected file:', error);
    showNotification('Error processing file. Please try again.', 'error');
    fileInput.value = '';
  }
}

function updateSecondaryTertiaryOptions() {
  const primarySort = document.querySelector('input[name="primary-sort"]:checked')?.value;
  const secondarySelect = document.getElementById('secondary-sort-select');
  const tertiarySelect = document.getElementById('tertiary-sort-select');

  if (!primarySort || !secondarySelect || !tertiarySelect) return;

  // Get all options
  const allOptions = [
    { value: 'employee_no', text: 'Employee No.' },
    { value: 'employee_name', text: 'Employee Name' },
    { value: 'section', text: 'Section' },
    { value: 'department', text: 'Department' },
    { value: 'job_title', text: 'Job Title' }
  ];

  // Filter out primary sort option
  const availableOptions = allOptions.filter(opt => opt.value !== primarySort);

  // Update secondary select
  const currentSecondary = secondarySelect.value;
  secondarySelect.innerHTML = '<option value="">Select secondary sorting criteria</option>';
  availableOptions.forEach(opt => {
    const option = document.createElement('option');
    option.value = opt.value;
    option.textContent = opt.text;
    if (opt.value === currentSecondary) option.selected = true;
    secondarySelect.appendChild(option);
  });

  // Update tertiary select (exclude both primary and secondary)
  const currentTertiary = tertiarySelect.value;
  const secondaryValue = secondarySelect.value;
  const tertiaryOptions = availableOptions.filter(opt => opt.value !== secondaryValue);

  tertiarySelect.innerHTML = '<option value="">Select tertiary sorting criteria</option>';
  tertiaryOptions.forEach(opt => {
    const option = document.createElement('option');
    option.value = opt.value;
    option.textContent = opt.text;
    if (opt.value === currentTertiary) option.selected = true;
    tertiarySelect.appendChild(option);
  });
}

async function startPdfSorting() {
  if (!window.selectedPdfPath) {
    showNotification('Please select a PDF file first', 'error');
    return;
  }

  try {
    // Get sorting configuration
    const sortConfig = getSortingConfiguration();
    if (!sortConfig) {
      showNotification('Please configure sorting options', 'error');
      return;
    }

    console.log('🚀 Starting PDF sorting with config:', sortConfig);

    // Set up real-time progress listener for PDF Sorter BEFORE starting
    setupPdfSorterProgressListener();

    // Show processing section
    showProcessingSection();

    // Add a small delay to ensure UI is ready
    await new Promise(resolve => setTimeout(resolve, 100));

    // Start sorting with progress updates
    console.log('📤 Calling sortPdf API...');
    const result = await window.api.sortPdf(window.selectedPdfPath, sortConfig);

    if (result.success) {
      // Get user info and add to result
      const reportName = document.getElementById('sorter-report-name')?.value || 'Unknown';
      const reportDesignation = document.getElementById('sorter-report-designation')?.value || 'N/A';

      // Add user info to the result for global storage
      result.report_name = reportName;
      result.report_designation = reportDesignation;

      // Store result globally for download/save functions
      window.sortingResult = result;

      // Show results
      showSortingResults(result);
      showNotification('PDF sorting completed successfully!', 'success');

      // Update final progress
      updateSortingProgress(100, 'Sorting completed successfully!', {
        processed_pages: result.total_pages || 0,
        payslips_found: result.total_payslips || 0,
        current_stage: 'Complete'
      });
    } else {
      throw new Error(result.error || 'Sorting failed');
    }

  } catch (error) {
    console.error('PDF sorting error:', error);

    // Show detailed error information
    let errorMessage = 'Sorting failed';
    if (error.message) {
      errorMessage += `: ${error.message}`;
    }

    // Log additional error details for debugging
    if (error.raw_output) {
      console.error('[PDF-SORTER] Raw Python output:', error.raw_output);
      errorMessage += '\nCheck console for detailed error information.';
    }

    showNotification(errorMessage, 'error');
    hideProcessingSection();
  }
}

function getSortingConfiguration() {
  // Get primary sort
  const primarySort = document.querySelector('input[name="primary-sort"]:checked')?.value;
  if (!primarySort) {
    return null;
  }

  // Get secondary sort
  const secondaryEnabled = document.getElementById('enable-secondary-sort')?.checked;
  const secondarySort = secondaryEnabled ? document.getElementById('secondary-sort-select')?.value || '' : '';

  // Get tertiary sort
  const tertiaryEnabled = document.getElementById('enable-tertiary-sort')?.checked;
  const tertiarySort = tertiaryEnabled ? document.getElementById('tertiary-sort-select')?.value || '' : '';

  // Get sort order
  const sortOrder = document.querySelector('input[name="sort-order"]:checked')?.value || 'ascending';

  return {
    primary_sort: primarySort,
    secondary_sort: secondarySort,
    tertiary_sort: tertiarySort,
    sort_order: sortOrder
  };
}

function showProcessingSection() {
  // Hide configuration section
  document.getElementById('sorting-config').style.display = 'none';

  // Show processing section
  const processingSection = document.getElementById('sorting-progress');
  processingSection.style.display = 'block';

  // Reset progress
  updateSortingProgress(0, 'Initializing...', {
    processed_pages: 0,
    payslips_found: 0,
    current_stage: 'Initialization'
  });

  // Scroll to processing section
  processingSection.scrollIntoView({ behavior: 'smooth' });
}

function hideProcessingSection() {
  document.getElementById('sorting-progress').style.display = 'none';
  document.getElementById('sorting-config').style.display = 'block';
}

function setupPdfSorterProgressListener() {
  // Set up real-time progress listener specifically for PDF Sorter
  if (window.api && window.api.onRealTimeExtractionUpdate) {
    console.log('🔄 Setting up PDF Sorter progress listener...');

    window.pdfSorterProgressListener = window.api.onRealTimeExtractionUpdate((updateData) => {
      console.log('📊 PDF Sorter progress update received:', updateData);

      // Handle different types of updates for PDF Sorter with improved real-time sync
      if (updateData.type === 'pdf_sorter_progress') {
        console.log('📈 Processing PDF sorter progress update:', updateData);
        updateSortingProgress(
          updateData.percentage || 0,
          updateData.message || updateData.status || 'Processing...',
          {
            processed_pages: updateData.processed_pages || 0,
            payslips_found: updateData.extracted_payslips || 0,
            current_stage: updateData.current_stage || 'Processing'
          }
        );
      } else if (updateData.type === 'extraction_progress') {
        console.log('📈 Processing extraction progress update:', updateData);
        // Handle general extraction progress for PDF Sorter
        const percentage = updateData.percentage || 0;
        const message = updateData.message || 'Extracting payslips...';
        updateSortingProgress(percentage, message, {
          processed_pages: updateData.processed_pages || 0,
          payslips_found: updateData.extracted_payslips || 0,
          current_stage: 'Extracting Data'
        });
      } else if (updateData.message && updateData.message.includes('Copying page')) {
        console.log('📈 Processing page copying update:', updateData);
        // Parse copying messages for real-time page updates
        const copyMatch = updateData.message.match(/Copying page (\d+)\/(\d+)/);
        if (copyMatch) {
          const [, current, total] = copyMatch;
          const percentage = Math.round((parseInt(current) / parseInt(total)) * 100);
          updateSortingProgress(percentage, `Copying page ${current}/${total}`, {
            processed_pages: parseInt(total), // Total pages processed
            payslips_found: updateData.extracted_payslips || parseInt(total),
            current_stage: 'Generating Sorted PDF'
          });
        }
      } else if (updateData.message && updateData.message.includes('Processed')) {
        console.log('📈 Processing page progress update:', updateData);
        // Parse processing messages
        const processedMatch = updateData.message.match(/Processed (\d+)\/(\d+)/);
        if (processedMatch) {
          const [, current, total] = processedMatch;
          const percentage = Math.round((parseInt(current) / parseInt(total)) * 100);
          updateSortingProgress(percentage, `Processed ${current}/${total} pages`, {
            processed_pages: parseInt(current),
            current_stage: 'Processing Pages'
          });
        }
      } else {
        console.log('📋 Other update type for PDF Sorter:', updateData.type, updateData);
        // Handle any other progress updates with better data extraction
        if (updateData.message || updateData.status) {
          updateSortingProgress(
            updateData.percentage || 0,
            updateData.message || updateData.status || 'Processing...',
            {
              processed_pages: updateData.processed_pages || 0,
              payslips_found: updateData.extracted_payslips || 0,
              current_stage: updateData.current_stage || 'Processing'
            }
          );
        }

        // Check for final completion
        if (updateData.final_result || (updateData.percentage >= 100 && updateData.current_stage === 'Complete')) {
          console.log('🎉 PDF Sorting completed! Waiting for final result...');
          // Wait a moment for the backend to return the final result
          setTimeout(() => {
            console.log('⏰ Checking for completion result...');
            // The main sorting function should handle showing results
          }, 1000);
        }
      }
    });

    console.log('✅ PDF Sorter progress listener setup complete');

  } else {
    console.warn('❌ Real-time extraction API not available for PDF Sorter');
  }
}

function updateSortingProgress(percentage, status, stats = {}) {
  console.log(`🔄 Updating sorting progress: ${percentage}% - ${status}`, stats);

  // Ensure percentage is within bounds
  const safePercentage = Math.min(100, Math.max(0, percentage));

  // Update progress bar with smooth animation
  const progressFill = document.getElementById('sort-progress-fill');
  const progressPercentage = document.getElementById('sort-progress-percentage');
  const progressStatus = document.getElementById('sort-progress-status');

  if (progressFill) {
    progressFill.style.width = `${safePercentage}%`;
    progressFill.style.transition = 'width 0.3s ease';
    console.log(`📊 Progress bar updated to ${safePercentage}%`);
  } else {
    console.warn('❌ Progress fill element not found');
  }

  if (progressPercentage) {
    progressPercentage.textContent = `${safePercentage}%`;
  } else {
    console.warn('❌ Progress percentage element not found');
  }

  if (progressStatus) {
    progressStatus.textContent = status;
  } else {
    console.warn('❌ Progress status element not found');
  }

  // Update stats with real-time synchronization
  const pagesProcessed = document.getElementById('pages-processed');
  const payslipsFound = document.getElementById('payslips-found');
  const currentStage = document.getElementById('current-stage');

  // Force immediate DOM updates for real-time display
  if (pagesProcessed && stats.processed_pages !== undefined) {
    pagesProcessed.textContent = stats.processed_pages;
    pagesProcessed.style.color = '#1976d2'; // Highlight updated values
    setTimeout(() => { pagesProcessed.style.color = ''; }, 500);
  }

  if (payslipsFound && (stats.payslips_found !== undefined || stats.extracted_payslips !== undefined)) {
    const payslipCount = stats.payslips_found || stats.extracted_payslips || 0;
    payslipsFound.textContent = payslipCount;
    payslipsFound.style.color = '#1976d2'; // Highlight updated values
    setTimeout(() => { payslipsFound.style.color = ''; }, 500);
  }

  if (currentStage && stats.current_stage) {
    currentStage.textContent = stats.current_stage;
    currentStage.style.color = '#1976d2'; // Highlight updated values
    setTimeout(() => { currentStage.style.color = ''; }, 500);
  }

  // Force DOM repaint for immediate visual updates
  if (progressFill) {
    progressFill.offsetHeight; // Trigger reflow
  }
}

function showSortingResults(result) {
  // Hide processing section
  document.getElementById('sorting-progress').style.display = 'none';

  // Show results section
  const resultsSection = document.getElementById('sorting-results');
  resultsSection.style.display = 'block';

  // Update summary stats
  document.getElementById('total-payslips-sorted').textContent = result.total_payslips || 0;
  document.getElementById('processing-time').textContent = `${result.processing_time || 0}s`;
  document.getElementById('sort-criteria-used').textContent = result.sort_criteria || 'Unknown';

  // Update preview
  updateSortingPreview(result.preview_items || []);

  // Store result for download
  window.sortingResult = result;

  // Scroll to results
  resultsSection.scrollIntoView({ behavior: 'smooth' });
}

function updateSortingPreview(previewItems) {
  const previewList = document.getElementById('preview-list');
  if (!previewList) return;

  previewList.innerHTML = '';

  if (previewItems.length === 0) {
    previewList.innerHTML = '<p style="text-align: center; color: #666;">No preview available</p>';
    return;
  }

  previewItems.forEach((item, index) => {
    const previewItem = document.createElement('div');
    previewItem.className = 'preview-item';

    previewItem.innerHTML = `
      <div class="preview-employee">
        ${index + 1}. ${item.employee_name || 'Unknown'} (${item.employee_no || 'N/A'})
      </div>
      <div class="preview-details">
        ${item.primary_value || 'N/A'} • Page ${item.page_number || 'N/A'}
      </div>
    `;

    previewList.appendChild(previewItem);
  });
}

async function downloadSortedPdf() {
  if (!window.sortingResult || !window.sortingResult.output_path) {
    showNotification('No sorted PDF available for download', 'error');
    console.error('❌ No sorting result or output path available');
    return;
  }

  try {
    console.log('📁 Attempting to open sorted PDF:', window.sortingResult.output_path);
    console.log('📊 Full sorting result:', window.sortingResult);

    // Normalize the path to handle any relative path issues
    let filePath = window.sortingResult.output_path;

    // If path contains "..", try to normalize it
    if (filePath.includes('..')) {
      console.log('🔧 Normalizing path with relative components...');
      // Convert relative path to absolute path by removing the core/.. part
      filePath = filePath.replace(/core[\\\/]\.\.[\\\/]/, '');
      console.log('🔧 Normalized path:', filePath);
    }

    // Ensure we have an absolute path
    if (!filePath.match(/^[A-Z]:/)) {
      // If not absolute, prepend the workspace root
      filePath = `C:\\THE PAYROLL AUDITOR\\${filePath}`;
      console.log('🔧 Made absolute path:', filePath);
    }

    const result = await window.api.openFile(filePath);
    if (result.success) {
      showNotification('Sorted PDF opened successfully', 'success');
      console.log('✅ PDF opened successfully');
    } else {
      console.error('❌ Failed to open PDF:', result.error);
      throw new Error(result.error || 'Failed to open PDF');
    }
  } catch (error) {
    console.error('❌ Download error:', error);
    console.error('❌ Sorting result:', window.sortingResult);
    showNotification(`Failed to open PDF: ${error.message}`, 'error');
  }
}

async function saveToReports() {
  if (!window.sortingResult) {
    showNotification('No sorting result to save', 'error');
    return;
  }

  try {
    // Get user info from PDF Sorter form
    const reportName = document.getElementById('sorter-report-name')?.value || 'Unknown';
    const reportDesignation = document.getElementById('sorter-report-designation')?.value || 'N/A';

    // Add source tab information and user details
    const reportData = {
      ...window.sortingResult,
      source_tab: 'pdf_sorter',
      report_type: 'PDF_Sorting_Report',
      report_name: reportName,
      report_designation: reportDesignation
    };

    console.log('💾 Saving PDF Sorter report with user info:', {
      report_name: reportName,
      report_designation: reportDesignation
    });

    const result = await window.api.saveToReportManager(reportData);
    if (result.success) {
      showNotification('Report saved successfully', 'success');
    } else {
      throw new Error(result.error || 'Failed to save report');
    }
  } catch (error) {
    console.error('Save error:', error);
    showNotification(`Failed to save report: ${error.message}`, 'error');
  }
}

function resetSortingConfiguration() {
  // Reset primary sort to employee_no
  const employeeNoRadio = document.getElementById('sort-employee-no');
  if (employeeNoRadio) employeeNoRadio.checked = true;

  // Disable and reset secondary sort
  const secondaryToggle = document.getElementById('enable-secondary-sort');
  const secondaryOptions = document.getElementById('secondary-sort-options');
  const secondarySelect = document.getElementById('secondary-sort-select');

  if (secondaryToggle) secondaryToggle.checked = false;
  if (secondaryOptions) secondaryOptions.style.display = 'none';
  if (secondarySelect) secondarySelect.value = '';

  // Disable and reset tertiary sort
  const tertiaryToggle = document.getElementById('enable-tertiary-sort');
  const tertiaryOptions = document.getElementById('tertiary-sort-options');
  const tertiarySelect = document.getElementById('tertiary-sort-select');

  if (tertiaryToggle) tertiaryToggle.checked = false;
  if (tertiaryOptions) tertiaryOptions.style.display = 'none';
  if (tertiarySelect) tertiarySelect.value = '';

  // Reset sort order to ascending
  const ascendingRadio = document.querySelector('input[name="sort-order"][value="ascending"]');
  if (ascendingRadio) ascendingRadio.checked = true;

  // Update secondary/tertiary options
  updateSecondaryTertiaryOptions();

  showNotification('Configuration reset to defaults', 'info');
}

function resetPdfSorterToInitialState() {
  // Clear file selection
  const fileInput = document.getElementById('pdf-file-input');
  if (fileInput) fileInput.value = '';

  // Hide all sections except upload
  document.getElementById('pdf-file-info').style.display = 'none';
  document.getElementById('sorting-config').style.display = 'none';
  document.getElementById('sorting-progress').style.display = 'none';
  document.getElementById('sorting-results').style.display = 'none';

  // Show upload area
  document.getElementById('pdf-upload-area').style.display = 'block';

  // Clear stored data
  window.selectedPdfPath = null;
  window.selectedPdfInfo = null;
  window.sortingResult = null;

  // Reset configuration
  resetSortingConfiguration();

  console.log('PDF Sorter reset to initial state');
}

async function cancelPdfSorting() {
  try {
    const result = await window.api.stopPdfSorting();
    if (result.success) {
      showNotification('Sorting cancelled', 'info');
      hideProcessingSection();
    } else {
      showNotification('Failed to cancel sorting', 'error');
    }
  } catch (error) {
    console.error('Cancel error:', error);
    showNotification('Failed to cancel sorting', 'error');
  }
}

// Data Builder functionality
function initializeDataBuilderTab() {
  console.log('🔧 Initializing Data Builder tab...');

  // Initialize file upload
  initializeDataBuilderUpload();

  // Initialize dictionary manager interface
  initializeDataBuilderDictionaryManager();

  // Initialize build controls
  initializeDataBuilderControls();

  // Initialize analytics
  initializeDataBuilderAnalytics();

  // Set up dictionary update listener
  setupDictionaryUpdateListener();

  // Set default month to current month
  const monthInput = document.getElementById('month-year-input');
  if (monthInput) {
    const now = new Date();
    const currentMonth = now.toISOString().slice(0, 7); // YYYY-MM format
    monthInput.value = currentMonth;
  }

  console.log('✅ Data Builder tab initialized successfully');
}

// Set up listener for dictionary updates from Payroll Auditor
function setupDictionaryUpdateListener() {
  // Listen for dictionary updates from the main dictionary manager
  if (window.appEvents) {
    window.appEvents.on('dictionaryUpdated', (updatedDictionary) => {
      console.log('📡 Dictionary updated in Payroll Auditor, refreshing Data Builder...');

      // Check if Data Builder tab is active
      const dataBuilderTab = document.getElementById('data-builder-tab');
      if (dataBuilderTab && dataBuilderTab.classList.contains('active')) {
        // Refresh the Data Builder dictionary display
        populateDataBuilderDictionaryTabs(updatedDictionary);
        showNotification('Dictionary updated from Payroll Auditor', 'info');
      }
    });
  }

  // Also set up a global event emitter if it doesn't exist
  if (!window.appEvents) {
    window.appEvents = {
      listeners: {},
      on: function(event, callback) {
        if (!this.listeners[event]) {
          this.listeners[event] = [];
        }
        this.listeners[event].push(callback);
      },
      emit: function(event, data) {
        if (this.listeners[event]) {
          this.listeners[event].forEach(callback => callback(data));
        }
      }
    };
  }
}

function initializeDataBuilderUpload() {
  console.log('🔧 Initializing Data Builder file upload...');

  const selectBtn = document.getElementById('select-data-builder-file-btn');
  const changeBtn = document.getElementById('change-data-builder-file-btn');
  const fileInput = document.getElementById('data-builder-file-input');

  if (selectBtn) {
    selectBtn.addEventListener('click', async (e) => {
      e.stopPropagation();
      try {
        const filePath = await window.api.selectPdfFile();
        if (filePath) {
          handleDataBuilderFileSelection(filePath);
        }
      } catch (error) {
        console.error('❌ Error selecting file:', error);
        showNotification('Error selecting file', 'error');
      }
    });
  }

  if (changeBtn) {
    changeBtn.addEventListener('click', async (e) => {
      e.stopPropagation();
      try {
        const filePath = await window.api.selectPdfFile();
        if (filePath) {
          handleDataBuilderFileSelection(filePath);
        }
      } catch (error) {
        console.error('❌ Error selecting file:', error);
        showNotification('Error selecting file', 'error');
      }
    });
  }

  if (fileInput) {
    fileInput.addEventListener('change', (e) => {
      const file = e.target.files[0];
      if (file) {
        handleDataBuilderFileSelection(file.path || file.name);
      }
    });
  }
}

function handleDataBuilderFileSelection(filePath) {
  console.log('📁 Data Builder file selected:', filePath);

  const uploadArea = document.getElementById('data-builder-upload-area');
  const fileInfo = document.getElementById('data-builder-file-info');
  const fileName = document.getElementById('data-builder-file-name');
  const fileSize = document.getElementById('data-builder-file-size');

  if (uploadArea && fileInfo && fileName) {
    // Hide upload area and show file info
    uploadArea.style.display = 'none';
    fileInfo.style.display = 'block';

    // Extract filename from path
    const name = filePath.split(/[\\/]/).pop();
    fileName.textContent = name;

    // Get file size (placeholder for now)
    fileSize.textContent = 'Size: Calculating...';

    // Store file path for later use
    window.dataBuilderSelectedFile = filePath;

    showNotification('File selected successfully', 'success');

    // Auto-refresh dictionary to show current items
    refreshDataBuilderDictionary();
  }
}

function initializeDataBuilderDictionaryManager() {
  console.log('🔧 Initializing Data Builder Dictionary Manager...');

  // Initialize section tabs
  initializeDictionaryTabs();

  // Initialize global controls
  initializeDictionaryControls();

  // Load initial dictionary data
  refreshDataBuilderDictionary();

  console.log('✅ Data Builder Dictionary Manager initialized');
}

function initializeDictionaryTabs() {
  const sectionTabs = document.querySelectorAll('.section-tab');
  const sectionPanels = document.querySelectorAll('.section-panel');

  sectionTabs.forEach(tab => {
    tab.addEventListener('click', () => {
      const targetSection = tab.dataset.section;

      // Remove active class from all tabs and panels
      sectionTabs.forEach(t => t.classList.remove('active'));
      sectionPanels.forEach(p => p.classList.remove('active'));

      // Add active class to clicked tab and corresponding panel
      tab.classList.add('active');
      const targetPanel = document.querySelector(`.section-panel[data-section="${targetSection}"]`);
      if (targetPanel) {
        targetPanel.classList.add('active');
      }

      console.log(`📋 Switched to ${targetSection} section`);
    });
  });
}

function initializeDictionaryControls() {
  const selectAllCheckbox = document.getElementById('select-all-items');
  const refreshBtn = document.getElementById('refresh-dictionary-btn');
  const addItemBtn = document.getElementById('add-item-btn');

  if (selectAllCheckbox) {
    selectAllCheckbox.addEventListener('change', (e) => {
      const isChecked = e.target.checked;
      const activeSection = document.querySelector('.section-panel.active');
      if (activeSection) {
        const checkboxes = activeSection.querySelectorAll('.include-toggle input[type="checkbox"]');
        checkboxes.forEach(checkbox => {
          checkbox.checked = isChecked;
        });
      }
      console.log(`📋 ${isChecked ? 'Selected' : 'Deselected'} all items in active section`);
    });
  }

  if (refreshBtn) {
    refreshBtn.addEventListener('click', () => {
      refreshDataBuilderDictionary();
    });
  }

  if (addItemBtn) {
    addItemBtn.addEventListener('click', () => {
      showAddItemModal();
    });
  }
}

async function refreshDataBuilderDictionary() {
  console.log('🔄 Refreshing Data Builder dictionary...');

  try {
    // Use the main dictionary system from Payroll Auditor
    const dictionaryData = await window.api.getEnhancedDictionary();

    if (dictionaryData) {
      populateDataBuilderDictionaryTabs(dictionaryData);
      showNotification('Dictionary refreshed', 'success');
    } else {
      throw new Error('No dictionary data received');
    }
  } catch (error) {
    console.error('❌ Error refreshing dictionary:', error);
    showNotification('Error refreshing dictionary: ' + error.message, 'error');

    // Load sample data for demonstration
    loadSampleDictionaryData();
  }
}

function populateDataBuilderDictionaryTabs(dictionaryData) {
  console.log('📋 Populating Data Builder dictionary tabs with data:', dictionaryData);

  if (!dictionaryData || Object.keys(dictionaryData).length === 0) {
    console.warn('❌ No dictionary data provided');
    loadSampleDictionaryData();
    return;
  }

  // Define section mappings
  const sectionMappings = {
    'PERSONAL DETAILS': 'personal-details-table-body',
    'EARNINGS': 'earnings-table-body',
    'DEDUCTIONS': 'deductions-table-body',
    'EMPLOYERS CONTRIBUTION': 'employers-contribution-table-body',
    'LOANS': 'loans-table-body',
    'EMPLOYEE BANK DETAILS': 'employee-bank-details-table-body'
  };

  let totalItems = 0;

  // Populate each section
  Object.entries(sectionMappings).forEach(([sectionName, tableBodyId]) => {
    const tableBody = document.getElementById(tableBodyId);
    if (!tableBody) {
      console.warn(`❌ Table body not found for section: ${sectionName}`);
      return;
    }

    // Clear existing content
    tableBody.innerHTML = '';

    const sectionData = dictionaryData[sectionName];
    if (sectionData && sectionData.items && Object.keys(sectionData.items).length > 0) {
      Object.entries(sectionData.items).forEach(([itemName, itemData]) => {
        const row = createDictionaryTabRow(itemName, itemData);
        tableBody.appendChild(row);
        totalItems++;
      });
    } else {
      // Show empty state
      tableBody.innerHTML = `
        <tr>
          <td colspan="5" class="empty-state">
            <i class="fas fa-inbox"></i>
            <h4>No Items Found</h4>
            <p>No items found in ${sectionName} section</p>
          </td>
        </tr>
      `;
    }
  });

  console.log(`✅ Populated ${totalItems} dictionary items across all sections`);
}

function createDictionaryTabRow(itemName, itemData) {
  const row = document.createElement('tr');

  // Item Name cell
  const nameCell = document.createElement('td');
  nameCell.textContent = itemName;
  nameCell.style.fontWeight = '500';

  // Format cell
  const formatCell = document.createElement('td');
  const formatSpan = document.createElement('span');
  formatSpan.className = 'format-cell';
  formatSpan.textContent = itemData.format || '[TC]';
  formatCell.appendChild(formatSpan);

  // Value Format cell
  const valueFormatCell = document.createElement('td');
  valueFormatCell.className = 'value-format-cell';
  valueFormatCell.textContent = itemData.value_format || 'Text';

  // Include in Report toggle cell
  const includeCell = document.createElement('td');
  const toggleLabel = document.createElement('label');
  toggleLabel.className = 'include-toggle';

  const toggleInput = document.createElement('input');
  toggleInput.type = 'checkbox';
  toggleInput.checked = itemData.include_in_report !== false; // Default to true
  toggleInput.dataset.itemName = itemName;

  const toggleSlider = document.createElement('span');
  toggleSlider.className = 'toggle-slider';

  toggleLabel.appendChild(toggleInput);
  toggleLabel.appendChild(toggleSlider);
  includeCell.appendChild(toggleLabel);

  // Actions cell
  const actionsCell = document.createElement('td');
  const actionButtons = document.createElement('div');
  actionButtons.className = 'action-buttons';

  const editBtn = document.createElement('button');
  editBtn.className = 'action-btn edit';
  editBtn.innerHTML = '<i class="fas fa-edit"></i>';
  editBtn.title = 'Edit Item';
  editBtn.onclick = () => editDictionaryItem(itemName, itemData);

  const deleteBtn = document.createElement('button');
  deleteBtn.className = 'action-btn delete';
  deleteBtn.innerHTML = '<i class="fas fa-trash"></i>';
  deleteBtn.title = 'Delete Item';
  deleteBtn.onclick = () => deleteDictionaryItem(itemName);

  actionButtons.appendChild(editBtn);
  actionButtons.appendChild(deleteBtn);
  actionsCell.appendChild(actionButtons);

  // Append all cells to row
  row.appendChild(nameCell);
  row.appendChild(formatCell);
  row.appendChild(valueFormatCell);
  row.appendChild(includeCell);
  row.appendChild(actionsCell);

  return row;
}

// Edit and delete functions for dictionary items
function editDictionaryItem(itemName, itemData) {
  console.log('Edit item:', itemName, itemData);

  // Get current section
  const activeSection = document.querySelector('.section-panel.active');
  const sectionName = activeSection ? activeSection.dataset.section : 'PERSONAL DETAILS';

  // Create edit modal
  const editModalHtml = `
    <div class="modal" id="edit-item-modal">
      <div class="modal-content">
        <div class="modal-header">
          <h3><i class="fas fa-edit"></i> Edit Dictionary Item</h3>
          <span class="close-modal" onclick="closeEditModal()">&times;</span>
        </div>
        <div class="modal-body">
          <form id="edit-item-form">
            <div class="form-group">
              <label for="edit-item-name">Item Name:</label>
              <input type="text" id="edit-item-name" value="${itemName}" required>
            </div>
            <div class="form-group">
              <label for="edit-format">Format:</label>
              <select id="edit-format">
                <option value="[TC]" ${itemData.format === '[TC]' ? 'selected' : ''}>Text/Characters [TC]</option>
                <option value="[UC]" ${itemData.format === '[UC]' ? 'selected' : ''}>Uppercase [UC]</option>
                <option value="[TC][.]" ${itemData.format === '[TC][.]' ? 'selected' : ''}>Text with Numbers [TC][.]</option>
              </select>
            </div>
            <div class="form-group">
              <label for="edit-value-format">Value Format:</label>
              <select id="edit-value-format">
                <option value="Text" ${itemData.value_format === 'Text' ? 'selected' : ''}>Text</option>
                <option value="Alphanumeric" ${itemData.value_format === 'Alphanumeric' ? 'selected' : ''}>Alphanumeric</option>
                <option value="Numeric with decimal places" ${itemData.value_format === 'Numeric with decimal places' ? 'selected' : ''}>Numeric with decimal places</option>
              </select>
            </div>
            <div class="form-group">
              <label>
                <input type="checkbox" id="edit-include-report" ${itemData.include_in_report ? 'checked' : ''}>
                Include in Report
              </label>
            </div>
          </form>
        </div>
        <div class="modal-footer">
          <button class="btn btn-secondary" onclick="closeEditModal()">Cancel</button>
          <button class="btn btn-primary" onclick="saveEditedItem('${itemName}', '${sectionName}')">Save Changes</button>
        </div>
      </div>
    </div>
  `;

  document.body.insertAdjacentHTML('beforeend', editModalHtml);
}

function deleteDictionaryItem(itemName) {
  console.log('Delete item:', itemName);

  // Get current section
  const activeSection = document.querySelector('.section-panel.active');
  const sectionName = activeSection ? activeSection.dataset.section : 'PERSONAL DETAILS';

  // Create confirmation modal
  const deleteModalHtml = `
    <div class="modal" id="delete-item-modal">
      <div class="modal-content">
        <div class="modal-header">
          <h3><i class="fas fa-trash"></i> Delete Dictionary Item</h3>
          <span class="close-modal" onclick="closeDeleteModal()">&times;</span>
        </div>
        <div class="modal-body">
          <p>Are you sure you want to delete the item <strong>"${itemName}"</strong> from the <strong>${sectionName}</strong> section?</p>
          <p class="text-warning"><i class="fas fa-exclamation-triangle"></i> This action cannot be undone.</p>
        </div>
        <div class="modal-footer">
          <button class="btn btn-secondary" onclick="closeDeleteModal()">Cancel</button>
          <button class="btn btn-danger" onclick="confirmDeleteItem('${itemName}', '${sectionName}')">Delete Item</button>
        </div>
      </div>
    </div>
  `;

  document.body.insertAdjacentHTML('beforeend', deleteModalHtml);
}

function createDictionaryRow(section, item) {
  const row = document.createElement('tr');

  const includeCell = document.createElement('td');
  const includeCheckbox = document.createElement('input');
  includeCheckbox.type = 'checkbox';
  includeCheckbox.checked = item.includeInReport !== false; // Default to true
  includeCheckbox.dataset.section = section;
  includeCheckbox.dataset.label = item.label || item.name;
  includeCell.appendChild(includeCheckbox);

  const sectionCell = document.createElement('td');
  sectionCell.textContent = section;

  const labelCell = document.createElement('td');
  labelCell.textContent = item.label || item.name || 'Unknown';

  const typeCell = document.createElement('td');
  typeCell.textContent = item.type || 'Text';

  const statusCell = document.createElement('td');
  const statusBadge = document.createElement('span');
  statusBadge.className = `status-badge status-${item.status || 'fixed'}`;
  statusBadge.textContent = item.status || 'Fixed';
  statusCell.appendChild(statusBadge);

  const frequencyCell = document.createElement('td');
  frequencyCell.textContent = item.frequency || 'Always';

  row.appendChild(includeCell);
  row.appendChild(sectionCell);
  row.appendChild(labelCell);
  row.appendChild(typeCell);
  row.appendChild(statusCell);
  row.appendChild(frequencyCell);

  return row;
}

function loadSampleDictionaryData() {
  console.log('📋 Loading sample dictionary data...');

  const sampleData = {
    'Personal Details': [
      { label: 'Employee No.', type: 'Text', status: 'fixed', frequency: 'Always' },
      { label: 'Employee Name', type: 'Text', status: 'fixed', frequency: 'Always' },
      { label: 'SSF No.', type: 'Text', status: 'fixed', frequency: 'Always' },
      { label: 'Ghana Card ID', type: 'Text', status: 'fixed', frequency: 'Always' },
      { label: 'Section', type: 'Text', status: 'fixed', frequency: 'Always' },
      { label: 'Department', type: 'Text', status: 'fixed', frequency: 'Always' },
      { label: 'Job Title', type: 'Text', status: 'fixed', frequency: 'Always' }
    ],
    'Earnings': [
      { label: 'BASIC SALARY', type: 'Currency', status: 'fixed', frequency: 'Always' },
      { label: 'GROSS SALARY', type: 'Currency', status: 'fixed', frequency: 'Always' },
      { label: 'NET PAY', type: 'Currency', status: 'fixed', frequency: 'Always' },
      { label: 'OVERTIME', type: 'Currency', status: 'varying', frequency: 'Sometimes' },
      { label: 'ALLOWANCES', type: 'Currency', status: 'varying', frequency: 'Sometimes' }
    ],
    'Deductions': [
      { label: 'TOTAL DEDUCTIONS', type: 'Currency', status: 'fixed', frequency: 'Always' },
      { label: 'SSF EMPLOYEE', type: 'Currency', status: 'fixed', frequency: 'Always' },
      { label: 'INCOME TAX', type: 'Currency', status: 'fixed', frequency: 'Always' },
      { label: 'TAXABLE SALARY', type: 'Currency', status: 'fixed', frequency: 'Always' },
      { label: 'TITHES', type: 'Currency', status: 'varying', frequency: 'Sometimes' }
    ],
    'Loans': [
      { label: 'LOAN BALANCE', type: 'Currency', status: 'varying', frequency: 'Sometimes' },
      { label: 'CURRENT DEDUCTION', type: 'Currency', status: 'varying', frequency: 'Sometimes' }
    ],
    'Employers Contribution': [
      { label: 'SSF EMPLOYER', type: 'Currency', status: 'fixed', frequency: 'Always' }
    ],
    'Employee Bank Details': [
      { label: 'Bank', type: 'Text', status: 'fixed', frequency: 'Always' },
      { label: 'Account No.', type: 'Text', status: 'fixed', frequency: 'Always' },
      { label: 'Branch', type: 'Text', status: 'fixed', frequency: 'Always' }
    ]
  };

  // Convert old format to new format
  const convertedData = {};
  Object.entries(sampleData).forEach(([sectionName, items]) => {
    const mappedSectionName = sectionName.toUpperCase().replace(' ', ' ');
    convertedData[mappedSectionName] = {
      items: {}
    };

    items.forEach(item => {
      convertedData[mappedSectionName].items[item.label] = {
        format: item.type === 'Currency' ? '[UC]' : '[TC]',
        value_format: item.type === 'Currency' ? 'Numeric with decimal places' : 'Text',
        include_in_report: true,
        is_fixed: item.status === 'fixed'
      };
    });
  });

  populateDataBuilderDictionaryTabs(convertedData);
}

function initializeDataBuilderControls() {
  console.log('🔧 Initializing Data Builder controls...');

  const buildBtn = document.getElementById('build-spreadsheet-btn');
  const previewBtn = document.getElementById('preview-columns-btn');
  const cancelBtn = document.getElementById('cancel-data-builder-btn');

  if (buildBtn) {
    buildBtn.addEventListener('click', startSpreadsheetBuild);
  }

  if (previewBtn) {
    previewBtn.addEventListener('click', previewSpreadsheetColumns);
  }

  if (cancelBtn) {
    cancelBtn.addEventListener('click', cancelSpreadsheetBuild);
  }
}

async function startSpreadsheetBuild() {
  console.log('🚀 Starting spreadsheet build...');

  if (!window.dataBuilderSelectedFile) {
    showNotification('Please select a payroll file first', 'error');
    return;
  }

  const monthYear = document.getElementById('month-year-input').value;
  const includeAnalytics = document.getElementById('include-analytics').checked;
  const includeVarying = document.getElementById('include-varying-items').checked;

  // Get selected dictionary items
  const selectedItems = getSelectedDictionaryItems();

  if (selectedItems.length === 0) {
    showNotification('Please select at least one dictionary item', 'error');
    return;
  }

  // Show progress section
  showDataBuilderProgress();

  try {
    const buildConfig = {
      filePath: window.dataBuilderSelectedFile,
      monthYear: monthYear,
      selectedItems: selectedItems,
      includeAnalytics: includeAnalytics,
      includeVaryingItems: includeVarying
    };

    console.log('📋 Build configuration:', buildConfig);

    // Start the build process
    const result = await window.api.buildSpreadsheet(buildConfig);

    if (result.success) {
      showDataBuilderResults(result);
      showNotification('Spreadsheet built successfully!', 'success');
    } else {
      throw new Error(result.error || 'Failed to build spreadsheet');
    }

  } catch (error) {
    console.error('❌ Error building spreadsheet:', error);
    showNotification('Error building spreadsheet: ' + error.message, 'error');
    hideDataBuilderProgress();
  }
}

function getSelectedDictionaryItems() {
  // Use the new tabbed interface function
  return getSelectedDictionaryItemsFromTabs();
}

function showDataBuilderProgress() {
  const progressSection = document.getElementById('data-builder-progress-section');
  const resultsSection = document.getElementById('data-builder-results-section');

  if (progressSection) {
    progressSection.style.display = 'block';
    updateDataBuilderProgress(0, 'Initializing spreadsheet build...');
  }

  if (resultsSection) {
    resultsSection.style.display = 'none';
  }

  // Simulate progress updates
  let progress = 0;
  const progressInterval = setInterval(() => {
    progress += Math.random() * 15;
    if (progress > 90) progress = 90;

    const messages = [
      'Extracting payslip data...',
      'Processing employee information...',
      'Building spreadsheet columns...',
      'Calculating analytics...',
      'Finalizing spreadsheet...'
    ];

    const messageIndex = Math.floor(progress / 20);
    const message = messages[messageIndex] || 'Processing...';

    updateDataBuilderProgress(progress, message);

    if (progress >= 90) {
      clearInterval(progressInterval);
    }
  }, 500);

  window.dataBuilderProgressInterval = progressInterval;
}

function updateDataBuilderProgress(percentage, message) {
  const progressFill = document.getElementById('data-builder-progress-fill');
  const progressText = document.getElementById('data-builder-progress-text');

  if (progressFill) {
    progressFill.style.width = percentage + '%';
  }

  if (progressText) {
    progressText.textContent = message;
  }
}

function hideDataBuilderProgress() {
  const progressSection = document.getElementById('data-builder-progress-section');

  if (progressSection) {
    progressSection.style.display = 'none';
  }

  if (window.dataBuilderProgressInterval) {
    clearInterval(window.dataBuilderProgressInterval);
    window.dataBuilderProgressInterval = null;
  }
}

function showDataBuilderResults(result) {
  hideDataBuilderProgress();

  const resultsSection = document.getElementById('data-builder-results-section');

  if (resultsSection) {
    resultsSection.style.display = 'block';

    // Update summary information
    document.getElementById('total-employees').textContent = `Employees: ${result.totalEmployees || 0}`;
    document.getElementById('total-columns').textContent = `Columns: ${result.totalColumns || 0}`;
    document.getElementById('processing-time').textContent = `Time: ${result.processingTime || '0'}s`;

    document.getElementById('output-file-name').textContent = `File: ${result.fileName || 'spreadsheet.xlsx'}`;
    document.getElementById('output-file-size').textContent = `Size: ${result.fileSize || '0'} MB`;
    document.getElementById('output-location').textContent = `Location: ${result.outputPath || 'Not saved'}`;

    // Store result data for later use
    window.dataBuilderResult = result;
  }

  // Complete progress bar
  updateDataBuilderProgress(100, 'Spreadsheet build completed!');
}

async function previewSpreadsheetColumns() {
  console.log('👁️ Previewing spreadsheet columns...');

  // Get selected items from all dictionary tabs
  const selectedItems = getSelectedDictionaryItemsFromTabs();

  if (selectedItems.length === 0) {
    showNotification('No items selected for preview', 'warning');
    return;
  }

  // Group items by section for better display
  const groupedItems = {};
  selectedItems.forEach(item => {
    if (!groupedItems[item.section]) {
      groupedItems[item.section] = [];
    }
    groupedItems[item.section].push(item.label);
  });

  // Create preview modal
  const previewHtml = `
    <div class="modal" id="column-preview-modal">
      <div class="modal-content">
        <div class="modal-header">
          <h3><i class="fas fa-columns"></i> Column Preview</h3>
          <span class="close-modal" onclick="closeColumnPreview()">&times;</span>
        </div>
        <div class="modal-body">
          <p><strong>Selected Columns (${selectedItems.length}):</strong></p>
          <div class="column-preview-list">
            ${Object.entries(groupedItems).map(([section, items]) => `
              <div class="section-group">
                <h4 class="section-title">${section}</h4>
                <div class="section-items">
                  ${items.map(item => `
                    <div class="column-item">
                      <i class="fas fa-check-circle"></i>
                      <span class="column-label">${item}</span>
                    </div>
                  `).join('')}
                </div>
              </div>
            `).join('')}
          </div>
        </div>
        <div class="modal-footer">
          <button class="btn btn-secondary" onclick="closeColumnPreview()">Close</button>
        </div>
      </div>
    </div>
  `;

  // Add modal to page
  document.body.insertAdjacentHTML('beforeend', previewHtml);

  console.log(`✅ Preview showing ${selectedItems.length} selected columns across ${Object.keys(groupedItems).length} sections`);
}

function getSelectedDictionaryItemsFromTabs() {
  const selectedItems = [];
  const allToggleInputs = document.querySelectorAll('.include-toggle input[type="checkbox"]:checked');

  allToggleInputs.forEach(input => {
    const itemName = input.dataset.itemName;
    if (itemName) {
      // Find which section this item belongs to
      const sectionPanel = input.closest('.section-panel');
      const sectionName = sectionPanel ? sectionPanel.dataset.section : 'Unknown';

      selectedItems.push({
        section: sectionName,
        label: itemName
      });
    }
  });

  return selectedItems;
}

function closeColumnPreview() {
  const modal = document.getElementById('column-preview-modal');
  if (modal) {
    modal.remove();
  }
}

// Modal management functions
function closeEditModal() {
  const modal = document.getElementById('edit-item-modal');
  if (modal) {
    modal.remove();
  }
}

function closeDeleteModal() {
  const modal = document.getElementById('delete-item-modal');
  if (modal) {
    modal.remove();
  }
}

async function saveEditedItem(originalItemName, sectionName) {
  try {
    const itemName = document.getElementById('edit-item-name').value.trim();
    const format = document.getElementById('edit-format').value;
    const valueFormat = document.getElementById('edit-value-format').value;
    const includeInReport = document.getElementById('edit-include-report').checked;

    if (!itemName) {
      showNotification('Item name is required', 'error');
      return;
    }

    // Get current dictionary
    const dictionary = await window.api.getEnhancedDictionary();

    if (!dictionary || !dictionary[sectionName]) {
      showNotification('Dictionary section not found', 'error');
      return;
    }

    // If name changed, remove old item
    if (originalItemName !== itemName && dictionary[sectionName].items[originalItemName]) {
      delete dictionary[sectionName].items[originalItemName];
    }

    // Update/add item
    dictionary[sectionName].items[itemName] = {
      format: format,
      value_format: valueFormat,
      include_in_report: includeInReport,
      is_fixed: true // Assume edited items are fixed
    };

    // Save dictionary
    const success = await window.api.saveEnhancedDictionary(dictionary);

    if (success) {
      showNotification('Item updated successfully', 'success');
      closeEditModal();
      refreshDataBuilderDictionary(); // Refresh the display

      // Emit dictionary update event
      if (window.appEvents) {
        window.appEvents.emit('dictionaryUpdated', dictionary);
      }
    } else {
      showNotification('Failed to save changes', 'error');
    }

  } catch (error) {
    console.error('Error saving edited item:', error);
    showNotification('Error saving changes: ' + error.message, 'error');
  }
}

async function confirmDeleteItem(itemName, sectionName) {
  try {
    // Get current dictionary
    const dictionary = await window.api.getEnhancedDictionary();

    if (!dictionary || !dictionary[sectionName] || !dictionary[sectionName].items[itemName]) {
      showNotification('Item not found in dictionary', 'error');
      return;
    }

    // Remove item
    delete dictionary[sectionName].items[itemName];

    // Save dictionary
    const success = await window.api.saveEnhancedDictionary(dictionary);

    if (success) {
      showNotification('Item deleted successfully', 'success');
      closeDeleteModal();
      refreshDataBuilderDictionary(); // Refresh the display

      // Emit dictionary update event
      if (window.appEvents) {
        window.appEvents.emit('dictionaryUpdated', dictionary);
      }
    } else {
      showNotification('Failed to delete item', 'error');
    }

  } catch (error) {
    console.error('Error deleting item:', error);
    showNotification('Error deleting item: ' + error.message, 'error');
  }
}

// Add Item functionality
function showAddItemModal() {
  // Get current active section
  const activeSection = document.querySelector('.section-panel.active');
  const sectionName = activeSection ? activeSection.dataset.section : 'PERSONAL DETAILS';

  const addModalHtml = `
    <div class="modal" id="add-item-modal">
      <div class="modal-content">
        <div class="modal-header">
          <h3><i class="fas fa-plus"></i> Add New Dictionary Item</h3>
          <span class="close-modal" onclick="closeAddModal()">&times;</span>
        </div>
        <div class="modal-body">
          <form id="add-item-form">
            <div class="form-group">
              <label for="add-section">Section:</label>
              <select id="add-section">
                <option value="PERSONAL DETAILS" ${sectionName === 'PERSONAL DETAILS' ? 'selected' : ''}>Personal Details</option>
                <option value="EARNINGS" ${sectionName === 'EARNINGS' ? 'selected' : ''}>Earnings</option>
                <option value="DEDUCTIONS" ${sectionName === 'DEDUCTIONS' ? 'selected' : ''}>Deductions</option>
                <option value="EMPLOYERS CONTRIBUTION" ${sectionName === 'EMPLOYERS CONTRIBUTION' ? 'selected' : ''}>Employers Contribution</option>
                <option value="LOANS" ${sectionName === 'LOANS' ? 'selected' : ''}>Loans</option>
                <option value="EMPLOYEE BANK DETAILS" ${sectionName === 'EMPLOYEE BANK DETAILS' ? 'selected' : ''}>Employee Bank Details</option>
              </select>
            </div>
            <div class="form-group">
              <label for="add-item-name">Item Name:</label>
              <input type="text" id="add-item-name" placeholder="Enter item name" required>
            </div>
            <div class="form-group">
              <label for="add-format">Format:</label>
              <select id="add-format">
                <option value="[TC]">Text/Characters [TC]</option>
                <option value="[UC]">Uppercase [UC]</option>
                <option value="[TC][.]">Text with Numbers [TC][.]</option>
              </select>
            </div>
            <div class="form-group">
              <label for="add-value-format">Value Format:</label>
              <select id="add-value-format">
                <option value="Text">Text</option>
                <option value="Alphanumeric">Alphanumeric</option>
                <option value="Numeric with decimal places">Numeric with decimal places</option>
              </select>
            </div>
            <div class="form-group">
              <label>
                <input type="checkbox" id="add-include-report" checked>
                Include in Report
              </label>
            </div>
          </form>
        </div>
        <div class="modal-footer">
          <button class="btn btn-secondary" onclick="closeAddModal()">Cancel</button>
          <button class="btn btn-primary" onclick="saveNewItem()">Add Item</button>
        </div>
      </div>
    </div>
  `;

  document.body.insertAdjacentHTML('beforeend', addModalHtml);
}

function closeAddModal() {
  const modal = document.getElementById('add-item-modal');
  if (modal) {
    modal.remove();
  }
}

async function saveNewItem() {
  try {
    const sectionName = document.getElementById('add-section').value;
    const itemName = document.getElementById('add-item-name').value.trim();
    const format = document.getElementById('add-format').value;
    const valueFormat = document.getElementById('add-value-format').value;
    const includeInReport = document.getElementById('add-include-report').checked;

    if (!itemName) {
      showNotification('Item name is required', 'error');
      return;
    }

    // Get current dictionary
    const dictionary = await window.api.getEnhancedDictionary();

    if (!dictionary) {
      showNotification('Failed to load dictionary', 'error');
      return;
    }

    // Ensure section exists
    if (!dictionary[sectionName]) {
      dictionary[sectionName] = { items: {} };
    }

    // Check if item already exists
    if (dictionary[sectionName].items[itemName]) {
      showNotification('Item already exists in this section', 'error');
      return;
    }

    // Add new item
    dictionary[sectionName].items[itemName] = {
      format: format,
      value_format: valueFormat,
      include_in_report: includeInReport,
      is_fixed: true // New items are considered fixed
    };

    // Save dictionary
    const success = await window.api.saveEnhancedDictionary(dictionary);

    if (success) {
      showNotification('Item added successfully', 'success');
      closeAddModal();
      refreshDataBuilderDictionary(); // Refresh the display

      // Emit dictionary update event
      if (window.appEvents) {
        window.appEvents.emit('dictionaryUpdated', dictionary);
      }
    } else {
      showNotification('Failed to save new item', 'error');
    }

  } catch (error) {
    console.error('Error saving new item:', error);
    showNotification('Error adding item: ' + error.message, 'error');
  }
}

async function cancelSpreadsheetBuild() {
  console.log('🛑 Cancelling spreadsheet build...');

  try {
    await window.api.cancelSpreadsheetBuild();
    hideDataBuilderProgress();
    showNotification('Spreadsheet build cancelled', 'info');
  } catch (error) {
    console.error('❌ Error cancelling build:', error);
    showNotification('Error cancelling build', 'error');
  }
}

function initializeDataBuilderAnalytics() {
  console.log('🔧 Initializing Data Builder analytics...');

  const analyticsTabs = document.querySelectorAll('.analytics-tab');
  const downloadBtn = document.getElementById('download-spreadsheet-btn');
  const viewAnalyticsBtn = document.getElementById('view-analytics-btn');
  const viewReportBtn = document.getElementById('view-report-btn');

  // Analytics tab switching
  analyticsTabs.forEach(tab => {
    tab.addEventListener('click', () => {
      const targetTab = tab.dataset.tab;
      switchAnalyticsTab(targetTab);
    });
  });

  // Result action buttons
  if (downloadBtn) {
    downloadBtn.addEventListener('click', downloadSpreadsheet);
  }

  if (viewAnalyticsBtn) {
    viewAnalyticsBtn.addEventListener('click', showAnalyticsSection);
  }

  if (viewReportBtn) {
    viewReportBtn.addEventListener('click', viewReport);
  }
}

function switchAnalyticsTab(targetTab) {
  // Remove active class from all tabs and panels
  document.querySelectorAll('.analytics-tab').forEach(tab => tab.classList.remove('active'));
  document.querySelectorAll('.analytics-panel').forEach(panel => panel.classList.remove('active'));

  // Add active class to selected tab and panel
  document.querySelector(`[data-tab="${targetTab}"]`).classList.add('active');
  document.getElementById(`analytics-${targetTab}`).classList.add('active');

  console.log(`📊 Switched to analytics tab: ${targetTab}`);
}

function showAnalyticsSection() {
  const analyticsSection = document.getElementById('data-builder-analytics-section');

  if (analyticsSection) {
    analyticsSection.style.display = 'block';

    // Load analytics data
    loadAnalyticsData();

    // Scroll to analytics section
    analyticsSection.scrollIntoView({ behavior: 'smooth' });
  }
}

function loadAnalyticsData() {
  console.log('📊 Loading analytics data...');

  // Load sample analytics data for demonstration
  const summaryPanel = document.getElementById('analytics-summary');
  if (summaryPanel) {
    summaryPanel.innerHTML = `
      <div class="analytics-grid">
        <div class="analytics-card">
          <h4>Total Employees</h4>
          <p class="analytics-value">2,847</p>
        </div>
        <div class="analytics-card">
          <h4>Total Gross Salary</h4>
          <p class="analytics-value">GHS 8,542,350.00</p>
        </div>
        <div class="analytics-card">
          <h4>Total Deductions</h4>
          <p class="analytics-value">GHS 1,284,567.50</p>
        </div>
        <div class="analytics-card">
          <h4>Net Payroll</h4>
          <p class="analytics-value">GHS 7,257,782.50</p>
        </div>
      </div>
      <div class="analytics-insights">
        <h4>Key Insights</h4>
        <ul>
          <li>Average salary: GHS 3,001.23 per employee</li>
          <li>Deduction rate: 15.04% of gross salary</li>
          <li>Highest department: Engineering (847 employees)</li>
          <li>Most common allowance: Transport (67% of employees)</li>
        </ul>
      </div>
    `;
  }
}

async function downloadSpreadsheet() {
  console.log('💾 Downloading spreadsheet...');

  if (!window.dataBuilderResult) {
    showNotification('No spreadsheet to download', 'error');
    return;
  }

  try {
    const result = await window.api.downloadSpreadsheet(window.dataBuilderResult.outputPath);
    if (result.success) {
      showNotification('Spreadsheet downloaded successfully', 'success');
    } else {
      throw new Error(result.error || 'Download failed');
    }
  } catch (error) {
    console.error('❌ Error downloading spreadsheet:', error);
    showNotification('Error downloading spreadsheet', 'error');
  }
}

async function viewReport() {
  console.log('👁️ Viewing report...');

  try {
    if (!window.dataBuilderResult || !window.dataBuilderResult.outputPath) {
      showNotification('No report to view', 'error');
      return;
    }

    const result = await window.api.openFile(window.dataBuilderResult.outputPath);

    if (result.success) {
      showNotification('Report opened successfully', 'success');
    } else {
      throw new Error(result.error || 'Failed to open report');
    }
  } catch (error) {
    console.error('❌ Error viewing report:', error);
    showNotification('Error viewing report', 'error');
  }
}

// Bank Adviser functionality
function initializeBankAdviserTab() {
  console.log('🏦 Initializing Bank Adviser tab...');

  // Initialize Bank Adviser sub-tabs
  initializeBankAdviserSubTabs();

  // The Bank Adviser UI is automatically initialized by bank_adviser.js
  // when the script loads, so we just need to ensure the tab switching works

  console.log('✅ Bank Adviser tab initialized successfully');
}

// Initialize Bank Adviser sub-tabs
function initializeBankAdviserSubTabs() {
  const subTabs = document.querySelectorAll('.bank-adviser-tab-btn');
  const subTabContents = document.querySelectorAll('.bank-adviser-subtab-content');

  subTabs.forEach(tab => {
    tab.addEventListener('click', () => {
      const targetSubTab = tab.getAttribute('data-subtab');

      // Remove active class from all sub-tabs and contents
      subTabs.forEach(t => t.classList.remove('active'));
      subTabContents.forEach(content => content.classList.remove('active'));

      // Add active class to clicked tab and corresponding content
      tab.classList.add('active');
      let targetContent;
      if (targetSubTab === 'loan-tracker') {
        targetContent = document.getElementById('loan-tracker-subtab');
      } else {
        targetContent = document.getElementById(`${targetSubTab}-content`);
      }
      if (targetContent) {
        targetContent.classList.add('active');
      }

      console.log(`🏦 Switched to Bank Adviser sub-tab: ${targetSubTab}`);
    });
  });
}

// Real-time extraction listener
function initializeRealTimeExtractionListener() {
  // Listen for real-time extraction updates from backend
  if (window.api && window.api.onRealTimeExtractionUpdate) {
    window.api.onRealTimeExtractionUpdate((updateData) => {
      console.log('Real-time extraction update received:', updateData);

      // Forward to Auto-Learning handler if available
      if (typeof window.handleRealTimeExtractionUpdate === 'function') {
        window.handleRealTimeExtractionUpdate(updateData);
      }

      // Update status message
      if (updateData.message) {
        updateStatusMessage(updateData.message);
      }

      // Add to activity log
      if (updateData.type === 'new_item_found') {
        addActivityItem(`Found: ${updateData.item.label} = ${updateData.item.value}`);
      } else if (updateData.type === 'section_start') {
        addActivityItem(`Processing ${updateData.section} section`);
      } else if (updateData.type === 'extraction_complete') {
        addActivityItem(`Extraction completed with 100% accuracy`);
      }
    });
  } else {
    console.warn('Real-time extraction API not available');
  }
}

// Utility functions
function showNotification(message, type = 'info') {
  // Create notification element
  const notification = document.createElement('div');
  notification.className = `notification ${type}`;
  notification.style.cssText = `
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 15px 20px;
    border-radius: 5px;
    color: white;
    font-weight: 500;
    z-index: 10000;
    max-width: 400px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    animation: slideIn 0.3s ease-out;
  `;

  // Set background color based on type
  const colors = {
    success: '#28a745',
    error: '#dc3545',
    warning: '#ffc107',
    info: '#17a2b8'
  };
  notification.style.backgroundColor = colors[type] || colors.info;

  // Add icon
  const icons = {
    success: 'fas fa-check-circle',
    error: 'fas fa-exclamation-circle',
    warning: 'fas fa-exclamation-triangle',
    info: 'fas fa-info-circle'
  };

  notification.innerHTML = `
    <i class="${icons[type] || icons.info}"></i>
    <span style="margin-left: 10px;">${message}</span>
  `;

  document.body.appendChild(notification);

  // Auto remove after 5 seconds
  setTimeout(() => {
    notification.style.animation = 'slideOut 0.3s ease-in';
    setTimeout(() => {
      if (notification.parentNode) {
        notification.parentNode.removeChild(notification);
      }
    }, 300);
  }, 5000);
}

// Safe dictionary stats loading
async function loadDictionaryStatsSafe() {
  try {
    console.log('📚 Loading dictionary stats safely...');

    const dictCount = document.getElementById('dictionary-count');
    if (!dictCount) {
      console.log('Dictionary count element not found, skipping...');
      return;
    }

    // Set loading state
    dictCount.textContent = 'Loading...';

    // Try to get dictionary stats with timeout
    const timeoutPromise = new Promise((_, reject) =>
      setTimeout(() => reject(new Error('Dictionary stats timeout')), 3000)
    );

    const statsPromise = window.api?.getDictionaryStats?.() || Promise.resolve({ total_items: 0 });

    const stats = await Promise.race([statsPromise, timeoutPromise]);

    if (stats && typeof stats.total_items === 'number') {
      dictCount.textContent = `${stats.total_items} items`;
      dictCount.style.color = '#28a745';
    } else {
      dictCount.textContent = 'Ready';
      dictCount.style.color = '#28a745';
    }

    console.log('✅ Dictionary stats loaded successfully');
  } catch (error) {
    console.warn('⚠️ Dictionary stats loading failed:', error);
    const dictCount = document.getElementById('dictionary-count');
    if (dictCount) {
      dictCount.textContent = 'Ready';
      dictCount.style.color = '#6c757d';
    }
  }
}

async function loadDictionaryStats() {
  try {
    const stats = await window.api.getDictionaryStats();
    const dictionaryCount = document.getElementById('dictionary-count');
    if (dictionaryCount && stats) {
      dictionaryCount.textContent = `${stats.total_items || 0} items`;
      dictionaryCount.style.color = '#28a745';
    }
  } catch (error) {
    console.error('Error loading dictionary stats:', error);
  }
}

// REMOVED: Duplicate loadTabContent function - Using the one at line 804-830 instead

// REMOVED: Duplicate addActivityItem function - Using the improved one above

function updateProcessingStep(stepIndex, message) {
  const step = document.querySelector(`.processing-step:nth-child(${stepIndex})`);
  if (step) {
    step.textContent = message;
  }
}

/**
 * Switch to a different phase in the enhanced workflow
 * @param {number} phaseNumber - The phase number (1-6)
 * @param {string} phaseTitle - The phase title to display
 * @param {string} phaseDescription - Description of the current phase
 */
function switchToEnhancedPhase(phaseNumber, phaseTitle, phaseDescription) {
  console.log(`🔄 Switching to phase ${phaseNumber}: ${phaseTitle}`);
  
  // Map phase number to phase name
  const phaseMap = {
    1: 'extraction',
    2: 'pre-auditing',
    3: 'comparison',
    4: 'tracker-feeding',
    5: 'pre-reporting',
    6: 'report-generation'
  };
  
  const phaseName = phaseMap[phaseNumber] || 'extraction';
  currentProcessPhase = phaseName;
  
  // Update phase indicators
  updatePhaseIndicators(phaseName);
  
  // Update top phase info
  const currentPhaseTitle = document.getElementById('current-phase-title');
  if (currentPhaseTitle) currentPhaseTitle.textContent = phaseTitle.toUpperCase();
  
  const currentPhaseDescription = document.getElementById('current-phase-description');
  if (currentPhaseDescription) currentPhaseDescription.textContent = phaseDescription;
  
  // Update phase icon - UPDATED SEQUENCE
  const currentPhaseIcon = document.getElementById('current-phase-icon');
  if (currentPhaseIcon && currentPhaseIcon.querySelector('i')) {
    const iconClass = {
      'extraction': 'fas fa-file-upload',
      'comparison': 'fas fa-balance-scale',
      'auto-learning': 'fas fa-brain',
      'tracker-feeding': 'fas fa-database',
      'pre-reporting': 'fas fa-filter',
      'report-generation': 'fas fa-file-alt'
    }[phaseName] || 'fas fa-cog';

    currentPhaseIcon.querySelector('i').className = iconClass;
  }
  
  // Add activity item
  addRealTimeActivity(phaseName, `Starting ${phaseTitle} phase: ${phaseDescription}`, 0);
  
  return phaseName;
}

/**
 * Show the pre-reporting phase UI and load comparison data
 * @param {string} description - Description for the pre-reporting phase
 */
function showPreReportingPhase(description) {
  console.log('📊 Showing pre-reporting phase UI...');
  
  // Hide all other content
  const phaseDivs = document.querySelectorAll('.phase-content');
  phaseDivs.forEach(div => {
    if (div.id === 'pre-reporting-content') {
      div.style.display = 'block';
    } else {
      div.style.display = 'none';
    }
  });
  
  // Update progress info
  const currentPhaseTitle = document.getElementById('current-phase-title');
  if (currentPhaseTitle) currentPhaseTitle.textContent = 'PRE-REPORTING';
  
  const currentPhaseDescription = document.getElementById('current-phase-description');
  if (currentPhaseDescription) currentPhaseDescription.textContent = description;
  
  // Update phase icon
  const currentPhaseIcon = document.getElementById('current-phase-icon');
  if (currentPhaseIcon && currentPhaseIcon.querySelector('i')) {
    currentPhaseIcon.querySelector('i').className = 'fas fa-filter';
  }
  
  // Load comparison data for pre-reporting and set up the UI
  loadComparisonDataForPreReporting();
  setupPreReportingEventListeners();
}

/**
 * Set up event listeners for pre-reporting UI action buttons
 */
function setupPreReportingEventListeners() {
  console.log('🔊 Setting up event listeners for pre-reporting UI...');
  
  // Handle the Generate Final Report button click
  const generateFinalReportBtn = document.getElementById('generate-final-report-btn');
  if (generateFinalReportBtn) {
    // Remove any existing event listeners
    generateFinalReportBtn.replaceWith(generateFinalReportBtn.cloneNode(true));
    const newGenerateBtn = document.getElementById('generate-final-report-btn');
    
    newGenerateBtn.addEventListener('click', async () => {
      console.log('📝 Generate Final Report button clicked');
      
      if (!window.interactivePreReporting || !window.interactivePreReporting.getSelectedChanges) {
        showNotification('Error: Pre-reporting data not ready. Please try again.', 'error');
        return;
      }
      
      // Get selected changes from the interactive pre-reporting UI
      const selectedChanges = window.interactivePreReporting.getSelectedChanges();
      console.log(`📊 Selected ${selectedChanges.length} changes for final report`);
      
      if (selectedChanges.length === 0) {
        showNotification('Please select at least one change to include in the final report.', 'warning');
        return;
      }
      
      try {
        // Hide pre-reporting UI
        document.getElementById('pre-reporting-content').style.display = 'none';
        
        // Switch to report generation phase
        console.log('🔄 Switching to Phase 6: Report Generation');
        switchToEnhancedPhase(6, 'Report Generation', 'Creating final audit report with selected changes...');
        
        // Show loading indicator
        const loadingSpinner = document.createElement('div');
        loadingSpinner.className = 'loading-spinner-container';
        loadingSpinner.innerHTML = `
          <div class="loading-spinner"></div>
          <p>Generating final report with ${selectedChanges.length} selected changes...</p>
        `;
        document.querySelector('.phase-content').appendChild(loadingSpinner);
        
        // Add selected changes to report config
        if (!window.reportConfig) {
          window.reportConfig = {};
        }
        window.reportConfig.selected_changes = selectedChanges;
        
        console.log('📈 Generating reports with selected changes:', window.reportConfig);
        const reportResult = await generateReportsWithProgress(window.reportConfig, {
          format: 'all',
          include_signature: true
        });
        
        console.log('📄 Report generation result:', reportResult);
        
        if (reportResult.success) {
          // Show completion
          showEnhancedAuditResults(window.lastEnhancedAuditResult);
          showNotification('Final report generated successfully with selected payroll changes', 'success');
          
          // Record activity with selected change count
          addActivityItem(`Generated final report with ${selectedChanges.length} selected payroll changes`);
        } else {
          throw new Error('Report generation failed: ' + reportResult.error);
        }
      } catch (error) {
        console.error('❌ Error generating final report:', error);
        showNotification('Error generating final report: ' + error.message, 'error');
      }
    });
  }
  
  // Handle reset/cancel button
  const cancelPreReportingBtn = document.getElementById('cancel-pre-reporting-btn');
  if (cancelPreReportingBtn) {
    // Remove any existing event listeners
    cancelPreReportingBtn.replaceWith(cancelPreReportingBtn.cloneNode(true));
    const newCancelBtn = document.getElementById('cancel-pre-reporting-btn');
    
    newCancelBtn.addEventListener('click', () => {
      console.log('🔄 Cancel pre-reporting and reset workflow');
      resetWorkflow();
    });
  }
}

function updateStatusMessage(message) {
  const statusMessage = document.getElementById('status-message');
  if (statusMessage) {
    statusMessage.textContent = message;
  }
}

// Initialize Dictionary Manager placeholder
function initializeDictionaryManager() {
  // Dictionary manager is handled by dictionary_manager.js
  console.log('Dictionary Manager initialized');
}

function onApplicationReady() {
  console.log('🎉 Application ready and fully initialized');

  // Update status
  const statusMessage = document.getElementById('status-message');
  if (statusMessage) {
    statusMessage.textContent = 'Application Ready';
  }

  // Initialize any additional components that need the full UI to be ready
  if (typeof initializeContentSwitching === 'function') {
    initializeContentSwitching();
  }
  
  // Set up all UI event listeners
  setupUIEventListeners();

  // Set up real-time update event listeners
  setupRealTimeEventListeners();

  // Load system status
  loadSystemStatus();
}

// Settings Modal Functions
function openSettingsModal() {
  console.log('🔧 Opening settings modal...');

  // Create settings modal if it doesn't exist
  let settingsModal = document.getElementById('settings-modal');
  if (!settingsModal) {
    settingsModal = createSettingsModal();
    document.body.appendChild(settingsModal);
  }

  // Load current settings
  loadCurrentSettings();

  // Show modal
  settingsModal.style.display = 'block';
}

function createSettingsModal() {
  const modal = document.createElement('div');
  modal.id = 'settings-modal';
  modal.className = 'modal';
  modal.innerHTML = `
    <div class="modal-content settings-modal-content">
      <div class="modal-header">
        <h3><i class="fas fa-cog"></i> Application Settings</h3>
        <span class="close-modal" onclick="closeSettingsModal()">&times;</span>
      </div>
      <div class="modal-body">
        <div class="settings-section">
          <h4><i class="fas fa-check-circle"></i> Perfect Extractor Status</h4>
          <div class="setting-item">
            <div class="status-display">
              <i class="fas fa-check text-success"></i>
              <strong>Perfect Section-Aware Extractor Active</strong>
            </div>
            <small>98-100% accuracy guaranteed on same-structure payslips</small>
          </div>
          <div class="setting-item">
            <div class="status-display">
              <i class="fas fa-brain text-info"></i>
              <strong>Auto-Learning Always Enabled</strong>
            </div>
            <small>All extracted items automatically sent to pending approval</small>
          </div>
          <div class="setting-item">
            <label>
              <input type="checkbox" id="enable-real-time-updates" checked>
              Enable Real-time Updates
            </label>
            <small>Show extraction progress in real-time</small>
          </div>
          <div class="setting-item">
            <label for="batch-threshold">Batch Processing Threshold:</label>
            <input type="number" id="batch-threshold" value="100" min="1" max="1000">
            <small>Number of payslips to process in each batch</small>
          </div>
        </div>

        <div class="settings-section">
          <h4><i class="fas fa-cogs"></i> Performance Settings</h4>
          <div class="setting-item">
            <label>
              <input type="checkbox" id="enable-worker-threads" checked>
              <strong>Enable Worker Threads (Recommended)</strong>
            </label>
            <small>Process payrolls in background threads to keep UI responsive</small>
            <div class="performance-info">
              <div class="performance-benefit">
                <i class="fas fa-check-circle text-success"></i>
                <span>Prevents UI freezing during heavy processing</span>
              </div>
              <div class="performance-benefit">
                <i class="fas fa-check-circle text-success"></i>
                <span>Allows multitasking while processing large payrolls</span>
              </div>
              <div class="performance-benefit">
                <i class="fas fa-check-circle text-success"></i>
                <span>Better memory management and stability</span>
              </div>
            </div>
          </div>
          <div class="setting-item">
            <label for="worker-timeout">Worker Timeout (minutes):</label>
            <input type="number" id="worker-timeout" value="30" min="5" max="120">
            <small>Maximum time to wait for processing before timeout</small>
          </div>
        </div>

        <div class="settings-section">
          <h4><i class="fas fa-file-alt"></i> Report Settings</h4>
          <div class="setting-item">
            <label>
              <input type="checkbox" id="auto-generate-reports" checked>
              Auto-generate Reports
            </label>
            <small>Automatically generate reports after audit completion</small>
          </div>
          <div class="setting-item">
            <label>
              <input type="checkbox" id="include-summary-stats" checked>
              Include Summary Statistics
            </label>
            <small>Add statistical summaries to generated reports</small>
          </div>
        </div>

        <div class="settings-section">
          <h4><i class="fas fa-bell"></i> Notification Settings</h4>
          <div class="setting-item">
            <label>
              <input type="checkbox" id="enable-notifications" checked>
              Enable Notifications
            </label>
            <small>Show system notifications for important events</small>
          </div>
          <div class="setting-item">
            <label>
              <input type="checkbox" id="enable-sound-alerts" checked>
              Enable Sound Alerts
            </label>
            <small>Play sound alerts for completed operations</small>
          </div>
        </div>

        <div class="settings-section">
          <h4><i class="fas fa-broom"></i> Data Management</h4>
          <div class="setting-item">
            <button id="clean-all-data-btn" class="btn danger" onclick="cleanAllAppData()">
              <i class="fas fa-trash-alt"></i> Clean All Data
            </button>
            <small>Remove all data, reports, and reset app to fresh state. This action cannot be undone!</small>
          </div>
          <div class="setting-item">
            <button id="clean-extracted-data-btn" class="btn warning" onclick="cleanExtractedData()">
              <i class="fas fa-database"></i> Clean Extracted Data
            </button>
            <small>Remove all extracted payroll audit data. Keeps reports, settings, and Bank Adviser tracker intact.</small>
          </div>
          <div class="setting-item">
            <button id="reset-auto-learning-btn" class="btn secondary" onclick="resetAutoLearning()">
              <i class="fas fa-brain"></i> Reset Auto-Learning
            </button>
            <small>Clear all pending, approved, and rejected items from Auto-Learning</small>
          </div>
        </div>
      </div>
      <div class="modal-footer">
        <button class="btn secondary" onclick="resetSettingsToDefault()">
          <i class="fas fa-undo"></i> Reset to Defaults
        </button>
        <button class="btn primary" onclick="saveSettings()">
          <i class="fas fa-save"></i> Save Settings
        </button>
      </div>
    </div>
  `;
  return modal;
}

function openHelpModal() {
  console.log('❓ Opening help modal...');

  // Create help modal if it doesn't exist
  let helpModal = document.getElementById('help-modal');
  if (!helpModal) {
    helpModal = createHelpModal();
    document.body.appendChild(helpModal);
  }

  // Show modal
  helpModal.style.display = 'block';
}

function createHelpModal() {
  const modal = document.createElement('div');
  modal.id = 'help-modal';
  modal.className = 'modal';
  modal.innerHTML = `
    <div class="modal-content help-modal-content">
      <div class="modal-header">
        <h3><i class="fas fa-question-circle"></i> Help & Documentation</h3>
        <span class="close-modal" onclick="closeHelpModal()">&times;</span>
      </div>
      <div class="modal-body">
        <div class="help-section">
          <h4><i class="fas fa-play-circle"></i> Getting Started</h4>
          <ol>
            <li><strong>Select Files:</strong> Choose your current and previous month payroll PDF files</li>
            <li><strong>Enter Signature:</strong> Provide your name and designation for the report</li>
            <li><strong>Start Audit:</strong> Click "Start Payroll Audit" to begin the comparison</li>
            <li><strong>Review Results:</strong> Examine the generated comparison reports</li>
          </ol>
        </div>

        <div class="help-section">
          <h4><i class="fas fa-book"></i> Dictionary Manager</h4>
          <p>The Dictionary Manager helps you customize how payroll items are extracted:</p>
          <ul>
            <li><strong>Auto-Learning:</strong> Review and approve automatically detected items</li>
            <li><strong>Fixed Items:</strong> Core payroll items that cannot be deleted</li>
            <li><strong>Variable Items:</strong> Custom items specific to your organization</li>
          </ul>
        </div>

        <div class="help-section">
          <h4><i class="fas fa-cogs"></i> Features</h4>
          <ul>
            <li><strong>Perfect + Hybrid Extraction:</strong> Dual extraction engines for 100% accuracy</li>
            <li><strong>Section-Aware Processing:</strong> Understands payslip structure automatically</li>
            <li><strong>Real-time Progress:</strong> Live updates during processing</li>
            <li><strong>Multiple Report Formats:</strong> Word, PDF, and Excel reports</li>
          </ul>
        </div>

        <div class="help-section">
          <h4><i class="fas fa-exclamation-triangle"></i> Troubleshooting</h4>
          <ul>
            <li><strong>File Not Loading:</strong> Ensure PDF files are not password-protected</li>
            <li><strong>Extraction Errors:</strong> Check that payslips follow standard format</li>
            <li><strong>Missing Items:</strong> Use Dictionary Manager to add custom items</li>
            <li><strong>Performance Issues:</strong> Reduce batch size in settings for large files</li>
          </ul>
        </div>

        <div class="help-section">
          <h4><i class="fas fa-info-circle"></i> System Information</h4>
          <p><strong>Application:</strong> TEMPLAR PAYROLL AUDITOR</p>
          <p><strong>Version:</strong> 2.0.0</p>
          <p><strong>Created By:</strong> SAMUEL ASIEDU</p>
          <p><strong>Extraction Engine:</strong> Perfect Section-Aware + Hybrid Fallback</p>
          <p><strong>Supported Formats:</strong> PDF payroll files</p>
          <p><strong>Output Formats:</strong> Word, PDF, Excel reports</p>
        </div>
      </div>
      <div class="modal-footer">
        <button class="btn secondary" onclick="openUserManual()">
          <i class="fas fa-book-open"></i> User Manual
        </button>
        <button class="btn primary" onclick="closeHelpModal()">
          <i class="fas fa-check"></i> Got It
        </button>
      </div>
    </div>
  `;
  return modal;
}

// Modal control functions
function closeSettingsModal() {
  const modal = document.getElementById('settings-modal');
  if (modal) modal.style.display = 'none';
}

function closeHelpModal() {
  const modal = document.getElementById('help-modal');
  if (modal) modal.style.display = 'none';
}

async function loadCurrentSettings() {
  try {
    console.log('Loading current settings from backend...');

    const response = await window.api.getAppSettings();
    if (response.success && response.settings) {
      const settings = response.settings;

      // Load performance settings
      if (settings.performance) {
        const workerThreadsCheckbox = document.getElementById('enable-worker-threads');
        const workerTimeoutInput = document.getElementById('worker-timeout');
        const batchThresholdInput = document.getElementById('batch-threshold');
        const realTimeUpdatesCheckbox = document.getElementById('enable-real-time-updates');

        if (workerThreadsCheckbox) workerThreadsCheckbox.checked = settings.performance.enableWorkerThreads !== false;
        if (workerTimeoutInput) workerTimeoutInput.value = settings.performance.workerTimeout || 30;
        if (batchThresholdInput) batchThresholdInput.value = settings.performance.batchThreshold || 100;
        if (realTimeUpdatesCheckbox) realTimeUpdatesCheckbox.checked = settings.performance.enableRealTimeUpdates !== false;
      }

      // Load report settings
      if (settings.reports) {
        const autoGenerateCheckbox = document.getElementById('auto-generate-reports');
        const includeSummaryCheckbox = document.getElementById('include-summary-stats');

        if (autoGenerateCheckbox) autoGenerateCheckbox.checked = settings.reports.autoGenerateReports !== false;
        if (includeSummaryCheckbox) includeSummaryCheckbox.checked = settings.reports.includeSummaryStats !== false;
      }

      // Load notification settings
      if (settings.notifications) {
        const notificationsCheckbox = document.getElementById('enable-notifications');
        const soundAlertsCheckbox = document.getElementById('enable-sound-alerts');

        if (notificationsCheckbox) notificationsCheckbox.checked = settings.notifications.enableNotifications !== false;
        if (soundAlertsCheckbox) soundAlertsCheckbox.checked = settings.notifications.enableSoundAlerts !== false;
      }

      console.log('✅ Settings loaded successfully');
    } else {
      console.warn('Failed to load settings, using defaults');
    }
  } catch (error) {
    console.error('Error loading settings:', error);
    showNotification('Failed to load settings', 'error');
  }
}

async function saveSettings() {
  try {
    console.log('Saving settings to backend...');

    // Collect settings from form
    const settings = {
      performance: {
        enableWorkerThreads: document.getElementById('enable-worker-threads')?.checked !== false,
        workerTimeout: parseInt(document.getElementById('worker-timeout')?.value) || 30,
        batchThreshold: parseInt(document.getElementById('batch-threshold')?.value) || 100,
        enableRealTimeUpdates: document.getElementById('enable-real-time-updates')?.checked !== false
      },
      reports: {
        autoGenerateReports: document.getElementById('auto-generate-reports')?.checked !== false,
        includeSummaryStats: document.getElementById('include-summary-stats')?.checked !== false
      },
      notifications: {
        enableNotifications: document.getElementById('enable-notifications')?.checked !== false,
        enableSoundAlerts: document.getElementById('enable-sound-alerts')?.checked !== false
      }
    };

    const response = await window.api.saveAppSettings(settings);
    if (response.success) {
      console.log('✅ Settings saved successfully');
      showNotification('Settings saved successfully! Worker thread changes take effect immediately.', 'success');
      closeSettingsModal();
    } else {
      console.error('Failed to save settings:', response.error);
      showNotification('Failed to save settings: ' + response.error, 'error');
    }
  } catch (error) {
    console.error('Error saving settings:', error);
    showNotification('Error saving settings: ' + error.message, 'error');
  }
}

async function resetSettingsToDefault() {
  if (confirm('Reset all settings to default values? This will enable worker threads and restore all default settings.')) {
    try {
      console.log('Resetting settings to defaults...');

      const response = await window.api.resetAppSettings();
      if (response.success) {
        console.log('✅ Settings reset to defaults');
        showNotification('Settings reset to defaults successfully!', 'success');

        // Reload the settings in the UI
        await loadCurrentSettings();
      } else {
        console.error('Failed to reset settings:', response.error);
        showNotification('Failed to reset settings: ' + response.error, 'error');
      }
    } catch (error) {
      console.error('Error resetting settings:', error);
      showNotification('Error resetting settings: ' + error.message, 'error');
    }
  }
}

function openUserManual() {
  console.log('Opening user manual...');
  showNotification('User manual feature coming soon!', 'info');
}

// Clean All Data Function
async function cleanAllAppData() {
  const confirmMessage = `🚨 WARNING: This will permanently delete ALL data including:

• All reports and generated files
• All Auto-Learning data (pending, approved, rejected items)
• All application settings (reset to defaults)
• All cached data and temporary files

This action CANNOT be undone!

Are you absolutely sure you want to proceed?`;

  if (!confirm(confirmMessage)) {
    return;
  }

  // Create custom confirmation modal for double confirmation
  const doubleConfirm = await showCustomConfirmDialog(
    'Final Confirmation Required',
    'Type "DELETE ALL DATA" exactly to confirm this destructive action:',
    'DELETE ALL DATA'
  );

  if (!doubleConfirm) {
    showNotification('Clean operation cancelled - confirmation text did not match', 'info');
    return;
  }

  try {
    console.log('🧹 Starting clean all data operation...');
    showNotification('Cleaning all application data...', 'info');

    const response = await window.api.cleanAllAppData();
    if (response.success) {
      console.log('✅ All data cleaned successfully');
      showNotification('All application data cleaned successfully! The app will restart with fresh settings.', 'success');

      // Close settings modal
      closeSettingsModal();

      // Restart the application after a short delay
      setTimeout(() => {
        window.location.reload();
      }, 2000);
    } else {
      console.error('Failed to clean data:', response.error);
      showNotification('Failed to clean data: ' + response.error, 'error');
    }
  } catch (error) {
    console.error('Error cleaning data:', error);
    showNotification('Error cleaning data: ' + error.message, 'error');
  }
}

// Reset Auto-Learning Function
async function resetAutoLearning() {
  if (!confirm('Reset Auto-Learning data? This will clear all pending, approved, and rejected items.')) {
    return;
  }

  try {
    console.log('🧠 Resetting Auto-Learning data...');
    showNotification('Resetting Auto-Learning data...', 'info');

    const response = await window.api.resetAutoLearning();
    if (response.success) {
      console.log('✅ Auto-Learning data reset successfully');
      showNotification('Auto-Learning data reset successfully!', 'success');
    } else {
      console.error('Failed to reset Auto-Learning:', response.error);
      showNotification('Failed to reset Auto-Learning: ' + response.error, 'error');
    }
  } catch (error) {
    console.error('Error resetting Auto-Learning:', error);
    showNotification('Error resetting Auto-Learning: ' + error.message, 'error');
  }
}

// Clean Extracted Data Function
async function cleanExtractedData() {
  console.log('🗑️ User requested to clean extracted data');

  // Show confirmation dialog using standard confirm
  const confirmMessage = `🗑️ WARNING: This will permanently delete all extracted payroll audit data including:

• All extracted employee data
• All audit sessions
• All comparison results
• All pre-reporting data

The following will be preserved:
• Reports and settings
• Bank Adviser tracker data
• Auto-learning dictionary

This action CANNOT be undone!

Are you sure you want to proceed?`;

  if (!confirm(confirmMessage)) {
    console.log('🚫 User cancelled clean extracted data operation');
    return;
  }

  try {
    console.log('🗑️ Starting clean extracted data operation...');
    showNotification('Cleaning extracted data...', 'info');

    const response = await window.api.cleanExtractedData({});
    if (response.success) {
      console.log('✅ Extracted data cleaned successfully');
      showNotification('Extracted data cleaned successfully! Database is now clean and ready for new payroll processing.', 'success');
    } else {
      console.error('Failed to clean extracted data:', response.error);
      showNotification('Failed to clean extracted data: ' + response.error, 'error');
    }
  } catch (error) {
    console.error('Error cleaning extracted data:', error);
    showNotification('Error cleaning extracted data: ' + error.message, 'error');
  }
}

// Clean Duplicate Sessions Function (called automatically before payroll audit)
async function cleanDuplicateSessions() {
  try {
    console.log('🧹 Cleaning duplicate audit sessions...');
    const response = await window.api.cleanDuplicateSessions();
    if (response.success) {
      console.log('✅ Duplicate sessions cleaned successfully');
      return true;
    } else {
      console.error('Failed to clean duplicate sessions:', response.error);
      return false;
    }
  } catch (error) {
    console.error('Error cleaning duplicate sessions:', error);
    return false;
  }
}

// Custom Confirmation Dialog Function
function showCustomConfirmDialog(title, message, expectedText) {
  return new Promise((resolve) => {
    // Create modal
    const modal = document.createElement('div');
    modal.className = 'modal';
    modal.style.display = 'block';
    modal.innerHTML = `
      <div class="modal-content" style="max-width: 500px;">
        <div class="modal-header">
          <h3><i class="fas fa-exclamation-triangle text-danger"></i> ${title}</h3>
        </div>
        <div class="modal-body">
          <p>${message}</p>
          <input type="text" id="confirmation-input" class="form-control" placeholder="Type here..." style="margin-top: 15px; padding: 10px; border: 2px solid #dc3545; border-radius: 4px; width: 100%;">
          <small class="text-muted">Expected: "${expectedText}"</small>
        </div>
        <div class="modal-footer">
          <button id="cancel-confirm" class="btn secondary">Cancel</button>
          <button id="proceed-confirm" class="btn danger">Proceed</button>
        </div>
      </div>
    `;

    document.body.appendChild(modal);

    const input = modal.querySelector('#confirmation-input');
    const cancelBtn = modal.querySelector('#cancel-confirm');
    const proceedBtn = modal.querySelector('#proceed-confirm');

    // Focus on input
    input.focus();

    // Handle cancel
    cancelBtn.onclick = () => {
      document.body.removeChild(modal);
      resolve(false);
    };

    // Handle proceed
    proceedBtn.onclick = () => {
      const inputValue = input.value.trim();
      document.body.removeChild(modal);
      resolve(inputValue === expectedText);
    };

    // Handle Enter key
    input.onkeypress = (e) => {
      if (e.key === 'Enter') {
        proceedBtn.click();
      }
    };

    // Handle Escape key
    modal.onkeydown = (e) => {
      if (e.key === 'Escape') {
        cancelBtn.click();
      }
    };
  });
}
// ENHANCED FEATURES INITIALIZATION
// ========================================

// Set up all real-time event listeners for backend updates
function setupRealTimeEventListeners() {
  console.log('🔄 Setting up real-time event listeners for backend updates...');

  // Listen for real-time extraction updates
  const extractionUnsubscribe = window.api.onRealTimeExtractionUpdate((data) => {
    console.log('📊 Received real-time extraction update:', data);
    handleRealTimeExtractionProgress(data);
  });

  // Listen for backend progress updates
  const progressUnsubscribe = window.api.onBackendProgress((data) => {
    console.log('📈 Received backend progress update:', data);
    handleBackendProgress(data);
  });

  // Listen for enhanced progress updates from the new workflow
  const enhancedProgressUnsubscribe = window.api.onEnhancedProgressUpdate((data) => {
    console.log('🚀 Received enhanced progress update:', data);
    handleEnhancedProgressUpdate(data);
  });

  // Listen for phase completion events
  const extractionCompleteUnsubscribe = window.api.onExtractionComplete((data) => {
    console.log('✅ Extraction phase complete:', data);
    updatePhaseIndicator('extraction', 'complete');
    addRealTimeActivity('extraction', 'Extraction completed successfully', 100);
  });

  const preAuditingCompleteUnsubscribe = window.api.onPreAuditingComplete((data) => {
    console.log('✅ Pre-auditing phase complete:', data);
    updatePhaseIndicator('pre-auditing', 'complete');
    addRealTimeActivity('pre-auditing', 'Pre-auditing completed successfully', 100);
  });
  
  const comparisonCompleteUnsubscribe = window.api.onComparisonComplete((data) => {
    console.log('✅ Comparison phase complete:', data);
    updatePhaseIndicator('comparison', 'complete');
    addRealTimeActivity('comparison', 'Comparison completed successfully', 100);
    
    // If we have comparison data, process it with proper prioritization
    if (data && data.changes) {
      const categorizedChanges = window.ContentSwitchingManager ? 
        window.ContentSwitchingManager.categorizeChanges(data.changes) : 
        data.changes.map(change => ({
          ...change,
          priority: determinePriority(change.section),
          isRoutineBulkChange: isRoutineChange(change.item_name),
          includeInReport: true
        }));
        
      // Update UI with categorized changes
      console.log(`📊 Processed ${categorizedChanges.length} changes with priority categorization`);
      
      // Filter out routine bulk changes if ContentSwitchingManager is available
      const significantChanges = categorizedChanges.filter(c => !c.isRoutineBulkChange);
      console.log(`🔍 Identified ${significantChanges.length} significant changes after filtering routine bulk changes`);
    }
  });
  
  const trackerFeedingCompleteUnsubscribe = window.api.onTrackerFeedingComplete((data) => {
    console.log('✅ Tracker feeding phase complete:', data);
    updatePhaseIndicator('tracker-feeding', 'complete');
    addRealTimeActivity('tracker', 'Tracker feeding completed successfully', 100);
    // CRITICAL: Transition to auto-learning phase
    console.log('🔄 Tracker feeding complete - transitioning to AUTO_LEARNING phase');
    updateUIPhase('AUTO_LEARNING', 'Starting auto-learning...', 60);
  });
  
  const autoLearningCompleteUnsubscribe = window.api.onAutoLearningComplete((data) => {
    console.log('✅ Auto-learning phase complete:', data);
    updatePhaseIndicator('auto-learning', 'complete');
    addRealTimeActivity('auto-learning', 'Auto-learning completed successfully', 100);

    // Process any auto-learning suggestions if available
    if (data && data.suggestions) {
      console.log(`🧠 Received ${data.suggestions.length} auto-learning suggestions`);
    }

    // CRITICAL: Transition to pre-reporting phase
    console.log('🔄 Auto-learning complete - transitioning to PRE_REPORTING phase');
    updateUIPhase('PRE_REPORTING', 'Starting pre-reporting...', 80);

    // FALLBACK: Ensure pre-reporting UI loads even if phase transition doesn't trigger it
    setTimeout(() => {
      console.log('🔄 FALLBACK: Ensuring pre-reporting UI loads after auto-learning');
      loadPreReportingUIFromDatabase();
    }, 2000);
  });
  
  const reportGenerationCompleteUnsubscribe = window.api.onReportGenerationComplete((data) => {
    console.log('✅ Report generation phase complete:', data);
    updatePhaseIndicator('report-generation', 'complete');
    addRealTimeActivity('reporting', 'Report generation completed successfully', 100);

    // Show report completion notification
    showNotification('Payroll audit report generated successfully', 'success');

    // CRITICAL: Final phase completion
    console.log('🏁 All phases complete - audit finished');
    updateUIPhase('REPORT_GENERATION', 'Payroll audit completed successfully!', 100);

    // If we have report data, update UI
    if (data && data.reportPath) {
      console.log(`📄 Report generated at: ${data.reportPath}`);
    }
  });
  
  // Listen for database events
  const databaseProgressUnsubscribe = window.api.onDatabaseProgress((data) => {
    console.log('🗄️ Database progress update:', data);
    // Update UI if needed
  });
  
  const databasePerformanceUnsubscribe = window.api.onDatabasePerformance((data) => {
    console.log('⚡ Database performance metrics:', data);
    // Update performance metrics if needed
  });
  
  const databaseErrorUnsubscribe = window.api.onDatabaseError((data) => {
    console.error('❌ Database error:', data);
    showNotification(`Database error: ${data.message || 'Unknown error'}`, 'error');
  });
  
  const migrationProgressUnsubscribe = window.api.onMigrationProgress((data) => {
    console.log('🔄 Migration progress:', data);
    // Update migration progress UI if applicable
  });

  // Store unsubscribe functions to prevent memory leaks
  window.eventUnsubscribers = {
    extractionUnsubscribe,
    progressUnsubscribe,
    enhancedProgressUnsubscribe,
    extractionCompleteUnsubscribe,
    preAuditingCompleteUnsubscribe,
    comparisonCompleteUnsubscribe,
    trackerFeedingCompleteUnsubscribe,
    autoLearningCompleteUnsubscribe,
    reportGenerationCompleteUnsubscribe,
    databaseProgressUnsubscribe,
    databasePerformanceUnsubscribe,
    databaseErrorUnsubscribe,
    migrationProgressUnsubscribe
  };

  console.log('✅ Real-time event listeners set up successfully');
}

// Process real-time updates with proper priority categorization
function processRealTimeUpdate(updateData) {
  if (!updateData || typeof updateData !== 'object') return;

  // Make sure ContentSwitchingManager is available
  if (typeof window.ContentSwitchingManager === 'undefined') {
    console.warn('ContentSwitchingManager not found, using fallback categorization');
    // Simple fallback categorization
    return {
      priority: determinePriority(updateData.section),
      isRoutineBulkChange: isRoutineChange(updateData.item_name),
      formattedChange: formatChange(updateData)
    };
  }

  // Use ContentSwitchingManager to properly categorize changes
  return window.ContentSwitchingManager.categorizeChanges([updateData])[0];
}

// Determine priority based on section
function determinePriority(section) {
  if (!section) return 'Low';
  
  const sectionUpper = section.toUpperCase();
  
  if (sectionUpper.includes('PERSONAL DETAILS') || 
      sectionUpper.includes('EARNINGS') || 
      sectionUpper.includes('DEDUCTIONS')) {
    return 'High';
  } else if (sectionUpper.includes('LOANS') || 
             sectionUpper.includes('EMPLOYEE BANK')) {
    return 'Medium';
  } else {
    return 'Low';
  }
}

// Check if this is a routine bulk change that should be filtered
function isRoutineChange(itemName) {
  if (!itemName) return false;
  
  const upperName = itemName.toUpperCase();
  
  return upperName.includes('STAFF CREDIT UNION') || 
         upperName.includes('ROUTINE ADJUSTMENT') ||
         upperName.includes('STANDARD CONTRIBUTION');
}

// Format change with professional terminology
function formatChange(change) {
  if (!change) return '';
  
  const employeeInfo = change.employee_id ? 
    `${change.employee_id}-${change.employee_name || ''}` : 
    (change.employee_name || 'Unknown Employee');
  
  const itemName = change.item_name || 'Unknown Item';
  const previousValue = change.previous_value || '0';
  const currentValue = change.current_value || '0';
  let changeType, difference;
  
  // Calculate difference for numeric values
  if (!isNaN(previousValue) && !isNaN(currentValue)) {
    const prev = parseFloat(previousValue);
    const curr = parseFloat(currentValue);
    difference = curr - prev;
    
    if (prev === 0) {
      changeType = 'introduced'; // Using professional terminology
    } else {
      changeType = 'changed'; // Using professional terminology
      if (difference > 0) {
        difference = `increase ${Math.abs(difference)}`;
      } else if (difference < 0) {
        difference = `decrease ${Math.abs(difference)}`;
      } else {
        difference = 'no change';
      }
    }
  } else {
    // For non-numeric values
    if (!previousValue || previousValue === '0' || previousValue === 'N/A') {
      changeType = 'introduced';
      difference = '';
    } else {
      changeType = 'changed';
      difference = '';
    }
  }
  
  const month1 = change.previous_month || 'Previous Period';
  const month2 = change.current_month || 'Current Period';
  
  return `${employeeInfo}: ${itemName} ${changeType} from ${previousValue} in ${month1} to ${currentValue} in ${month2}${difference ? ': ' + difference : ''}`;
}

// CRITICAL FIX: Ensure ContentSwitchingManager is always available
console.log('🔧 Ensuring ContentSwitchingManager is available...');

// Create the fallback ContentSwitchingManager class
window.ContentSwitchingManager = class ContentSwitchingManager {
  constructor() {
    console.log('✅ Fallback ContentSwitchingManager created');
    this.initialized = false;

    // CRITICAL: Add phases array to match real ContentSwitchingManager
    this.phases = [
      'extraction',
      'comparison',
      'auto-learning',
      'tracker-feeding',
      'pre-reporting',
      'report-generation'
    ];

    this.currentPhase = null;
  }

  initialize() {
    console.log('✅ Fallback ContentSwitchingManager initialized');
    this.initialized = true;
    return true;
  }
    
    // Categorize changes with priority, professional terminology, and routine change filtering
    static categorizeChanges(changes) {
      console.log(`📊 Categorizing ${changes.length} payroll changes with priority classification`);
      
      return changes.map(change => {
        // Determine priority based on section
        const priority = this.determinePriorityBySection(change.section);
        
        // Check if this is a routine bulk change that should be filtered
        const isRoutineBulkChange = this.isRoutineChange(change.item_name, change.section);
        
        // Use professional terminology for change description
        const description = this.getChangeDescription(change);
        
        // Calculate proper numeric differences
        const difference = this.calculateDifference(change.previous_value, change.current_value);
        
        // Determine if the change should be included in the final report
        // High and Moderate changes are included unless they are routine bulk changes
        const includeInReport = (priority === 'High' || priority === 'Moderate') && !isRoutineBulkChange;
        
        return {
          ...change,
          priority,
          isRoutineBulkChange,
          description,
          formattedDifference: difference,
          includeInReport,
          // Add timestamp for tracking
          processedAt: new Date().toISOString()
        };
      });
    }
    
    // Determine priority based on section according to business rules
    static determinePriorityBySection(section) {
      if (!section) return 'Low';
      
      const sectionLower = section.toLowerCase();
      
      // High priority sections
      if (sectionLower.includes('personal details') || 
          sectionLower.includes('earnings') || 
          sectionLower.includes('deductions')) {
        return 'High';
      }
      
      // Moderate priority sections
      if (sectionLower.includes('loans') || 
          sectionLower.includes('employee bank details')) {
        return 'Moderate';
      }
      
      // Everything else is low priority
      return 'Low';
    }
    
    // Check if a change is a routine bulk change that should be filtered
    static isRoutineChange(itemName, section) {
      if (!itemName) return false;
      
      const itemNameLower = itemName.toLowerCase();
      
      // List of known routine bulk changes that should be filtered
      const routinePatterns = [
        'staff credit union',
        'credit union',
        'staff association',
        'routine adjustment',
        'monthly adjustment',
        'standard deduction'
      ];
      
      return routinePatterns.some(pattern => itemNameLower.includes(pattern.toLowerCase()));
    }
    
    // Use professional terminology for change descriptions
    static getChangeDescription(change) {
      if (!change) return '';
      
      // If previous value is empty/null/undefined and current value exists = new item "introduced"
      if ((!change.previous_value || change.previous_value === '' || 
           change.previous_value === '0' || change.previous_value === 0) && 
          change.current_value && change.current_value !== '' && 
          change.current_value !== '0' && change.current_value !== 0) {
        return `${change.item_name} introduced in ${change.current_month || 'current'} period`;
      }
      
      // If current value is empty/null/undefined but previous value exists = item removed
      if ((!change.current_value || change.current_value === '' || 
           change.current_value === '0' || change.current_value === 0) && 
          change.previous_value && change.previous_value !== '' && 
          change.previous_value !== '0' && change.previous_value !== 0) {
        return `${change.item_name} removed in ${change.current_month || 'current'} period`;
      }
      
      // Otherwise it's a modification to existing item = "changed"
      return `${change.item_name} changed from ${change.previous_value} in ${change.previous_month || 'previous'} to ${change.current_value} in ${change.current_month || 'current'}`;
    }
    
    // Calculate and format numeric differences
    static calculateDifference(previousValue, currentValue) {
      // Handle non-numeric values
      if (!previousValue || !currentValue) return '';
      
      let prevNum = this.extractNumericValue(previousValue);
      let currNum = this.extractNumericValue(currentValue);
      
      // If extraction failed, return empty string
      if (prevNum === null || currNum === null) {
        return '';
      }
      
      // Calculate difference
      const difference = currNum - prevNum;
      
      // Format as currency if the values appear to be monetary
      const isCurrency = (String(previousValue).includes('$') || 
                        String(previousValue).includes('£') || 
                        String(previousValue).includes('€') || 
                        String(currentValue).includes('$') || 
                        String(currentValue).includes('£') || 
                        String(currentValue).includes('€'));
      
      if (isCurrency) {
        return difference > 0 ? 
          `increase ${difference.toFixed(2)}` : 
          `decrease ${Math.abs(difference).toFixed(2)}`;
      }
      
      // For non-currency values
      return difference > 0 ? 
        `increase ${difference}` : 
        `decrease ${Math.abs(difference)}`;
    }
    
    // Helper method to extract numeric values from strings
    static extractNumericValue(value) {
      if (typeof value === 'number') return value;
      if (!value) return null;
      
      // Try to extract numeric value from string
      const numericString = String(value).replace(/[^0-9.-]/g, '');
      const parsedValue = parseFloat(numericString);
      
      return isNaN(parsedValue) ? null : parsedValue;
    }

    // CRITICAL FIX: Add missing switchToPhase method to fallback class
    switchToPhase(phase, data = {}) {
      console.log(`🔄 Fallback ContentSwitchingManager switching to phase: ${phase}`);

      // PHASE VALIDATION: Check if phase is valid
      if (!this.phases.includes(phase)) {
        console.error(`❌ Fallback: Invalid phase '${phase}'. Valid phases:`, this.phases);
        return false;
      }

      // Update current phase tracking
      this.currentPhase = phase;

      // Direct phase switching implementation
      const allPhaseContent = document.querySelectorAll('.phase-content, .content-panel');
      allPhaseContent.forEach(el => el.classList.remove('active'));

      // Try to find the target phase content
      let targetElement = document.getElementById(`${phase}-panel`) ||
                         document.getElementById(`${phase}-content`);

      if (targetElement) {
        targetElement.classList.add('active');
        console.log(`✅ Fallback: Switched to ${phase} phase successfully`);

        // Dispatch phase change event for other components
        const phaseChangeEvent = new CustomEvent('phaseChanged', {
          detail: { phase: phase, data: data }
        });
        document.dispatchEvent(phaseChangeEvent);

        return true;
      } else {
        console.warn(`⚠️ Fallback: No content found for phase: ${phase}`);
        return false;
      }
    }

    // Add initialize method if missing
    initialize() {
      console.log('✅ Fallback ContentSwitchingManager initialized');
      this.initialized = true;
      return true;
    }

    // Add method to handle pre-reporting phase initialization
    initializePreReportingPhase(data = {}) {
      console.log('📋 Fallback: Initializing pre-reporting phase');

      // Switch to pre-reporting phase
      this.switchToPhase('pre-reporting');

      // Update any status indicators
      const phaseStatus = document.querySelector('#pre-reporting-panel .panel-status');
      if (phaseStatus) {
        phaseStatus.textContent = 'Ready for Review';
        phaseStatus.className = 'panel-status status-active';
      }

      return true;
    }

    // Add method to get current phase
    getCurrentPhase() {
      // Return tracked current phase if available
      if (this.currentPhase) {
        return this.currentPhase;
      }

      // Fallback: Simple detection based on active elements
      const activePanel = document.querySelector('.phase-content.active, .content-panel.active');
      if (activePanel) {
        const id = activePanel.id;
        if (id.includes('pre-reporting')) return 'pre-reporting';
        if (id.includes('extraction')) return 'extraction';
        if (id.includes('comparison')) return 'comparison';
        if (id.includes('tracker')) return 'tracker-feeding';
        if (id.includes('auto-learning')) return 'auto-learning';
        if (id.includes('report-generation')) return 'report-generation';
      }
      return null;
    }

    // Add method to get all phases (compatibility with real CSM)
    getPhases() {
      return [...this.phases]; // Return copy of phases array
    }

    // Add method to get phase index (compatibility with real CSM)
    getPhaseIndex(phase) {
      return this.phases.indexOf(phase);
    }

    // Add method to get next phase
    getNextPhase(currentPhase = null) {
      const phase = currentPhase || this.currentPhase;
      if (!phase) return null;

      const currentIndex = this.getPhaseIndex(phase);
      if (currentIndex === -1 || currentIndex === this.phases.length - 1) {
        return null; // Invalid phase or last phase
      }

      return this.phases[currentIndex + 1];
    }

    // Add method to check if phase is valid
    isValidPhase(phase) {
      return this.phases.includes(phase);
    }

    // COMPATIBILITY: Add methods that other parts of the system call

    // Update phase indicators (simplified version)
    updatePhaseIndicators(activePhase) {
      console.log(`📊 Fallback: Updating phase indicators for ${activePhase}`);

      const phaseSteps = document.querySelectorAll('.phase-step');
      phaseSteps.forEach(step => {
        const stepPhase = step.getAttribute('data-phase');
        const phaseIndex = this.phases.indexOf(stepPhase);
        const activeIndex = this.phases.indexOf(activePhase);

        // Remove all status classes
        step.classList.remove('active', 'completed', 'pending');

        // Add appropriate status class
        if (stepPhase === activePhase) {
          step.classList.add('active');
        } else if (phaseIndex < activeIndex) {
          step.classList.add('completed');
        } else {
          step.classList.add('pending');
        }
      });
    }

    // Update progress for phase (simplified version)
    updateProgressForPhase(phase) {
      console.log(`📈 Fallback: Updating progress for ${phase}`);

      // Simple progress mapping
      const progressMap = {
        'extraction': 20,
        'comparison': 35,
        'auto-learning': 50,
        'tracker-feeding': 65,
        'pre-reporting': 80,
        'report-generation': 100
      };

      const progress = progressMap[phase] || 0;

      // Update any progress bars found
      const progressBars = document.querySelectorAll('.progress');
      progressBars.forEach(bar => {
        bar.style.width = `${progress}%`;
      });

      return progress;
    }

    // Perform phase transition (simplified version)
    performPhaseTransition(phase, data = {}) {
      console.log(`🎯 Fallback: Performing transition to ${phase}`);

      // Update phase indicators
      this.updatePhaseIndicators(phase);

      // Update progress
      this.updateProgressForPhase(phase);

      // Initialize phase-specific UI
      this.initializePhaseUI(phase, data);

      return true;
    }

    // Initialize phase-specific UI (simplified version)
    initializePhaseUI(phase, data = {}) {
      console.log(`🔧 Fallback: Initializing UI for ${phase}`);

      switch(phase) {
        case 'pre-reporting':
          this.initializePreReportingPhase(data);
          break;
        case 'report-generation':
          console.log('📊 Fallback: Report generation phase initialized');
          break;
        default:
          console.log(`📋 Fallback: Default initialization for ${phase}`);
      }
    }
  };

  // CRITICAL: Create the instance immediately after class definition
  console.log('🔧 Creating ContentSwitchingManager instance...');
  window.contentSwitchingManager = new window.ContentSwitchingManager();
  window.contentSwitchingManager.initialize();
  console.log('✅ ContentSwitchingManager instance created and initialized');
  console.log('📋 switchToPhase method available:', typeof window.contentSwitchingManager.switchToPhase === 'function');

  console.log('✅ Fallback ContentSwitchingManager class defined and instantiated');

// 🎯 DIAGNOSTIC: Manual Pre-Reporting Test Function
window.testPreReportingUIDirect = async function() {
  console.log('🧪 MANUAL PRE-REPORTING UI TEST');
  console.log('=' * 50);

  try {
    // Step 1: Check if we can switch to pre-reporting phase
    console.log('1. 🔄 Testing direct phase switching...');
    if (typeof window.switchToPreReportingPhaseDirect === 'function') {
      window.switchToPreReportingPhaseDirect();
      console.log('   ✅ Phase switching successful');
    } else {
      console.log('   ❌ switchToPreReportingPhaseDirect not available');
      return;
    }

    // Step 2: Check if we can load data
    console.log('2. 📊 Testing data loading...');
    if (window.api && window.api.getLatestPreReportingData) {
      const response = await window.api.getLatestPreReportingData();
      console.log('   📊 Data response:', response);

      if (response && response.success && response.data) {
        console.log('   ✅ Data loaded successfully:', response.data.length, 'records');

        // Step 3: Test UI initialization
        console.log('3. 🎨 Testing UI initialization...');
        window.initializeInteractivePreReporting(response.data);

      } else {
        console.log('   ⚠️ No data available, testing with mock data...');

        // Create mock data for testing
        const mockData = [
          {
            employee_no: 'TEST001',
            employee_name: 'Test Employee 1',
            change_type: 'NEW',
            section: 'EARNINGS',
            item_name: 'Test Allowance',
            current_value: '1000',
            previous_value: '0'
          },
          {
            employee_no: 'TEST002',
            employee_name: 'Test Employee 2',
            change_type: 'INCREASED',
            section: 'DEDUCTIONS',
            item_name: 'Test Deduction',
            current_value: '500',
            previous_value: '300'
          }
        ];

        console.log('   🧪 Using mock data for testing...');
        window.initializeInteractivePreReporting(mockData);
      }
    } else {
      console.log('   ❌ API not available for data loading');
    }

  } catch (error) {
    console.error('❌ Test failed:', error);
  }
};

console.log('🧪 Manual test function available: window.testPreReportingUIDirect()');

function initializeEnhancedFeatures() {
  console.log('🔄 Initializing enhanced features - professionally audited');
  
  // CRITICAL: Initialize in correct order with error handling
  try {
    // Simplified initialization without content switching manager
    initializeEnhancedIpcListeners();
    initializeEnhancedUI();

    console.log('✅ Enhanced features initialized successfully');
  } catch (error) {
    console.error('❌ Error initializing enhanced features:', error);
    // Create a minimal implementation to prevent fatal errors
    initializeEnhancedUI(); // Fallback to basic UI initialization
  }
}

// Simplified initialization - no content switching manager needed

// Removed initializeContentSwitching - using direct phase transitions

function initializeEnhancedIpcListeners() {
  console.log('📡 Setting up enhanced IPC listeners...');

  // Clear any existing listeners first
  if (window.api.clearEnhancedListeners) {
    window.api.clearEnhancedListeners();
    console.log('🧹 Cleared existing enhanced listeners');
  }

  // Enhanced progress updates
  window.api.onEnhancedProgressUpdate((data) => {
    console.log(`📊 [${data.type || data.phase}] ${data.percentage || 'N/A'}% - ${data.message}`);

    // Handle different progress update types
    if (data.type === 'extraction_progress') {
      updateExtractionProgress(data.percentage, data.message, data.employees_extracted || data.employee_count || 0);
    } else if (data.type === 'batch_progress') {
      updateBatchProgress(data.message);
    } else if (data.type === 'batch_performance') {
      updatePerformanceMetrics(data);
    } else if (data.type === 'performance_info') {
      updatePerformanceInfo(data);
    } else if (data.type === 'extraction_complete') {
      updateExtractionComplete(data);
    } else if (data.type === 'pre_reporting_data_ready') {
      console.log('📊 Pre-reporting data ready from backend:', data);

      // Emit the event for the content switching manager
      if (window.appEvents) {
        window.appEvents.emit('pre-reporting-data-ready', data);
      }
    }

    // Update phase progress in UI directly
    if (data.phase) {
      updateUIPhase(data.phase, data.message, data.percentage);
    }

    // Update progress bar and status
    if (data.percentage) {
      updateProgressBar(data.percentage);
    }
    updateProcessingText(data.message);

    // Update current operation text
    const currentOperation = document.getElementById('current-operation');
    const operationDetails = document.getElementById('operation-details');

    if (currentOperation) {
      currentOperation.textContent = `Phase: ${data.phase}`;
    }

    if (operationDetails) {
      operationDetails.textContent = data.message;
    }

    // Update progress percentage and status
    const progressPercentage = document.getElementById('progress-percentage');
    const progressStatus = document.getElementById('progress-status');

    if (progressPercentage) {
      progressPercentage.textContent = `${data.percentage}%`;
    }

    if (progressStatus) {
      progressStatus.textContent = data.message;
    }

    // Update current phase
    currentProcessPhase = data.phase;

    // Switch to appropriate phase in UI
    if (data.phase === 'EXTRACTION') {
      switchToEnhancedPhase(2, 'Data Extraction', data.message);
    } else if (data.phase === 'COMPARISON') {
      switchToEnhancedPhase(3, 'Comparison & Analysis', data.message);
    } else if (data.phase === 'PRE_REPORTING') {
      switchToEnhancedPhase(4, 'Pre-reporting', data.message);
    } else if (data.phase === 'REPORT_GENERATION') {
      switchToEnhancedPhase(5, 'Report Generation', data.message);
    }
  });

  // Enhanced log updates
  window.api.onEnhancedLogUpdate((data) => {
    console.log(`📝 [${data.type.toUpperCase()}] ${data.message}`);

    // Check for REALTIME_UPDATE messages in logs (backup method)
    if (data.message && data.message.includes('REALTIME_UPDATE:')) {
      try {
        const updateJson = data.message.split('REALTIME_UPDATE:')[1];
        const updateData = JSON.parse(updateJson);
        console.log(`🔄 [BACKUP-PROGRESS] ${updateData.type}: ${updateData.message}`);

        // Process the update through our handlers
        if (updateData.type === 'extraction_progress') {
          updateExtractionProgress(updateData.percentage, updateData.message, updateData.employees_extracted || updateData.employee_count || 0);
        } else if (updateData.type === 'batch_progress') {
          updateBatchProgress(updateData.message);
        } else if (updateData.type === 'batch_performance') {
          updatePerformanceMetrics(updateData);
        } else if (updateData.type === 'performance_info') {
          updatePerformanceInfo(updateData);
        } else if (updateData.type === 'extraction_complete') {
          updateExtractionComplete(updateData);
        }
      } catch (e) {
        // Not a JSON update, ignore
      }
    }

    // Check for pre-reporting data
    if (data.message.includes('Pre-reporting data ready') && data.data) {
      console.log('📊 Triggering pre-reporting data ready event');
      window.appEvents.emit('pre-reporting-data-ready', data.data);
    }

    // Display in UI if needed
    displayEnhancedLog(data);
  });

  // Enhanced phase updates
  window.api.onEnhancedPhaseUpdate((data) => {
    console.log(`🔄 Phase ${data.phase}: ${data.status}`);

    if (data.status === 'active') {
      updateUIPhase(data.phase, `Phase ${data.phase} is now active`, 0);
    }
  });

  // Pre-reporting specific events
  window.appEvents.on('pre-reporting-data-ready', (data) => {
    console.log('📊 Pre-reporting data ready:', data);
    console.log('📊 Content switching manager exists:', !!window.contentSwitchingManager);
    console.log('📊 Interactive pre-reporting exists:', !!window.interactivePreReporting);

    // Switch to pre-reporting phase directly
    updateUIPhase('PRE_REPORTING', 'Pre-reporting data ready for review', 64);
  });



  window.appEvents.on('pre-reporting-complete', (data) => {
    console.log('✅ Pre-reporting completed with selections:', data);

    // Continue to report generation phase
    proceedToReportGeneration(data);
  });

  // Debug function to check tracker data
  window.checkTrackerData = async function() {
    try {
      console.log('🔍 CHECKING TRACKER DATA...');

      // Check in-house loans
      const inHouseResult = await window.api.callTrackerOperation('get_tracker_data', 'in_house_loans');
      console.log('📊 In-House Loans:', inHouseResult.success ? `${inHouseResult.data.length} records` : 'Error');

      // Check external loans
      const externalResult = await window.api.callTrackerOperation('get_tracker_data', 'external_loans');
      console.log('📊 External Loans:', externalResult.success ? `${externalResult.data.length} records` : 'Error');

      // Check motor vehicles
      const vehicleResult = await window.api.callTrackerOperation('get_tracker_data', 'motor_vehicles');
      console.log('📊 Motor Vehicles:', vehicleResult.success ? `${vehicleResult.data.length} records` : 'Error');

      return {
        inHouse: inHouseResult,
        external: externalResult,
        vehicles: vehicleResult
      };
    } catch (error) {
      console.error('❌ Error checking tracker data:', error);
      return { error: error.message };
    }
  };

  // Enhanced tracker updates
  window.api.onEnhancedTrackerUpdate((data) => {
    console.log(`🏦 Tracker ${data.component}: ${data.status}`);
    updateTrackerStatus(data.component, data.status, data.details);
  });

  // Enhanced learning updates
  window.api.onEnhancedLearningUpdate((data) => {
    console.log(`🧠 Learning ${data.component}: ${data.status}`);
    updateLearningStatus(data.component, data.status, data.details);
  });

  // Pre-reporting data ready events
  window.api.onPreReportingDataReady((data) => {
    console.log('📊 Pre-reporting data received from backend:', data);

    // Trigger the pre-reporting data ready event for UI
    if (window.appEvents) {
      window.appEvents.emit('pre-reporting-data-ready', data);
    }

    // Switch to pre-reporting phase directly
    updateUIPhase('PRE_REPORTING', 'Pre-reporting data ready', 64);
  });

  // PRODUCTION: Process waiting for user events
  window.api.onProcessWaitingForUser((data) => {
    console.log('⏳ Process waiting for user interaction:', data);

    if (data.phase === 'PRE_REPORTING') {
      console.log('📋 PRE-REPORTING phase waiting for user - loading interactive UI');

      // Update UI to show waiting state
      updateUIPhase('PRE_REPORTING', 'Ready for user review', 80);

      // Load interactive pre-reporting UI
      setTimeout(() => {
        loadPreReportingUIFromDatabase();
      }, 1000);
    }
  });

  // Content switching ready
  window.api.onContentSwitchingReady((data) => {
    console.log('📋 Content switching ready:', data);
  });
}

function initializeEnhancedUI() {
  console.log('🎨 Initializing enhanced UI components...');

  // Enhanced audit functionality is now integrated into the main system

  // Add enhanced status indicators
  addEnhancedStatusIndicators();
}

// Enhanced audit functionality is now integrated into the main audit button
// No separate enhanced button needed

function addEnhancedStatusIndicators() {
  // Add enhanced status indicators to the UI
  const statusContainer = document.createElement('div');
  statusContainer.id = 'enhanced-status-container';
  statusContainer.className = 'enhanced-status-container';
  statusContainer.style.display = 'none';
  statusContainer.innerHTML = `
    <div class="enhanced-status-header">
      <h4>🚀 Enhanced Processing Status</h4>
      <div class="enhanced-status-phase" id="enhanced-current-phase">Initializing...</div>
    </div>
    <div class="enhanced-status-details" id="enhanced-status-details">
      <!-- Status details will be populated here -->
    </div>
  `;

  // Insert into processing status area
  const processingStatus = document.getElementById('processing-status');
  if (processingStatus) {
    processingStatus.appendChild(statusContainer);
  }
}

// Enhanced payroll audit functionality is now integrated into the main startPayrollAuditProcess function

function showEnhancedProcessingStatus() {
  const statusContainer = document.getElementById('enhanced-status-container');
  if (statusContainer) {
    statusContainer.style.display = 'block';
  }

  // Switch to extraction phase
  updateUIPhase('EXTRACTION', 'Starting data extraction process...', 0);
}

function hideEnhancedProcessingStatus() {
  const statusContainer = document.getElementById('enhanced-status-container');
  if (statusContainer) {
    statusContainer.style.display = 'none';
  }
}

function displayEnhancedLog(logData) {
  const detailsContainer = document.getElementById('enhanced-status-details');
  if (detailsContainer) {
    const logEntry = document.createElement('div');
    logEntry.className = `enhanced-log-entry log-${logData.type}`;
    logEntry.innerHTML = `
      <span class="log-timestamp">${new Date(logData.timestamp).toLocaleTimeString()}</span>
      <span class="log-message">${logData.message}</span>
    `;

    detailsContainer.appendChild(logEntry);

    // Keep only last 20 log entries
    while (detailsContainer.children.length > 20) {
      detailsContainer.removeChild(detailsContainer.firstChild);
    }

    // Scroll to bottom
    detailsContainer.scrollTop = detailsContainer.scrollHeight;
  }
}

function updateTrackerStatus(component, status, details) {
  // Update tracker status in UI
  const statusElement = document.getElementById(`${component}-status`);
  if (statusElement) {
    statusElement.textContent = status.charAt(0).toUpperCase() + status.slice(1);
    statusElement.className = `progress-status status-${status}`;
  }
}

function updateLearningStatus(component, status, details) {
  // Update learning status in UI
  const statusElement = document.getElementById(`${component}-status`);
  if (statusElement) {
    statusElement.textContent = status.charAt(0).toUpperCase() + status.slice(1);
    statusElement.className = `progress-status status-${status}`;
  }
}

function proceedToReportGeneration(auditData) {
  console.log('🔍 Proceeding to pre-reporting phase for payroll change selection...');
  
  // Store audit data for later use in report generation
  window.lastEnhancedAuditResult = auditData;

  // Add activity log entry
  addRealTimeActivity('pre-reporting', 'Starting Pre-Reporting phase: Interactive change selection', 1);
  
  // Switch to pre-reporting phase
  updateUIPhase('PRE_REPORTING', 'Starting pre-reporting phase...', 64);
  
  // Update phase indicators to show Phase 5: Pre-Reporting
  switchToEnhancedPhase(5, 'Pre-Reporting', 'Select significant payroll changes for the final report');
  
  // Show pre-reporting UI and load comparison data
  showPreReportingPhase('Select significant payroll changes for the final report');
  
  // Update status message
  updateStatusMessage('Please review and select the payroll changes to include in the final report');
  
  console.log('✅ Transition to pre-reporting phase complete');
}

async function generateActualReports(preReportingData) {
  const progressFill = document.querySelector('#report-results .progress-fill');
  const statusElement = document.querySelector('#report-results .generation-status');

  try {
    // Update progress: Preparing data
    if (progressFill) progressFill.style.width = '10%';
    if (statusElement) statusElement.textContent = 'Preparing report data...';

    // PRODUCTION STANDARD: Load comparison data from single source of truth
    console.log('🔍 REPORT GENERATION DEBUG:');
    console.log('   lastEnhancedAuditResult exists:', !!lastEnhancedAuditResult);
    console.log('   lastEnhancedAuditResult.success:', lastEnhancedAuditResult?.success);
    console.log('   lastEnhancedAuditResult keys:', lastEnhancedAuditResult ? Object.keys(lastEnhancedAuditResult) : 'None');

    if (!lastEnhancedAuditResult || !lastEnhancedAuditResult.success) {
      throw new Error('No audit data available - run payroll audit first');
    }

    // PRODUCTION FIX: Load comparison data from single source of truth (temp_report_data.json)
    console.log('   🔍 Loading comparison data from single source of truth...');

    let comparisonResults = null;
    try {
      // Try to load from single source file first
      const singleSourceData = await window.api.loadSingleSourceComparisonData();

      if (singleSourceData && singleSourceData.success && singleSourceData.data) {
        const reportData = singleSourceData.data;
        comparisonResults = reportData.comparisonData?.comparison_results;
        console.log('   ✅ Loaded from single source:', comparisonResults ? comparisonResults.length : 'NO DATA');
      }
    } catch (error) {
      console.log('   ⚠️ Single source load failed:', error.message);
    }

    // Fallback: Try to get from enhanced audit result (legacy)
    if (!comparisonResults && lastEnhancedAuditResult.comparison_results) {
      comparisonResults = lastEnhancedAuditResult.comparison_results;
      console.log('   ⚠️ Using fallback from audit result:', comparisonResults.length);
    }

    if (!comparisonResults || comparisonResults.length === 0) {
      throw new Error('No comparison data found - ensure payroll audit completed successfully and comparison data was saved');
    }

    const reportData = {
      id: `report_${Date.now()}`,
      timestamp: new Date().toISOString(),
      current_month: currentMonth,
      current_year: currentYear,
      previous_month: previousMonth,
      previous_year: previousYear,
      report_name: signatureName || 'System Administrator',
      report_designation: signatureDesignation || 'Payroll Auditor',
      report_type: reportType || 'traditional',
      source_tab: 'payroll_audit',
      preReportingSelections: preReportingData,
      // EXACT STRUCTURE: comparisonData.comparison_results (array of employee changes)
      comparisonData: {
        comparison_results: comparisonResults,
        summary: lastEnhancedAuditResult.summary_stats || {}
      }
    };

    console.log('🔍 REPORT DATA VALIDATION:');
    console.log('   Comparison results count:', reportData.comparisonData.comparison_results.length);
    console.log('   Summary stats:', reportData.comparisonData.summary);
    console.log('   Sample change:', reportData.comparisonData.comparison_results[0] || 'None');

    // Update progress: Generating reports
    if (progressFill) progressFill.style.width = '50%';
    if (statusElement) statusElement.textContent = 'Generating report documents...';

    // Call the backend to generate reports
    const reportResult = await window.api.generateReport(reportData, {
      format: 'all',
      include_signature: true
    });

    // Update progress: Finalizing
    if (progressFill) progressFill.style.width = '90%';
    if (statusElement) statusElement.textContent = 'Finalizing reports...';

    if (reportResult.success) {
      // Update progress: Complete
      if (progressFill) progressFill.style.width = '100%';
      if (statusElement) statusElement.textContent = 'Reports generated successfully!';

      // Show final results
      setTimeout(() => {
        showFinalReportResults(preReportingData);
      }, 1000);
    } else {
      throw new Error(reportResult.error || 'Report generation failed');
    }

  } catch (error) {
    console.error('❌ Report generation failed:', error);
    if (statusElement) {
      statusElement.textContent = `Error: ${error.message}`;
      statusElement.style.color = '#dc3545';
    }
    showNotification('Report generation failed: ' + error.message, 'error');
  }
}
/**
 * Generates final reports with selected payroll changes from the pre-reporting phase
 * @param {Object} reportConfig - Report configuration including selected changes
 * @param {Object} options - Additional options for report generation
 * @returns {Promise<Object>} - Result object with success/error information
 */
async function generateReportsWithProgress(reportConfig, options = {}) {
  console.log('📊 Generating final reports with selected changes:', 
    reportConfig.selected_changes?.length || 0, 'changes');
  
  // Create progress indicator if not exists
  const mainContainer = document.querySelector('.content-switching-container');
  let progressContainer = document.getElementById('report-generation-progress');
  
  if (!progressContainer) {
    progressContainer = document.createElement('div');
    progressContainer.id = 'report-generation-progress';
    progressContainer.className = 'report-generation-progress';
    progressContainer.innerHTML = `
      <h3><i class="fas fa-file-contract"></i> Generating Final Audit Report</h3>
      <div class="progress-details">
        <p class="selected-count">Processing ${reportConfig.selected_changes?.length || 0} selected changes</p>
        <div class="progress-bar">
          <div class="progress-fill" style="width: 0%"></div>
        </div>
        <div class="generation-status">Preparing report data...</div>
      </div>
    `;
    
    if (mainContainer) {
      // Clear any existing content
      const existingPhaseContent = document.querySelector('.phase-content.active');
      if (existingPhaseContent) {
        existingPhaseContent.classList.remove('active');
      }
      
      // Create a container for this phase if needed
      let reportPhaseContent = document.getElementById('report-generation-content');
      if (!reportPhaseContent) {
        reportPhaseContent = document.createElement('div');
        reportPhaseContent.id = 'report-generation-content';
        reportPhaseContent.className = 'phase-content active';
        mainContainer.appendChild(reportPhaseContent);
      } else {
        reportPhaseContent.className = 'phase-content active';
        reportPhaseContent.style.display = 'block';
      }
      
      reportPhaseContent.innerHTML = '';
      reportPhaseContent.appendChild(progressContainer);
    }
  }
  
  const progressFill = progressContainer.querySelector('.progress-fill');
  const statusText = progressContainer.querySelector('.generation-status');
  
  if (!reportConfig.selected_changes || !Array.isArray(reportConfig.selected_changes) || reportConfig.selected_changes.length === 0) {
    const errorMessage = 'No changes selected for the report';
    console.error(errorMessage);
    if (statusText) statusText.textContent = `Error: ${errorMessage}`;
    return { success: false, error: errorMessage };
  }
  
  try {
    // Store selected changes in report data for use in actual report generation
    const reportData = {
      id: `report_${Date.now()}`,
      process_id: currentProcessId,
      report_date: new Date().toISOString(),
      report_title: 'Payroll Audit Report',
      payroll_month: reportConfig.payroll_month || getCurrentMonth(),
      payroll_year: reportConfig.payroll_year || getCurrentYear(),
      current_month: currentMonth,
      current_year: currentYear,
      previous_month: previousMonth,
      previous_year: previousYear,
      report_name: signatureName || 'System Administrator',
      report_designation: signatureDesignation || 'Payroll Auditor',
      report_type: 'final',
      source_tab: 'payroll_audit',
      selected_changes: reportConfig.selected_changes.map(change => ({
        ...change,
        priority: change.section_priority || 'LOW',
        report_description: change.report_description || getFormattedChangeDescription(change)
      })),
      options: options
    };
    
    // Update status for PDF report generation
    if (statusText) statusText.textContent = 'Generating PDF report...';
    if (progressFill) progressFill.style.width = '30%';
    
    // Call the backend API to generate PDF report
    const pdfResult = await window.api.generateFinalReport(reportData, 'pdf');
    if (!pdfResult.success) {
      throw new Error(`PDF generation failed: ${pdfResult.error || 'Unknown error'}`);
    }
    
    console.log('✅ PDF report generated successfully');
    addRealTimeActivity('report', 'PDF report generated successfully', 1);
    
    // Update progress for Excel report
    if (statusText) statusText.textContent = 'Generating Excel report...';
    if (progressFill) progressFill.style.width = '60%';
    
    // Call the backend API to generate Excel report
    const excelResult = await window.api.generateFinalReport(reportData, 'excel');
    if (!excelResult.success) {
      throw new Error(`Excel generation failed: ${excelResult.error || 'Unknown error'}`);
    }
    
    console.log('✅ Excel report generated successfully');
    addRealTimeActivity('report', 'Excel report generated successfully', 1);
    
    // Update progress for final tasks
    if (statusText) statusText.textContent = 'Finalizing reports...';
    if (progressFill) progressFill.style.width = '90%';
    
    // Wait for UI update
    await new Promise(resolve => setTimeout(resolve, 500));
    
    // Complete progress
    if (progressFill) progressFill.style.width = '100%';
    if (statusText) statusText.textContent = 'Reports generated successfully!';
    
    // Store report paths for later reference
    window.lastGeneratedReports = {
      pdf: pdfResult.reportPath,
      excel: excelResult.reportPath,
      timestamp: new Date().toISOString(),
      selected_count: reportConfig.selected_changes.length
    };
    
    // Show final report results
    setTimeout(() => {
      showFinalReportResults({
        selection_summary: {
          total_selected: reportConfig.selected_changes.length,
          high_priority: reportConfig.selected_changes.filter(c => c.section_priority === 'HIGH').length,
          moderate_priority: reportConfig.selected_changes.filter(c => c.section_priority === 'MODERATE').length,
          low_priority: reportConfig.selected_changes.filter(c => c.section_priority === 'LOW').length
        },
        report_paths: {
          pdf: pdfResult.reportPath, 
          excel: excelResult.reportPath
        }
      });
    }, 1000);
    
    return { 
      success: true, 
      reportPaths: { 
        pdf: pdfResult.reportPath, 
        excel: excelResult.reportPath 
      } 
    };
  } catch (error) {
    console.error('❌ Error generating reports:', error);
    if (statusText) statusText.textContent = `Error: ${error.message}`;
    showNotification('Report generation failed: ' + error.message, 'error');
    return { success: false, error: error.message };
  }
}

/**
 * Helper function to get current month name
 */
function getCurrentMonth() {
  const months = ['January', 'February', 'March', 'April', 'May', 'June', 
                 'July', 'August', 'September', 'October', 'November', 'December'];
  return months[new Date().getMonth()];
}

/**
 * Helper function to get current year
 */
function getCurrentYear() {
  return new Date().getFullYear();
}

/**
 * Helper function to format change description in professional language
 */
function getFormattedChangeDescription(change) {
  const action = change.is_new ? 'introduced' : 'changed';
  let formattedDescription = '';
  
  if (change.employee_id && change.employee_name) {
    formattedDescription += `${change.employee_id}-${change.employee_name}: `;
  }
  
  formattedDescription += `${change.item_name || 'Item'} ${action}`;
  
  if (change.previous_value !== undefined && change.current_value !== undefined) {
    formattedDescription += ` from ${change.previous_value} in ${previousMonth} ${previousYear}`;
    formattedDescription += ` to ${change.current_value} in ${currentMonth} ${currentYear}`;
    
    // Add difference information if numeric
    const prevValue = parseFloat(change.previous_value);
    const currValue = parseFloat(change.current_value);
    
    if (!isNaN(prevValue) && !isNaN(currValue)) {
      const difference = currValue - prevValue;
      const changeType = difference > 0 ? 'increase' : 'decrease';
      formattedDescription += `: ${changeType} ${Math.abs(difference)}`;
    }
  }
  
  return formattedDescription;
}

function simulateReportGeneration(preReportingData) {
  const progressFill = document.querySelector('#report-results .progress-fill');
  const statusElement = document.querySelector('#report-results .generation-status');

  let progress = 0;
  const interval = setInterval(() => {
    progress += 10;

    if (progressFill) {
      progressFill.style.width = `${progress}%`;
    }

    if (statusElement) {
      if (progress <= 30) {
        statusElement.textContent = 'Analyzing selected changes...';
      } else if (progress <= 60) {
        statusElement.textContent = 'Generating report documents...';
      } else if (progress <= 90) {
        statusElement.textContent = 'Finalizing reports...';
      } else {
        statusElement.textContent = 'Reports generated successfully!';
      }
    }

    if (progress >= 100) {
      clearInterval(interval);
      showFinalReportResults(preReportingData);
    }
  }, 300);
}

function showEnhancedAuditResults(results) {
  console.log('📊 Showing enhanced audit results:', results);

  // Switch to reports phase
  updateUIPhase('REPORT_GENERATION', 'Starting report generation...', 80);

  // Display results in UI
  const resultsContainer = document.getElementById('audit-results');
  if (resultsContainer) {
    resultsContainer.innerHTML = `
      <div class="enhanced-results-header">
        <h3>🎉 Enhanced Audit Complete</h3>
        <p>Processing completed with enhanced features</p>
      </div>
      <div class="enhanced-results-summary">
        <div class="result-stat">
          <strong>Total Execution Time:</strong> ${results.total_execution_time?.toFixed(2) || 'N/A'}s
        </div>
        <div class="result-stat">
          <strong>Process Type:</strong> ${results.process_type || 'Enhanced Phased Audit'}
        </div>
        <div class="result-stat">
          <strong>Session ID:</strong> ${results.session_id || 'N/A'}
        </div>
      </div>
      <div class="enhanced-results-actions">
        <button class="btn btn-primary" onclick="openReportsFolder()">
          📁 Open Reports Folder
        </button>
        <button class="btn btn-secondary" onclick="viewDetailedResults()">
          📊 View Detailed Results
        </button>
      </div>
    `;

    resultsContainer.style.display = 'block';
  }
}

function showFinalReportResults(preReportingData) {
  const reportPanel = document.getElementById('report-results');
  if (reportPanel) {
    reportPanel.innerHTML = `
      <div class="final-report-results">
        <div class="results-header">
          <h3>🎉 Reports Generated Successfully!</h3>
          <p>Your enhanced payroll audit is complete</p>
        </div>

        <div class="selection-summary">
          <h4>📊 Selection Summary</h4>
          <div class="summary-stats">
            <div class="stat-item">
              <strong>${preReportingData.selection_summary.total_selected}</strong>
              <span>Changes Reported</span>
            </div>
            <div class="stat-item">
              <strong>${Object.keys(preReportingData.selection_summary.priority_breakdown || {}).length}</strong>
              <span>Priority Levels</span>
            </div>
            <div class="stat-item">
              <strong>${Object.keys(preReportingData.selection_summary.bulk_size_breakdown || {}).length}</strong>
              <span>Change Types</span>
            </div>
          </div>
        </div>

        <div class="report-actions">
          <button class="btn btn-primary" onclick="openReportsFolder()">
            📁 Open Reports Folder
          </button>
          <button class="btn btn-secondary" onclick="downloadReports()">
            💾 Download Reports
          </button>
          <button class="btn btn-outline" onclick="startNewAudit()">
            🔄 Start New Audit
          </button>
        </div>
      </div>
    `;
  }

  // Show success notification
  showNotification('Enhanced payroll audit completed with user-selected changes!', 'success');
}

function startNewAudit() {
  // Reset the interface for a new audit
  updateUIPhase('EXTRACTION', 'Ready to start new audit...', 0);

  // Reset form
  document.getElementById('current-pdf-input').value = '';
  document.getElementById('previous-pdf-input').value = '';
  document.getElementById('current-file-status').textContent = 'No file selected';
  document.getElementById('previous-file-status').textContent = 'No file selected';

  // Reset global state
  auditCurrentPdfPath = null;
  auditPreviousPdfPath = null;
  window.auditCurrentPdfPath = null; // CRITICAL: Reset window property
  window.auditPreviousPdfPath = null; // CRITICAL: Reset window property
  enhancedProcessActive = false;

  showNotification('Ready for new audit', 'info');
}

// ============================================================================
// PROCESS CONTROL FUNCTIONS (PAUSE/STOP)
// ============================================================================

// Timer variables (using existing declarations from line 2519)

function showProcessingControls() {
  // Hide start button and file upload panels during processing
  const startBtn = document.getElementById('start-payroll-audit');

  console.log('🎛️ Showing processing controls');

  if (startBtn) {
    startBtn.style.display = 'none';
    console.log('✅ Start button hidden');
  }

  // Hide file upload panels during processing
  const fileSelectionEnhanced = document.querySelector('.file-selection-enhanced');
  if (fileSelectionEnhanced) {
    fileSelectionEnhanced.style.display = 'none';
    console.log('✅ File upload panels hidden');
  }

  // Also hide the entire phase-1-content (file selection phase)
  const phase1Content = document.getElementById('phase-1-content');
  if (phase1Content) {
    phase1Content.style.display = 'none';
    console.log('✅ Phase 1 content (file selection) hidden');
  }

  // Hide content switching container during processing
  const contentSwitchingContainer = document.getElementById('content-switching-container');
  if (contentSwitchingContainer) {
    contentSwitchingContainer.style.display = 'none';
    console.log('✅ Content switching container hidden');
  }

  // Show enhanced progress panel (which has its own controls)
  showEnhancedProgressPanel();
  console.log('✅ Enhanced progress panel shown');
}

function hideProcessingControls() {
  const startBtn = document.getElementById('start-payroll-audit');

  if (startBtn) {
    startBtn.style.display = 'inline-block';
  }

  // Show file upload panels again
  const fileSelectionEnhanced = document.querySelector('.file-selection-enhanced');
  if (fileSelectionEnhanced) {
    fileSelectionEnhanced.style.display = 'block';
    console.log('✅ File upload panels shown');
  }

  // Show phase-1-content (file selection phase) again
  const phase1Content = document.getElementById('phase-1-content');
  if (phase1Content) {
    phase1Content.style.display = 'block';
    console.log('✅ Phase 1 content (file selection) shown');
  }

  // Show content switching container again
  const contentSwitchingContainer = document.getElementById('content-switching-container');
  if (contentSwitchingContainer) {
    contentSwitchingContainer.style.display = 'block';
    console.log('✅ Content switching container shown');
  }

  // Hide enhanced progress panel
  hideEnhancedProgressPanel();

  // Stop the timer
  stopProcessTimer();
}

// Timer functions are already defined above at line 2522



function startProcessTimer() {
  // Start the process timer
  if (timerInterval) {
    clearInterval(timerInterval);
  }

  // Timer is now handled by enhanced progress panel
  const timerElement = document.getElementById('timer-display');
  if (timerElement) {
    timerElement.style.display = 'inline-block';
  }

  timerInterval = setInterval(() => {
    if (processStartTime && !processPaused) {
      const elapsed = Date.now() - processStartTime;
      const seconds = Math.floor(elapsed / 1000);
      const minutes = Math.floor(seconds / 60);
      const hours = Math.floor(minutes / 60);

      const timeString = hours > 0
        ? `${hours}:${(minutes % 60).toString().padStart(2, '0')}:${(seconds % 60).toString().padStart(2, '0')}`
        : `${minutes}:${(seconds % 60).toString().padStart(2, '0')}`;

      // Update timer display
      if (timerElement) {
        timerElement.textContent = `⏱️ ${timeString}`;
      }

      // Also update in operation details if no specific message
      const operationDetails = document.getElementById('operation-details');
      if (operationDetails && operationDetails.textContent.includes('Preparing to extract')) {
        operationDetails.textContent = `Processing... (${timeString})`;
      }
    }
  }, 1000);
}

function stopProcessTimer() {
  // Stop the process timer
  if (timerInterval) {
    clearInterval(timerInterval);
    timerInterval = null;
  }

  const timerElement = document.getElementById('timer-display');
  if (timerElement) {
    timerElement.textContent = '00:00';
    timerElement.style.display = 'none';
  }
}

// Progress update helper functions
function updateExtractionProgress(percentage, message, employeeCount = 0) {
  console.log(`🔄 Extraction Progress: ${percentage}% - ${message}`);

  // IMPORTANT: Extraction panels are now consolidated into the main progress panel
  // to avoid UI duplication. All extraction progress updates go to the main panel.

  // Update main progress bar and status with extraction progress
  updateProgress(percentage, message);

  // Update current operation in main panel
  updateCurrentOperation('Data Extraction', message);

  // Update step 1 status if still active
  const step1 = document.getElementById('step-1');
  if (step1 && step1.classList.contains('active')) {
    const stepContent = step1.querySelector('.step-content p');
    if (stepContent) {
      stepContent.textContent = message;
    }
  }

  // COMPLETELY REMOVE any extraction-specific panels that might have been created
  // This ensures extraction panels don't appear during the process
  document.querySelectorAll('.extraction-panel, .extraction-progress, [id^="extraction"]').forEach(panel => {
    console.log(`Removing extraction panel during progress update: ${panel.id || 'unnamed extraction panel'}`);
    if (panel.parentNode) {
      panel.parentNode.removeChild(panel);
    }
  });

  // Also remove any containers with class containing 'extraction'
  document.querySelectorAll('[class*="extraction"]').forEach(element => {
    if (element.id !== 'processing-progress-section' && element.parentNode) {
      console.log(`Removing element with extraction class: ${element.id || 'unnamed'}`);
      element.parentNode.removeChild(element);
    }
  });

  // Update main progress bar at bottom (the one showing employee count)
  // This is the ONLY progress indicator we're keeping
  const mainProgressBar = document.querySelector('.progress-bar .progress-fill');
  const mainProgressText = document.querySelector('.progress-text');

  if (mainProgressBar) {
    mainProgressBar.style.width = `${percentage}%`;
  }

  if (mainProgressText) {
    // Be specific that this is extraction to maintain context
    mainProgressText.textContent = `${employeeCount} employees processed with ${percentage.toFixed(1)}% quality`;
  }
  
  // Ensure main processing section is visible
  const mainProgressSection = document.getElementById('processing-progress-section');
  if (mainProgressSection) {
    mainProgressSection.style.display = 'block';
  }
}

function updateProcessingLogs(message, type = 'info') {
  console.log(`📝 Processing Log [${type}]: ${message}`);

  // Determine which logs container to use based on current phase
  let logsContainer = document.getElementById('extraction-logs');

  // Try to find the currently visible logs container
  const trackerLogs = document.getElementById('tracker-logs');
  const preReportingLogs = document.getElementById('pre-reporting-logs');

  // Use the visible container or default to extraction logs
  if (trackerLogs && trackerLogs.offsetParent !== null) {
    logsContainer = trackerLogs;
  } else if (preReportingLogs && preReportingLogs.offsetParent !== null) {
    logsContainer = preReportingLogs;
  }

  if (logsContainer) {
    const logEntry = document.createElement('div');
    logEntry.className = `log-entry log-${type}`;

    // Add timestamp
    const timestamp = new Date().toLocaleTimeString();
    logEntry.innerHTML = `<span class="log-time">[${timestamp}]</span> ${message}`;

    // Add styling based on type
    if (type === 'error') {
      logEntry.style.color = '#dc3545';
      logEntry.style.fontWeight = 'bold';
    } else if (type === 'success') {
      logEntry.style.color = '#28a745';
      logEntry.style.fontWeight = 'bold';
    } else if (type === 'warning') {
      logEntry.style.color = '#ffc107';
    }

    logsContainer.appendChild(logEntry);

    // Keep only last 15 log entries
    while (logsContainer.children.length > 15) {
      logsContainer.removeChild(logsContainer.firstChild);
    }

    // Scroll to bottom
    logsContainer.scrollTop = logsContainer.scrollHeight;
  }
}

function updateBatchProgress(message) {
  console.log(`📦 Batch Progress: ${message}`);

  // Use the new unified logging function
  updateProcessingLogs(message, 'info');
}

function updatePerformanceMetrics(data) {
  console.log(`⚡ Performance: ${data.pages_per_second?.toFixed(1)} pages/sec, ${data.pages_per_worker?.toFixed(1)} pages/worker`);

  // Update performance display in logs
  const extractionLogs = document.getElementById('extraction-logs');
  if (extractionLogs) {
    const logEntry = document.createElement('div');
    logEntry.className = 'log-entry performance-log';
    logEntry.innerHTML = `
      <strong>Performance:</strong> ${data.pages_per_second?.toFixed(1)} pages/sec |
      ${data.pages_per_worker?.toFixed(1)} pages/worker |
      ${data.workers_used} workers |
      ${data.batch_duration?.toFixed(1)}s
    `;
    logEntry.style.color = '#28a745';
    logEntry.style.fontWeight = 'bold';
    extractionLogs.appendChild(logEntry);
  }
}

function updatePerformanceInfo(data) {
  console.log(`💻 System Info: ${data.cpu_cores} cores, ${data.workers} workers, batch size ${data.batch_size}`);

  // Add system info to logs
  const extractionLogs = document.getElementById('extraction-logs');
  if (extractionLogs) {
    const logEntry = document.createElement('div');
    logEntry.className = 'log-entry system-info';
    logEntry.innerHTML = `
      <strong>System Optimized:</strong> ${data.cpu_cores} CPU cores → ${data.workers} parallel workers (batch size: ${data.batch_size})
    `;
    logEntry.style.color = '#007bff';
    logEntry.style.fontWeight = 'bold';
    extractionLogs.appendChild(logEntry);
  }
}

function updateExtractionComplete(data) {
  console.log(`✅ Extraction Complete: ${data.employees_extracted} employees, ${data.quality}% quality`);

  // Update final status
  const extractionStatus = document.getElementById('extraction-status');
  const extractionDetails = document.getElementById('extraction-details');

  if (extractionStatus) {
    extractionStatus.textContent = `✅ Extraction Complete!`;
    extractionStatus.style.color = '#28a745';
  }

  if (extractionDetails) {
    // Fix undefined values with proper fallbacks
    const employeesCount = data.employees_extracted || data.total_employees || 0;
    const qualityPercentage = data.quality_percentage || data.quality || 0;

    extractionDetails.textContent = `${employeesCount} employees processed with ${qualityPercentage.toFixed(1)}% quality`;
  }

  // Update main progress bar at bottom to show completion
  const mainProgressBar = document.querySelector('.progress-bar .progress-fill');
  const mainProgressText = document.querySelector('.progress-text');

  if (mainProgressBar) {
    mainProgressBar.style.width = '100%';
  }

  if (mainProgressText) {
    const employeesCount = data.employees_extracted || data.total_employees || 0;
    const qualityPercentage = data.quality_percentage || data.quality || 0;
    mainProgressText.textContent = `${employeesCount} employees processed with ${qualityPercentage.toFixed(1)}% quality`;
  }

  // Update extraction progress bar to 100%
  const extractionProgress = document.getElementById('extraction-progress');
  if (extractionProgress) {
    extractionProgress.style.width = '100%';
  }
}

async function pausePayrollAuditProcess() {
  // Pause the current payroll audit process
  console.log('⏸️ Pause button clicked');

  if (!enhancedProcessActive) {
    showNotification('No active process to pause', 'warning');
    return;
  }

  try {
    processPaused = !processPaused;
    const pauseBtn = document.getElementById('pause-processing-btn');

    if (processPaused) {
      // Paused state
      if (pauseBtn) {
        pauseBtn.innerHTML = '<i class="fas fa-play"></i> Resume';
        pauseBtn.className = 'btn success large enhanced-btn';
      }

      // Send pause signal to backend
      await window.api.pauseEnhancedProcess(currentProcessId);

      showNotification('Process paused - Click Resume to continue', 'info');
      console.log('⏸️ Process paused');

      // Update UI to show paused state
      const currentOperation = document.getElementById('current-operation');
      const operationDetails = document.getElementById('operation-details');

      if (currentOperation) {
        currentOperation.textContent = '⏸️ Process Paused';
      }

      if (operationDetails) {
        operationDetails.textContent = 'Click Resume to continue processing...';
      }

    } else {
      // Resumed state
      if (pauseBtn) {
        pauseBtn.innerHTML = '<i class="fas fa-pause"></i> Pause Processing';
        pauseBtn.className = 'btn warning large enhanced-btn';
      }

      // Send resume signal to backend
      await window.api.resumeEnhancedProcess(currentProcessId);

      showNotification('Process resumed', 'success');
      console.log('▶️ Process resumed');

      // Update UI to show resumed state
      const currentOperation = document.getElementById('current-operation');
      const operationDetails = document.getElementById('operation-details');

      if (currentOperation) {
        currentOperation.textContent = `Phase: ${currentProcessPhase || 'Processing'}`;
      }

      if (operationDetails) {
        operationDetails.textContent = 'Processing resumed...';
      }
    }

  } catch (error) {
    console.error('❌ Error pausing/resuming process:', error);
    showNotification('Error controlling process: ' + error.message, 'error');
  }
}

async function stopPayrollAuditProcess() {
  // Stop the current payroll audit process
  console.log('🛑 Stop button clicked');

  if (!enhancedProcessActive) {
    showNotification('No active process to stop', 'warning');
    return;
  }

  // Confirm stop action
  if (!confirm('Are you sure you want to stop the current payroll audit process? This action cannot be undone.')) {
    return;
  }

  try {
    // Send stop signal to backend
    await window.api.stopEnhancedProcess(currentProcessId);

    // Reset UI state
    enhancedProcessActive = false;
    processPaused = false;
    currentProcessPhase = null;
    currentProcessId = null;

    // Hide processing controls
    hideProcessingControls();

    // Update UI to show stopped state
    const currentOperation = document.getElementById('current-operation');
    const operationDetails = document.getElementById('operation-details');
    const progressPercentage = document.getElementById('progress-percentage');
    const progressStatus = document.getElementById('progress-status');

    if (currentOperation) {
      currentOperation.textContent = '🛑 Process Stopped';
    }

    if (operationDetails) {
      operationDetails.textContent = 'Process was stopped by user request';
    }

    if (progressPercentage) {
      progressPercentage.textContent = '0%';
    }

    if (progressStatus) {
      progressStatus.textContent = 'Stopped';
    }

    // Switch back to initial phase
    updateUIPhase('EXTRACTION', 'Process stopped - ready for new audit', 0);

    showNotification('Process stopped successfully', 'info');
    console.log('🛑 Process stopped');

    // Reset form for new audit
    setTimeout(() => {
      startNewAudit();
    }, 2000);

  } catch (error) {
    console.error('❌ Error stopping process:', error);
    showNotification('Error stopping process: ' + error.message, 'error');
  }
}

// Add enhanced CSS styles
function addEnhancedStyles() {
  const styles = document.createElement('style');
  styles.textContent = `
    .enhanced-audit-container {
      margin-top: 10px;
      padding: 15px;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      border-radius: 8px;
      color: white;
    }

    .enhanced-audit-btn {
      background: rgba(255, 255, 255, 0.2);
      border: 1px solid rgba(255, 255, 255, 0.3);
      color: white;
      font-weight: 600;
    }

    .enhanced-audit-btn:hover:not(:disabled) {
      background: rgba(255, 255, 255, 0.3);
      transform: translateY(-1px);
    }

    .enhanced-audit-note {
      display: block;
      margin-top: 8px;
      opacity: 0.9;
      font-style: italic;
    }

    .enhanced-status-container {
      background: #f8f9fa;
      border: 1px solid #e9ecef;
      border-radius: 8px;
      padding: 20px;
      margin-top: 20px;
    }

    .enhanced-status-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 15px;
      padding-bottom: 10px;
      border-bottom: 1px solid #dee2e6;
    }

    .enhanced-status-phase {
      background: #007bff;
      color: white;
      padding: 4px 12px;
      border-radius: 12px;
      font-size: 12px;
      font-weight: 600;
    }

    .enhanced-status-details {
      max-height: 200px;
      overflow-y: auto;
      background: white;
      border: 1px solid #e9ecef;
      border-radius: 4px;
      padding: 10px;
    }

    .enhanced-log-entry {
      display: flex;
      gap: 10px;
      padding: 4px 0;
      border-bottom: 1px solid #f8f9fa;
      font-size: 12px;
    }

    .log-timestamp {
      color: #6c757d;
      font-weight: 600;
      min-width: 80px;
    }

    .log-message {
      flex: 1;
    }

    .log-error .log-message {
      color: #dc3545;
    }

    .log-info .log-message {
      color: #17a2b8;
    }

    .enhanced-results-header {
      text-align: center;
      margin-bottom: 20px;
    }

    .enhanced-results-summary {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 15px;
      margin-bottom: 20px;
    }

    .result-stat {
      background: #f8f9fa;
      padding: 15px;
      border-radius: 8px;
      text-align: center;
    }

    .enhanced-results-actions {
      display: flex;
      gap: 10px;
      justify-content: center;
    }

    .status-pending { background: #ffc107; color: #856404; }
    .status-processing { background: #17a2b8; color: white; }
    .status-completed { background: #28a745; color: white; }
    .status-error { background: #dc3545; color: white; }
  `;

  document.head.appendChild(styles);
}

// Initialize enhanced styles
addEnhancedStyles();

// Make critical functions globally accessible
window.setupUIEventListeners = setupUIEventListeners;
window.setupRealTimeEventListeners = setupRealTimeEventListeners;
window.openSettingsModal = openSettingsModal;
window.openHelpModal = openHelpModal;
window.closeSettingsModal = closeSettingsModal;
window.closeHelpModal = closeHelpModal;

console.log('THE PAYROLL AUDITOR - Enhanced Renderer script loaded');
console.log('✅ Critical functions made globally accessible:', {
  setupUIEventListeners: typeof window.setupUIEventListeners,
  setupRealTimeEventListeners: typeof window.setupRealTimeEventListeners,
  openSettingsModal: typeof window.openSettingsModal,
  openHelpModal: typeof window.openHelpModal
});

// Setup event listeners immediately when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
  console.log('🔧 DOM ready - setting up event listeners from renderer.js');

  // Small delay to ensure all elements are rendered
  setTimeout(() => {
    if (typeof setupUIEventListeners === 'function') {
      setupUIEventListeners();
      console.log('✅ Event listeners set up successfully from renderer.js');
    } else {
      console.error('❌ setupUIEventListeners function not available');
    }
  }, 100);
});
