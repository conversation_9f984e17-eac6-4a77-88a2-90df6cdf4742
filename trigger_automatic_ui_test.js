// Test Automatic Pre-Reporting UI Triggering
// This script simulates the PRE_REPORTING phase completion to test automatic UI triggering

console.log('🧪 TESTING AUTOMATIC PRE-REPORTING UI TRIGGERING...');

// Simulate the exact conditions that should trigger the automatic UI
async function testAutomaticUITriggering() {
    try {
        console.log('🎯 Step 1: Simulating PRE_REPORTING phase completion...');
        
        // This simulates what happens when the phased process manager completes PRE_REPORTING
        // and should automatically trigger the loadPreReportingUIFromDatabase function
        
        // Method 1: Direct function call (if available)
        if (typeof window.loadPreReportingUIFromDatabase === 'function') {
            console.log('✅ Found loadPreReportingUIFromDatabase function - calling directly...');
            await window.loadPreReportingUIFromDatabase();
            console.log('🎉 Direct function call completed!');
            return { success: true, method: 'direct_function_call' };
        }
        
        // Method 2: Simulate phase update that should trigger the UI
        if (typeof window.updateUIPhase === 'function') {
            console.log('✅ Found updateUIPhase function - simulating PRE_REPORTING completion...');
            window.updateUIPhase('PRE_REPORTING', 'Pre-reporting data ready for review', 85);
            console.log('🎉 Phase update completed!');
            return { success: true, method: 'phase_update' };
        }
        
        // Method 3: Simulate the exact message that triggers the UI
        if (typeof window.handleEnhancedProgressUpdate === 'function') {
            console.log('✅ Found handleEnhancedProgressUpdate function - simulating completion...');
            window.handleEnhancedProgressUpdate({
                type: 'phase_completion',
                phase: 'PRE_REPORTING',
                message: 'Pre-reporting data ready for user interaction',
                percentage: 85,
                ready_for_review: true
            });
            console.log('🎉 Progress update completed!');
            return { success: true, method: 'progress_update' };
        }
        
        // Method 4: Manual trigger using the API
        console.log('🔄 Fallback: Manual trigger using API...');
        
        if (!window.api || !window.api.getLatestPreReportingData) {
            throw new Error('API not available - make sure you are running this in the Electron app');
        }
        
        // Load the data
        const preReportingData = await window.api.getLatestPreReportingData();
        
        if (!preReportingData || !preReportingData.success) {
            throw new Error('Failed to load pre-reporting data: ' + (preReportingData?.error || 'Unknown error'));
        }
        
        console.log('✅ Pre-reporting data loaded:', preReportingData.data.length, 'changes');
        
        // Find container
        let container = document.getElementById('pre-reporting-content') || 
                       document.getElementById('main-content') ||
                       document.querySelector('.tab-content.active');
        
        if (!container) {
            // Create a test container
            container = document.createElement('div');
            container.id = 'automatic-ui-test-container';
            container.style.cssText = `
                margin: 20px; 
                padding: 20px; 
                background: white; 
                border-radius: 12px; 
                box-shadow: 0 4px 20px rgba(0,0,0,0.1);
                border: 2px solid #4CAF50;
            `;
            
            const header = document.createElement('div');
            header.innerHTML = `
                <div style="
                    background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%); 
                    color: white; 
                    padding: 15px; 
                    margin: -20px -20px 20px -20px; 
                    border-radius: 10px 10px 0 0;
                ">
                    <h2 style="margin: 0;">🧪 AUTOMATIC UI TEST - Pre-Reporting Interface</h2>
                    <p style="margin: 5px 0 0 0;">
                        Testing automatic triggering with ${preReportingData.data.length} real changes
                    </p>
                </div>
            `;
            container.appendChild(header);
            
            document.body.appendChild(container);
            console.log('📦 Created test container');
        }
        
        // Load InteractivePreReporting if needed
        if (!window.InteractivePreReporting) {
            console.log('📋 Loading InteractivePreReporting script...');
            const script = document.createElement('script');
            script.src = 'ui/interactive_pre_reporting.js';
            document.head.appendChild(script);
            
            await new Promise((resolve, reject) => {
                script.onload = resolve;
                script.onerror = () => reject(new Error('Failed to load script'));
                setTimeout(() => reject(new Error('Timeout loading script')), 5000);
            });
            
            console.log('✅ InteractivePreReporting script loaded');
        }
        
        // Create content container
        const contentContainer = document.createElement('div');
        contentContainer.id = 'test-ui-content';
        container.appendChild(contentContainer);
        
        // Initialize and render the actual pre-reporting UI
        console.log('🎨 Initializing actual pre-reporting UI...');
        window.interactivePreReporting = new window.InteractivePreReporting(contentContainer);
        
        // Render the UI
        if (typeof window.interactivePreReporting.render === 'function') {
            await window.interactivePreReporting.render(preReportingData.data);
        } else {
            window.interactivePreReporting.initialize(preReportingData.data);
        }
        
        // Success notification
        const notification = document.createElement('div');
        notification.innerHTML = `
            <div style="
                position: fixed; top: 20px; right: 20px; 
                background: #4CAF50; color: white; 
                padding: 15px 20px; border-radius: 8px; 
                box-shadow: 0 4px 12px rgba(0,0,0,0.2); z-index: 10000;
            ">
                🎉 AUTOMATIC UI TEST SUCCESS!<br>
                <small>Pre-reporting UI triggered with ${preReportingData.data.length} changes</small>
            </div>
        `;
        document.body.appendChild(notification);
        setTimeout(() => notification.remove(), 5000);
        
        // Scroll to view
        container.scrollIntoView({ behavior: 'smooth', block: 'start' });
        
        console.log('🎉 AUTOMATIC UI TEST COMPLETED SUCCESSFULLY!');
        
        return { 
            success: true, 
            method: 'manual_api_trigger',
            changesCount: preReportingData.data.length,
            sessionId: preReportingData.session_id
        };
        
    } catch (error) {
        console.error('❌ Automatic UI test failed:', error);
        
        // Error notification
        const errorNotification = document.createElement('div');
        errorNotification.innerHTML = `
            <div style="
                position: fixed; top: 20px; right: 20px; 
                background: #f44336; color: white; 
                padding: 15px 20px; border-radius: 8px; 
                box-shadow: 0 4px 12px rgba(0,0,0,0.2); z-index: 10000;
            ">
                ❌ AUTOMATIC UI TEST FAILED<br>
                <small>${error.message}</small>
            </div>
        `;
        document.body.appendChild(errorNotification);
        setTimeout(() => errorNotification.remove(), 8000);
        
        return { success: false, error: error.message };
    }
}

// Run the test
testAutomaticUITriggering()
    .then(result => {
        if (result.success) {
            console.log('✅ AUTOMATIC UI TRIGGERING TEST PASSED!');
            console.log('🎯 Result:', result);
        } else {
            console.log('❌ AUTOMATIC UI TRIGGERING TEST FAILED!');
            console.log('🎯 Error:', result.error);
        }
    })
    .catch(error => {
        console.error('❌ Test execution failed:', error);
    });
