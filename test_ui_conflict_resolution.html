<!DOCTYPE html>
<html>
<head>
    <title>Test UI Conflict Resolution</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; background: #f5f5f5; }
        .test-container { 
            background: white; 
            border: 1px solid #ddd; 
            padding: 20px; 
            margin: 20px 0; 
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        button { 
            padding: 12px 24px; 
            margin: 10px; 
            font-size: 16px; 
            border: none;
            border-radius: 6px;
            cursor: pointer;
        }
        .btn-primary { background: #007bff; color: white; }
        .btn-secondary { background: #6c757d; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-danger { background: #dc3545; color: white; }
        
        #pre-reporting-content { 
            min-height: 400px; 
            border: 2px dashed #ccc; 
            padding: 20px; 
            margin: 20px 0;
            border-radius: 8px;
            background: #fafafa;
        }
        
        .status { 
            padding: 10px; 
            margin: 10px 0; 
            border-radius: 4px; 
            font-weight: bold;
        }
        .status.success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .status.error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .status.info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        
        #debug-output { 
            background: #f8f9fa; 
            padding: 15px; 
            font-family: 'Courier New', monospace; 
            white-space: pre-wrap; 
            border: 1px solid #e9ecef;
            border-radius: 4px;
            max-height: 300px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <h1>🔧 UI Conflict Resolution Test</h1>
    <p>This test verifies that the <strong>enhanced InteractivePreReporting class</strong> loads instead of the simple fallback UI.</p>
    
    <div class="test-container">
        <h2>🎯 Test Controls</h2>
        <button class="btn-primary" onclick="testEnhancedUI()">🎨 Test Enhanced UI</button>
        <button class="btn-secondary" onclick="testFallbackDisabled()">🚫 Test Fallback Disabled</button>
        <button class="btn-success" onclick="testAPIConnection()">🔍 Test API Connection</button>
        <button class="btn-danger" onclick="clearTest()">🧹 Clear Test</button>
    </div>
    
    <div class="test-container">
        <h2>📊 Test Status</h2>
        <div id="test-status"></div>
    </div>
    
    <div class="test-container">
        <h2>🎨 Pre-Reporting UI Container</h2>
        <div id="pre-reporting-content">
            <p style="text-align: center; color: #666; font-style: italic;">
                Enhanced pre-reporting UI will appear here...
            </p>
        </div>
    </div>
    
    <div class="test-container">
        <h2>🔍 Debug Output</h2>
        <div id="debug-output"></div>
    </div>

    <script>
        function log(message, type = 'info') {
            const debugOutput = document.getElementById('debug-output');
            const timestamp = new Date().toLocaleTimeString();
            debugOutput.textContent += `[${timestamp}] ${message}\n`;
            console.log(message);
            
            // Update status
            updateStatus(message, type);
        }

        function updateStatus(message, type) {
            const statusDiv = document.getElementById('test-status');
            const statusClass = type === 'error' ? 'error' : type === 'success' ? 'success' : 'info';
            statusDiv.innerHTML = `<div class="status ${statusClass}">${message}</div>`;
        }

        async function testEnhancedUI() {
            log('🎨 Testing Enhanced InteractivePreReporting UI...', 'info');
            
            try {
                // Check if InteractivePreReporting class is available
                if (!window.InteractivePreReporting) {
                    log('❌ InteractivePreReporting class not available', 'error');
                    
                    // Try to load the script
                    log('🔄 Attempting to load InteractivePreReporting script...', 'info');
                    const script = document.createElement('script');
                    script.src = 'ui/interactive_pre_reporting.js';
                    script.onload = () => {
                        log('✅ InteractivePreReporting script loaded successfully', 'success');
                        testEnhancedUI(); // Retry
                    };
                    script.onerror = () => {
                        log('❌ Failed to load InteractivePreReporting script', 'error');
                    };
                    document.head.appendChild(script);
                    return;
                }
                
                log('✅ InteractivePreReporting class is available', 'success');
                
                // Get container
                const container = document.getElementById('pre-reporting-content');
                if (!container) {
                    log('❌ Container not found', 'error');
                    return;
                }
                
                // Clear container
                container.innerHTML = '<p style="text-align: center;">🔄 Loading enhanced UI...</p>';
                
                // Create InteractivePreReporting instance
                log('🎨 Creating InteractivePreReporting instance...', 'info');
                const preReporting = new window.InteractivePreReporting(container);
                
                // Check if it has the enhanced methods
                const hasEnhancedMethods = [
                    'initialize',
                    'loadDataFromDatabase', 
                    'processAndRender',
                    'groupByEmployee',
                    'selectHighPriority',
                    'toggleChangeDetails'
                ].every(method => typeof preReporting[method] === 'function');
                
                if (hasEnhancedMethods) {
                    log('✅ Enhanced methods detected - this is the ENHANCED UI!', 'success');
                    updateStatus('🎉 ENHANCED UI CONFIRMED - No conflict detected!', 'success');
                } else {
                    log('⚠️ Some enhanced methods missing', 'error');
                    updateStatus('⚠️ UI may not be fully enhanced', 'error');
                }
                
                // Initialize the UI
                log('🎨 Initializing enhanced UI...', 'info');
                preReporting.initialize();
                
                log('✅ Enhanced UI test completed successfully', 'success');
                
            } catch (error) {
                log(`❌ Enhanced UI test failed: ${error.message}`, 'error');
                updateStatus(`❌ Test failed: ${error.message}`, 'error');
            }
        }

        async function testFallbackDisabled() {
            log('🚫 Testing that fallback UI is disabled...', 'info');
            
            try {
                // Check if the fallback function exists but is disabled
                if (typeof window.forceRenderBeautifulPreReportingUI === 'function') {
                    log('⚠️ Fallback function still exists', 'info');
                    
                    // Test calling it to see if it's disabled
                    const container = document.getElementById('pre-reporting-content');
                    container.innerHTML = '<p>Testing fallback...</p>';
                    
                    // This should now redirect to enhanced UI instead of rendering fallback
                    window.forceRenderBeautifulPreReportingUI(container, []);
                    
                    // Check if it shows the enhanced UI message
                    setTimeout(() => {
                        const content = container.innerHTML;
                        if (content.includes('Enhanced Pre-Reporting Loading') || content.includes('enhanced')) {
                            log('✅ Fallback successfully redirects to enhanced UI', 'success');
                            updateStatus('✅ Fallback properly disabled - redirects to enhanced UI', 'success');
                        } else {
                            log('❌ Fallback may still be rendering old UI', 'error');
                            updateStatus('❌ Fallback conflict detected', 'error');
                        }
                    }, 100);
                    
                } else {
                    log('✅ Fallback function not found - completely removed', 'success');
                    updateStatus('✅ Fallback function completely removed', 'success');
                }
                
            } catch (error) {
                log(`❌ Fallback test failed: ${error.message}`, 'error');
            }
        }

        async function testAPIConnection() {
            log('🔍 Testing API connection for data loading...', 'info');
            
            try {
                if (!window.api) {
                    log('❌ window.api not available (normal in browser)', 'info');
                    updateStatus('ℹ️ API test requires Electron environment', 'info');
                    return;
                }
                
                if (!window.api.getPreReportingData) {
                    log('❌ getPreReportingData method not available', 'error');
                    return;
                }
                
                log('✅ API available, testing data retrieval...', 'info');
                const result = await window.api.getPreReportingData();
                
                if (result && result.success) {
                    log(`✅ API working! Retrieved ${result.data ? result.data.length : 0} changes`, 'success');
                    updateStatus(`✅ API working - ${result.data ? result.data.length : 0} changes available`, 'success');
                } else {
                    log('⚠️ API returned no data (may be normal if no audit run)', 'info');
                    updateStatus('ℹ️ API working but no data available', 'info');
                }
                
            } catch (error) {
                log(`❌ API test failed: ${error.message}`, 'error');
            }
        }

        function clearTest() {
            const container = document.getElementById('pre-reporting-content');
            container.innerHTML = '<p style="text-align: center; color: #666; font-style: italic;">Enhanced pre-reporting UI will appear here...</p>';
            
            const debugOutput = document.getElementById('debug-output');
            debugOutput.textContent = '';
            
            const statusDiv = document.getElementById('test-status');
            statusDiv.innerHTML = '';
            
            log('🧹 Test cleared', 'info');
        }

        // Auto-run basic checks on load
        window.addEventListener('load', () => {
            log('🚀 UI Conflict Resolution Test loaded', 'info');
            log('📊 Available globals:', 'info');
            log(`  window.api: ${!!window.api}`, 'info');
            log(`  window.InteractivePreReporting: ${!!window.InteractivePreReporting}`, 'info');
            log(`  window.forceRenderBeautifulPreReportingUI: ${!!window.forceRenderBeautifulPreReportingUI}`, 'info');
            
            updateStatus('🔧 Ready for conflict resolution testing', 'info');
        });
    </script>
</body>
</html>
