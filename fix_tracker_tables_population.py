#!/usr/bin/env python3
"""
Fix Tracker Tables Population
Ensures Bank Adviser tables are properly populated from tracker results
"""

import sqlite3
import json
import sys
import os

def get_current_session():
    """Get the current session ID"""
    try:
        conn = sqlite3.connect('payroll_audit.db')
        cursor = conn.cursor()
        
        cursor.execute("""
            SELECT session_id FROM sessions 
            WHERE is_current = 1 
            ORDER BY created_at DESC 
            LIMIT 1
        """)
        
        result = cursor.fetchone()
        conn.close()
        
        if result:
            return result[0]
        else:
            print("❌ No current session found")
            return None
            
    except Exception as e:
        print(f"❌ Error getting current session: {e}")
        return None

def populate_bank_adviser_tables():
    """Populate Bank Adviser tables from tracker results"""
    
    current_session = get_current_session()
    if not current_session:
        return {'success': False, 'error': 'No current session found'}
    
    try:
        conn = sqlite3.connect('payroll_audit.db')
        cursor = conn.cursor()
        
        print(f"🔄 POPULATING BANK ADVISER TABLES FOR SESSION: {current_session}")
        
        # Clear existing data for this session
        print("\n1. 🧹 CLEARING EXISTING DATA:")
        tables_to_clear = ['in_house_loans', 'external_loans', 'motor_vehicle_maintenance']
        
        for table in tables_to_clear:
            cursor.execute(f"DELETE FROM {table} WHERE source_session = ?", (current_session,))
            deleted_count = cursor.rowcount
            print(f"   Cleared {deleted_count} records from {table}")
        
        # Get tracker results
        print("\n2. 📊 LOADING TRACKER RESULTS:")
        cursor.execute("""
            SELECT tracker_type, COUNT(*) as count
            FROM tracker_results 
            WHERE session_id = ?
            GROUP BY tracker_type
        """, (current_session,))
        
        tracker_summary = cursor.fetchall()
        print("   Tracker results summary:")
        for row in tracker_summary:
            print(f"     {row[0]}: {row[1]} items")
        
        # Populate in-house loans
        print("\n3. 🏦 POPULATING IN-HOUSE LOANS:")
        cursor.execute("""
            SELECT employee_id, employee_name, item_label, item_value, numeric_value
            FROM tracker_results 
            WHERE session_id = ? AND tracker_type = 'IN_HOUSE_LOAN'
        """, (current_session,))
        
        in_house_data = cursor.fetchall()
        print(f"   Found {len(in_house_data)} in-house loans")
        
        in_house_count = 0
        for row in in_house_data:
            try:
                # Extract loan type from item_label
                loan_type = row[2]  # item_label
                if ' - ' in loan_type:
                    loan_type = loan_type.split(' - ')[0].strip()
                
                cursor.execute("""
                    INSERT INTO in_house_loans 
                    (employee_no, employee_name, department, loan_type, loan_amount,
                     period_month, period_year, period_acquired, source_session, remarks)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    row[0],  # employee_id
                    row[1],  # employee_name
                    'Department not specified',  # department
                    loan_type,  # loan_type
                    row[4] or 0,  # numeric_value
                    '06',  # current month
                    '2025',  # current year
                    '2025-06',  # period_acquired
                    current_session,  # source_session
                    'Monitoring'  # remarks
                ))
                in_house_count += 1
                
                if in_house_count <= 3:  # Show first 3
                    print(f"     ✅ {row[0]} - {loan_type}: {row[4] or 0}")
                    
            except Exception as e:
                print(f"     ❌ Failed to insert in-house loan: {e}")
        
        print(f"   ✅ Inserted {in_house_count} in-house loans")
        
        # Populate external loans
        print("\n4. 🏛️ POPULATING EXTERNAL LOANS:")
        cursor.execute("""
            SELECT employee_id, employee_name, item_label, item_value, numeric_value
            FROM tracker_results 
            WHERE session_id = ? AND tracker_type = 'EXTERNAL_LOAN'
        """, (current_session,))
        
        external_data = cursor.fetchall()
        print(f"   Found {len(external_data)} external loans")
        
        external_count = 0
        for row in external_data:
            try:
                # Extract loan type from item_label
                loan_type = row[2]  # item_label
                if ' - ' in loan_type:
                    loan_type = loan_type.split(' - ')[0].strip()
                
                cursor.execute("""
                    INSERT INTO external_loans 
                    (employee_no, employee_name, department, loan_type, loan_amount,
                     period_month, period_year, period_acquired, source_session)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    row[0],  # employee_id
                    row[1],  # employee_name
                    'Department not specified',  # department
                    loan_type,  # loan_type
                    row[4] or 0,  # numeric_value
                    '06',  # current month
                    '2025',  # current year
                    '2025-06',  # period_acquired
                    current_session  # source_session
                ))
                external_count += 1
                
                if external_count <= 3:  # Show first 3
                    print(f"     ✅ {row[0]} - {loan_type}: {row[4] or 0}")
                    
            except Exception as e:
                print(f"     ❌ Failed to insert external loan: {e}")
        
        print(f"   ✅ Inserted {external_count} external loans")
        
        # Populate motor vehicle maintenance
        print("\n5. 🚗 POPULATING MOTOR VEHICLE MAINTENANCE:")
        cursor.execute("""
            SELECT employee_id, employee_name, item_label, item_value, numeric_value
            FROM tracker_results 
            WHERE session_id = ? AND tracker_type = 'motor_vehicles'
        """, (current_session,))
        
        motor_data = cursor.fetchall()
        print(f"   Found {len(motor_data)} motor vehicle items")
        
        motor_count = 0
        for row in motor_data:
            try:
                cursor.execute("""
                    INSERT INTO motor_vehicle_maintenance 
                    (employee_no, employee_name, department, allowance_type, 
                     allowance_amount, payable_amount, maintenance_amount,
                     period_month, period_year, period_acquired, 
                     source_session, remarks)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    row[0],  # employee_id
                    row[1],  # employee_name
                    'Department not specified',  # department
                    row[2],  # item_label as allowance_type
                    row[4] or 0,  # numeric_value as allowance_amount
                    row[4] or 0,  # numeric_value as payable_amount
                    row[4] or 0,  # numeric_value as maintenance_amount
                    '06',  # current month
                    '2025',  # current year
                    '2025-06',  # period_acquired
                    current_session,  # source_session
                    'Monitoring'  # remarks
                ))
                motor_count += 1
                
                if motor_count <= 3:  # Show first 3
                    print(f"     ✅ {row[0]} - {row[2]}: {row[4] or 0}")
                    
            except Exception as e:
                print(f"     ❌ Failed to insert motor vehicle: {e}")
        
        print(f"   ✅ Inserted {motor_count} motor vehicle records")
        
        # Commit all changes
        conn.commit()
        conn.close()
        
        # Summary
        total_inserted = in_house_count + external_count + motor_count
        print(f"\n✅ POPULATION COMPLETE:")
        print(f"   In-house loans: {in_house_count}")
        print(f"   External loans: {external_count}")
        print(f"   Motor vehicles: {motor_count}")
        print(f"   Total inserted: {total_inserted}")
        
        return {
            'success': True,
            'in_house_loans': in_house_count,
            'external_loans': external_count,
            'motor_vehicles': motor_count,
            'total': total_inserted
        }
        
    except Exception as e:
        print(f"❌ Error populating tables: {e}")
        return {'success': False, 'error': str(e)}

def main():
    """Main entry point"""
    result = populate_bank_adviser_tables()
    print(json.dumps(result))

if __name__ == '__main__':
    main()
