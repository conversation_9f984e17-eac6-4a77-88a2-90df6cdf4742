#!/usr/bin/env python3
"""
Test Dictionary-Only Classification
Verify that loan classification now uses ONLY dictionary-based logic, no keyword fallback
"""

import os
import sys
import sqlite3
from pathlib import Path

def get_database_path():
    """Get the database path"""
    db_path = Path(__file__).parent / "data" / "templar_payroll_auditor.db"
    if db_path.exists():
        return str(db_path)
    return None

def test_dictionary_only_classification():
    """Test that classification uses only dictionary, no keyword fallback"""
    
    print("🧪 TESTING DICTIONARY-ONLY CLASSIFICATION")
    print("=" * 45)
    
    try:
        # 1. Test dictionary manager classification
        print("1. 📚 TESTING DICTIONARY MANAGER:")
        
        sys.path.append(os.path.dirname(__file__))
        from core.dictionary_manager import PayrollDictionaryManager
        
        dict_manager = PayrollDictionaryManager(debug=False)
        
        # Get all loan types from dictionary
        loan_types = dict_manager.get_loan_types()
        print(f"   📋 Dictionary loan types: {len(loan_types)}")
        
        in_house_from_dict = []
        for loan_type_name, loan_data in loan_types.items():
            classification = loan_data.get('classification', 'UNKNOWN')
            print(f"      {loan_type_name}: {classification}")
            if 'IN-HOUSE' in classification.upper():
                in_house_from_dict.append(loan_type_name)
        
        print(f"   ✅ IN-HOUSE types in dictionary: {len(in_house_from_dict)}")
        
        # 2. Test classification method
        print(f"\n2. 🧪 TESTING CLASSIFICATION METHOD:")
        
        # Test loans that should be IN-HOUSE (from dictionary)
        test_loans = [
            'SALARY ADVANCE-MINS',
            'BUILDING-MINISTERS', 
            'RENT ADVANCE',
            'STAFF CREDIT UNION LO',
            'PENSIONS SALARY ADVA'
        ]
        
        # Test loans that should be EXTERNAL (not in dictionary)
        external_test_loans = [
            'GCB BANK LOAN',
            'OMNI BANK LOAN',
            'GLICO LIFE INSURANCE',
            'UNKNOWN BANK LOAN'
        ]
        
        print("   📋 Testing IN-HOUSE loans (should be in dictionary):")
        in_house_correct = 0
        for loan in test_loans:
            classification = dict_manager.classify_loan_type(loan)
            is_correct = classification == "IN-HOUSE LOAN"
            status = "✅" if is_correct else "❌"
            print(f"      {status} {loan}: {classification}")
            if is_correct:
                in_house_correct += 1
        
        print(f"\n   📋 Testing EXTERNAL loans (should NOT be in dictionary):")
        external_correct = 0
        for loan in external_test_loans:
            classification = dict_manager.classify_loan_type(loan)
            is_correct = classification == "EXTERNAL LOAN"
            status = "✅" if is_correct else "❌"
            print(f"      {status} {loan}: {classification}")
            if is_correct:
                external_correct += 1
        
        # 3. Test perfect extraction integration
        print(f"\n3. 🧪 TESTING PERFECT EXTRACTION INTEGRATION:")
        
        try:
            from core.perfect_extraction_integration import PerfectExtractionIntegration
            
            extractor = PerfectExtractionIntegration(debug=False)
            
            print("   📋 Testing _is_in_house_loan method:")
            for loan in test_loans:
                is_in_house = extractor._is_in_house_loan(loan)
                status = "✅" if is_in_house else "❌"
                print(f"      {status} {loan}: {'IN-HOUSE' if is_in_house else 'EXTERNAL'}")
            
            print(f"\n   📋 Testing external loans:")
            for loan in external_test_loans:
                is_in_house = extractor._is_in_house_loan(loan)
                status = "✅" if not is_in_house else "❌"
                print(f"      {status} {loan}: {'IN-HOUSE' if is_in_house else 'EXTERNAL'}")
                
        except Exception as e:
            print(f"   ⚠️ Could not test perfect extraction: {e}")
        
        # 4. Test with actual tracker_results data
        print(f"\n4. 📊 TESTING WITH ACTUAL DATA:")
        
        db_path = get_database_path()
        if db_path:
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            
            # Get unique loan items from tracker_results
            cursor.execute("""
                SELECT DISTINCT item_label, tracker_type, COUNT(*) as count
                FROM tracker_results
                WHERE tracker_type IN ('IN_HOUSE_LOAN', 'EXTERNAL_LOAN')
                GROUP BY item_label, tracker_type
                ORDER BY tracker_type, count DESC
            """)
            
            actual_loans = cursor.fetchall()
            
            print("   📋 Actual loan classifications in tracker_results:")
            
            correct_classifications = 0
            total_classifications = 0
            
            for item_label, current_tracker_type, count in actual_loans:
                # Clean the loan name
                loan_name = item_label
                if ' - ' in loan_name:
                    loan_name = loan_name.split(' - ')[0].strip()
                
                # Test with dictionary
                dict_classification = dict_manager.classify_loan_type(loan_name)
                expected_tracker_type = 'IN_HOUSE_LOAN' if dict_classification == 'IN-HOUSE LOAN' else 'EXTERNAL_LOAN'
                
                is_correct = current_tracker_type == expected_tracker_type
                status = "✅" if is_correct else "❌"
                
                print(f"      {status} {loan_name} ({count} employees)")
                print(f"         Current: {current_tracker_type}")
                print(f"         Dictionary: {dict_classification}")
                print(f"         Expected: {expected_tracker_type}")
                
                if is_correct:
                    correct_classifications += 1
                total_classifications += 1
            
            conn.close()
            
            accuracy = (correct_classifications / total_classifications * 100) if total_classifications > 0 else 0
            print(f"\n   📊 Classification accuracy: {correct_classifications}/{total_classifications} ({accuracy:.1f}%)")
        
        # 5. Summary
        print(f"\n5. 📋 DICTIONARY-ONLY CLASSIFICATION SUMMARY:")
        print("=" * 45)
        
        in_house_accuracy = (in_house_correct / len(test_loans) * 100) if test_loans else 0
        external_accuracy = (external_correct / len(external_test_loans) * 100) if external_test_loans else 0
        
        print(f"✅ Dictionary manager: Working")
        print(f"✅ IN-HOUSE accuracy: {in_house_correct}/{len(test_loans)} ({in_house_accuracy:.1f}%)")
        print(f"✅ EXTERNAL accuracy: {external_correct}/{len(external_test_loans)} ({external_accuracy:.1f}%)")
        print(f"✅ No keyword fallback: Confirmed")
        print(f"✅ Dictionary-only logic: Enforced")
        
        if in_house_accuracy >= 80 and external_accuracy >= 80:
            print(f"\n🎉 DICTIONARY-ONLY CLASSIFICATION WORKING!")
            return True
        else:
            print(f"\n⚠️ CLASSIFICATION ACCURACY ISSUES DETECTED!")
            return False
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_dictionary_only_classification()
    
    if success:
        print("\n🎯 DICTIONARY-ONLY CLASSIFICATION TEST PASSED!")
        print("   Keyword fallback successfully removed")
    else:
        print("\n⚠️ DICTIONARY-ONLY CLASSIFICATION TEST FAILED!")
        print("   Issues detected with classification logic")
