﻿
interactive_pre_reporting.js:164 📊 Categorizing changes by bulk size and priority...
interactive_pre_reporting.js:230 🚀 Applying auto-selection rules...
interactive_pre_reporting.js:248 ✅ Auto-selected 656 changes
interactive_pre_reporting.js:119 🔄 Starting chunked rendering to prevent UI freeze...
interactive_pre_reporting.js:94 ✅ Loaded 16958 changes from database
interactive_pre_reporting.js:95 📊 Session used: audit_session_1750885462_afa94764
interactive_pre_reporting.js:164 📊 Categorizing changes by bulk size and priority...
interactive_pre_reporting.js:230 🚀 Applying auto-selection rules...
interactive_pre_reporting.js:248 ✅ Auto-selected 656 changes
interactive_pre_reporting.js:119 🔄 Starting chunked rendering to prevent UI freeze...
interactive_pre_reporting.js:94 ✅ Loaded 16958 changes from database
interactive_pre_reporting.js:95 📊 Session used: audit_session_1750885462_afa94764
interactive_pre_reporting.js:164 📊 Categorizing changes by bulk size and priority...
interactive_pre_reporting.js:230 🚀 Applying auto-selection rules...
interactive_pre_reporting.js:248 ✅ Auto-selected 656 changes
interactive_pre_reporting.js:119 🔄 Starting chunked rendering to prevent UI freeze...
interactive_pre_reporting.js:94 ✅ Loaded 16958 changes from database
interactive_pre_reporting.js:95 📊 Session used: audit_session_1750885462_afa94764
interactive_pre_reporting.js:164 📊 Categorizing changes by bulk size and priority...
interactive_pre_reporting.js:230 🚀 Applying auto-selection rules...
interactive_pre_reporting.js:248 ✅ Auto-selected 656 changes
interactive_pre_reporting.js:119 🔄 Starting chunked rendering to prevent UI freeze...
interactive_pre_reporting.js:94 ✅ Loaded 16958 changes from database
interactive_pre_reporting.js:95 📊 Session used: audit_session_1750885462_afa94764
interactive_pre_reporting.js:164 📊 Categorizing changes by bulk size and priority...
interactive_pre_reporting.js:230 🚀 Applying auto-selection rules...
interactive_pre_reporting.js:248 ✅ Auto-selected 656 changes
interactive_pre_reporting.js:119 🔄 Starting chunked rendering to prevent UI freeze...
interactive_pre_reporting.js:94 ✅ Loaded 16958 changes from database
interactive_pre_reporting.js:95 📊 Session used: audit_session_1750885462_afa94764
interactive_pre_reporting.js:164 📊 Categorizing changes by bulk size and priority...
interactive_pre_reporting.js:230 🚀 Applying auto-selection rules...
interactive_pre_reporting.js:248 ✅ Auto-selected 656 changes
interactive_pre_reporting.js:119 🔄 Starting chunked rendering to prevent UI freeze...
13
interactive_pre_reporting.js:155 ✅ Chunked rendering completed
index.html:2119 📂 Switched to tab: payroll-audit
index.html:2128 🔧 Initializing content for tab: payroll-audit
renderer.js:3768 🔍 Button state check:
renderer.js:3769   Current PDF: true
renderer.js:3770   Previous PDF: true
renderer.js:3771   Name: true
renderer.js:3772   Designation: true
renderer.js:3773   All met: true
renderer.js:3784 ✅ Button ENABLED
index.html:2195 🔍 Audit button state checked for payroll audit tab
﻿

