// Trigger Beautiful UI
// This script simulates the completion of all phases and triggers the beautiful pre-reporting UI

console.log('🎨 TRIGGERING BEAUTIFUL PRE-REPORTING UI...');

// Simulate phase completion
if (window.updateUIPhase) {
    console.log('📊 Updating UI phases...');
    
    // Mark all phases as complete
    window.updateUIPhase('EXTRACTION', 'Extraction completed', 100);
    window.updateUIPhase('COMPARISON', 'Comparison completed', 100);
    window.updateUIPhase('AUTO_LEARNING', 'Auto-learning completed', 100);
    window.updateUIPhase('TRACKER_FEEDING', 'Tracker feeding completed', 100);
    window.updateUIPhase('PRE_REPORTING', 'Loading pre-reporting data...', 85);
}

// Wait a moment then trigger pre-reporting UI
setTimeout(async () => {
    try {
        console.log('🔄 Loading pre-reporting data...');
        
        // Check if we have the pre-reporting API
        if (window.api && window.api.getLatestPreReportingData) {
            const preReportingData = await window.api.getLatestPreReportingData();
            
            if (preReportingData && preReportingData.success) {
                console.log('✅ Pre-reporting data loaded:', preReportingData.data.length, 'changes');
                
                // Initialize interactive pre-reporting if not already done
                if (!window.interactivePreReporting) {
                    console.log('🎯 Initializing Interactive Pre-Reporting...');
                    
                    // Load the interactive pre-reporting module
                    const script = document.createElement('script');
                    script.src = 'ui/interactive_pre_reporting.js';
                    script.onload = () => {
                        console.log('📱 Interactive Pre-Reporting loaded');
                        
                        // Initialize with container
                        const container = document.getElementById('pre-reporting-container') || 
                                        document.getElementById('main-content') ||
                                        document.body;
                        
                        if (window.InteractivePreReporting) {
                            window.interactivePreReporting = new window.InteractivePreReporting(container);
                            
                            // Render the beautiful UI
                            console.log('🎨 Rendering beautiful pre-reporting UI...');
                            window.interactivePreReporting.render(preReportingData.data);
                            
                            // Update phase to show completion
                            if (window.updateUIPhase) {
                                window.updateUIPhase('PRE_REPORTING', 'Pre-reporting ready for review', 100);
                            }
                            
                            console.log('🎉 BEAUTIFUL UI RENDERED SUCCESSFULLY!');
                        }
                    };
                    document.head.appendChild(script);
                } else {
                    // Pre-reporting already initialized, just render
                    console.log('🎨 Rendering beautiful pre-reporting UI...');
                    window.interactivePreReporting.render(preReportingData.data);
                    
                    if (window.updateUIPhase) {
                        window.updateUIPhase('PRE_REPORTING', 'Pre-reporting ready for review', 100);
                    }
                    
                    console.log('🎉 BEAUTIFUL UI RENDERED SUCCESSFULLY!');
                }
            } else {
                console.error('❌ Failed to load pre-reporting data:', preReportingData);
            }
        } else {
            console.error('❌ Pre-reporting API not available');
        }
    } catch (error) {
        console.error('❌ Error triggering beautiful UI:', error);
    }
}, 1000);

console.log('⏳ Beautiful UI trigger initiated...');
