#!/usr/bin/env python3
"""
Complete Database Schema Fix
Rebuilds the entire database schema to fix the "no such table: sessions" error
"""

import sqlite3
import json
import sys
import os
from datetime import datetime

def create_complete_database_schema():
    """Create complete database schema with all required tables"""
    
    try:
        print("🔧 COMPLETE DATABASE SCHEMA REBUILD:")
        
        # Connect to database
        conn = sqlite3.connect('payroll_audit.db')
        cursor = conn.cursor()
        
        print("1. 🗑️ Dropping existing tables (if any)...")
        
        # Drop existing tables to start fresh
        tables_to_drop = [
            'sessions', 'comparison_results', 'pre_reporting_results', 
            'tracker_results', 'in_house_loans', 'external_loans', 
            'motor_vehicle_maintenance', 'extracted_data'
        ]
        
        for table in tables_to_drop:
            try:
                cursor.execute(f"DROP TABLE IF EXISTS {table}")
                print(f"   Dropped {table}")
            except:
                pass
        
        print("\n2. 🏗️ Creating sessions table...")
        cursor.execute("""
            CREATE TABLE sessions (
                session_id TEXT PRIMARY KEY,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                is_current INTEGER DEFAULT 0,
                current_month TEXT,
                current_year TEXT,
                previous_month TEXT,
                previous_year TEXT,
                signature_name TEXT,
                signature_designation TEXT,
                report_type TEXT DEFAULT 'traditional',
                status TEXT DEFAULT 'active'
            )
        """)
        print("   ✅ Sessions table created")
        
        print("\n3. 🏗️ Creating comparison_results table...")
        cursor.execute("""
            CREATE TABLE comparison_results (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                session_id TEXT,
                employee_id TEXT,
                employee_name TEXT,
                section TEXT,
                item_label TEXT,
                change_type TEXT,
                current_value TEXT,
                previous_value TEXT,
                priority_level TEXT DEFAULT 'MODERATE',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (session_id) REFERENCES sessions(session_id)
            )
        """)
        print("   ✅ Comparison results table created")
        
        print("\n4. 🏗️ Creating pre_reporting_results table...")
        cursor.execute("""
            CREATE TABLE pre_reporting_results (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                session_id TEXT,
                change_id INTEGER,
                employee_id TEXT,
                employee_name TEXT,
                section TEXT,
                item_label TEXT,
                change_type TEXT,
                current_value TEXT,
                previous_value TEXT,
                priority_level TEXT,
                bulk_category TEXT,
                is_selected INTEGER DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (session_id) REFERENCES sessions(session_id)
            )
        """)
        print("   ✅ Pre-reporting results table created")
        
        print("\n5. 🏗️ Creating tracker_results table...")
        cursor.execute("""
            CREATE TABLE tracker_results (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                session_id TEXT,
                employee_id TEXT,
                employee_name TEXT,
                item_label TEXT,
                item_value TEXT,
                numeric_value REAL,
                tracker_type TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (session_id) REFERENCES sessions(session_id)
            )
        """)
        print("   ✅ Tracker results table created")
        
        print("\n6. 🏗️ Creating Bank Adviser tables...")
        
        # In-house loans table
        cursor.execute("""
            CREATE TABLE in_house_loans (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                employee_no TEXT,
                employee_name TEXT,
                department TEXT,
                loan_type TEXT,
                loan_amount REAL,
                period_month TEXT,
                period_year TEXT,
                period_acquired TEXT,
                source_session TEXT,
                remarks TEXT DEFAULT 'Monitoring',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)
        print("   ✅ In-house loans table created")
        
        # External loans table
        cursor.execute("""
            CREATE TABLE external_loans (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                employee_no TEXT,
                employee_name TEXT,
                department TEXT,
                loan_type TEXT,
                loan_amount REAL,
                period_month TEXT,
                period_year TEXT,
                period_acquired TEXT,
                source_session TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)
        print("   ✅ External loans table created")
        
        # Motor vehicle maintenance table
        cursor.execute("""
            CREATE TABLE motor_vehicle_maintenance (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                employee_no TEXT,
                employee_name TEXT,
                department TEXT,
                allowance_type TEXT,
                allowance_amount REAL,
                payable_amount REAL,
                maintenance_amount REAL,
                period_month TEXT,
                period_year TEXT,
                period_acquired TEXT,
                source_session TEXT,
                remarks TEXT DEFAULT 'Monitoring',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)
        print("   ✅ Motor vehicle maintenance table created")
        
        print("\n7. 🏗️ Creating extracted_data table...")
        cursor.execute("""
            CREATE TABLE extracted_data (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                session_id TEXT,
                employee_no TEXT,
                employee_name TEXT,
                department TEXT,
                section TEXT,
                item_label TEXT,
                item_value TEXT,
                numeric_value REAL,
                extraction_confidence REAL DEFAULT 1.0,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (session_id) REFERENCES sessions(session_id)
            )
        """)
        print("   ✅ Extracted data table created")
        
        print("\n8. 🎯 Creating current session...")
        
        # Create a current session
        current_session = f"audit_session_{int(datetime.now().timestamp())}_fixed"
        
        cursor.execute("""
            INSERT INTO sessions 
            (session_id, is_current, current_month, current_year, 
             previous_month, previous_year, signature_name, signature_designation)
            VALUES (?, 1, ?, ?, ?, ?, ?, ?)
        """, (
            current_session, 'June', '2025', 'May', '2025', 'SAM', 'AUDIT'
        ))
        print(f"   ✅ Current session created: {current_session}")
        
        # Commit all changes
        conn.commit()
        conn.close()
        
        print("\n✅ DATABASE SCHEMA REBUILD COMPLETE!")
        print(f"   Current session: {current_session}")
        print("   All tables created successfully")
        
        return {
            'success': True,
            'session_id': current_session,
            'tables_created': [
                'sessions', 'comparison_results', 'pre_reporting_results',
                'tracker_results', 'in_house_loans', 'external_loans',
                'motor_vehicle_maintenance', 'extracted_data'
            ]
        }
        
    except Exception as e:
        print(f"❌ Error rebuilding database schema: {e}")
        return {'success': False, 'error': str(e)}

def main():
    """Main entry point"""
    result = create_complete_database_schema()
    print(json.dumps(result))

if __name__ == '__main__':
    main()
