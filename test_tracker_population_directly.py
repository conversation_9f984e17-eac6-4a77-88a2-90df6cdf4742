#!/usr/bin/env python3
"""
Test Tracker Population Directly
Manually trigger the tracker population to see what's happening
"""

import os
import sys
import sqlite3
import json
from pathlib import Path

def get_database_path():
    """Get the database path"""
    db_path = Path(__file__).parent / "data" / "templar_payroll_auditor.db"
    if db_path.exists():
        return str(db_path)
    return None

def test_tracker_population():
    """Test tracker population directly"""
    
    print("🧪 TESTING TRACKER POPULATION DIRECTLY")
    print("=" * 40)
    
    try:
        db_path = get_database_path()
        if not db_path:
            print("❌ Database file not found")
            return False
        
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 1. Check current session
        print("1. 📊 Getting current session...")
        
        cursor.execute("""
            SELECT session_id FROM audit_sessions 
            ORDER BY created_at DESC 
            LIMIT 1
        """)
        session_result = cursor.fetchone()
        
        if not session_result:
            print("   ❌ No sessions found")
            return False
        
        current_session = session_result[0]
        print(f"   ✅ Current session: {current_session}")
        
        # 2. Check tracker_results data
        print(f"\n2. 📊 Checking tracker_results for session...")
        
        cursor.execute("""
            SELECT tracker_type, COUNT(*) as count
            FROM tracker_results 
            WHERE session_id = ?
            GROUP BY tracker_type
        """, (current_session,))
        
        tracker_data = cursor.fetchall()
        
        if not tracker_data:
            print("   ❌ No tracker_results for current session")
            # Check if there's any tracker_results at all
            cursor.execute("SELECT DISTINCT session_id FROM tracker_results LIMIT 5")
            all_sessions = cursor.fetchall()
            print(f"   Available sessions in tracker_results: {[s[0] for s in all_sessions]}")
            
            if all_sessions:
                # Use the first available session
                current_session = all_sessions[0][0]
                print(f"   🔄 Using available session: {current_session}")
                
                cursor.execute("""
                    SELECT tracker_type, COUNT(*) as count
                    FROM tracker_results 
                    WHERE session_id = ?
                    GROUP BY tracker_type
                """, (current_session,))
                tracker_data = cursor.fetchall()
        
        if tracker_data:
            print("   ✅ Tracker data found:")
            for tracker_type, count in tracker_data:
                print(f"      {tracker_type}: {count} items")
        else:
            print("   ❌ No tracker data found")
            return False
        
        # 3. Test the population method directly
        print(f"\n3. 🧪 Testing population method...")
        
        try:
            sys.path.append(os.path.dirname(__file__))
            
            # Import and run the population script
            import bank_adviser_tracker_operations
            
            # Test the populate_bank_adviser_tables function
            print("   🔄 Running populate_bank_adviser_tables...")
            
            # We need to simulate the command line call
            original_argv = sys.argv
            sys.argv = ['bank_adviser_tracker_operations.py', 'populate_tables']
            
            try:
                # Capture the output
                import io
                import contextlib
                
                output_buffer = io.StringIO()
                with contextlib.redirect_stdout(output_buffer):
                    bank_adviser_tracker_operations.main()
                
                output = output_buffer.getvalue()
                print(f"   📋 Population output:")
                print(f"      {output}")
                
                # Try to parse as JSON
                try:
                    result = json.loads(output.strip())
                    print(f"   📊 Population result: {result}")
                except:
                    print(f"   ⚠️ Could not parse output as JSON")
                
            finally:
                sys.argv = original_argv
                
        except Exception as e:
            print(f"   ❌ Population method test failed: {e}")
            import traceback
            traceback.print_exc()
        
        # 4. Check if data was populated
        print(f"\n4. 📊 Checking if data was populated...")
        
        tables = ['in_house_loans', 'external_loans', 'motor_vehicle_maintenance']
        
        for table in tables:
            cursor.execute(f"SELECT COUNT(*) FROM {table}")
            count = cursor.fetchone()[0]
            print(f"   {table}: {count} records")
            
            if count > 0:
                cursor.execute(f"SELECT * FROM {table} LIMIT 3")
                samples = cursor.fetchall()
                print(f"      Sample records:")
                for i, sample in enumerate(samples):
                    print(f"      {i+1}. Employee: {sample[1] if len(sample) > 1 else 'N/A'}")
        
        # 5. Manual population test
        print(f"\n5. 🔧 Manual population test...")
        
        # Get some tracker_results data
        cursor.execute("""
            SELECT employee_id, employee_name, item_label, item_value, numeric_value, tracker_type
            FROM tracker_results 
            WHERE session_id = ? 
            LIMIT 5
        """, (current_session,))
        
        sample_data = cursor.fetchall()
        
        if sample_data:
            print("   📊 Sample tracker_results data:")
            for row in sample_data:
                print(f"      {row[0]} - {row[2]} ({row[5]}): {row[4]}")
            
            # Try to manually insert one record
            print("   🧪 Testing manual insertion...")
            
            sample = sample_data[0]
            if sample[5] == 'IN_HOUSE_LOAN':
                try:
                    cursor.execute("""
                        INSERT INTO in_house_loans 
                        (employee_no, employee_name, department, loan_type, loan_amount,
                         period_month, period_year, period_acquired, source_session, remarks)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    """, (
                        sample[0],  # employee_id
                        sample[1],  # employee_name
                        'Test Department',  # department
                        sample[2],  # item_label as loan_type
                        sample[4] or 0,  # numeric_value
                        '06',  # current month
                        '2025',  # current year
                        '2025-06',  # period_acquired
                        current_session,  # source_session
                        'Manual Test'  # remarks
                    ))
                    
                    conn.commit()
                    print("   ✅ Manual insertion successful")
                    
                    # Check if it was inserted
                    cursor.execute("SELECT COUNT(*) FROM in_house_loans WHERE remarks = 'Manual Test'")
                    test_count = cursor.fetchone()[0]
                    print(f"   📊 Test records in database: {test_count}")
                    
                except Exception as e:
                    print(f"   ❌ Manual insertion failed: {e}")
        
        conn.close()
        
        print(f"\n6. 📋 TRACKER POPULATION TEST SUMMARY:")
        print("=" * 35)
        print("✅ Tracker data exists in tracker_results")
        print("✅ Population method can be called")
        print("⚠️ Need to verify why automatic population isn't working")
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_tracker_population()
    
    if success:
        print("\n🎯 TRACKER POPULATION TEST COMPLETED!")
        print("   Check the output above for issues")
    else:
        print("\n⚠️ TRACKER POPULATION TEST FAILED!")
        print("   Manual intervention required")
