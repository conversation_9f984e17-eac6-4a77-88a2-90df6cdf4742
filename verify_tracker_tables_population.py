#!/usr/bin/env python3
"""
Verify Loans & Allowance Tracker Tables Population
Checks if IN HOUSE LOAN, EXTERNAL LOAN, and Motor Vehicle Allowance tables are properly populated
"""

import os
import sys
import sqlite3
from pathlib import Path

def get_database_path():
    """Get the database path"""
    db_path = Path(__file__).parent / "data" / "templar_payroll_auditor.db"
    if db_path.exists():
        return str(db_path)
    return None

def verify_tracker_tables():
    """Verify tracker tables population and methods"""
    
    print("🔍 VERIFYING LOANS & ALLOWANCE TRACKER TABLES")
    print("=" * 50)
    
    try:
        db_path = get_database_path()
        if not db_path:
            print("❌ Database file not found")
            return False
        
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 1. Check if tracker tables exist
        print("1. 📊 CHECKING TRACKER TABLES EXISTENCE:")
        
        tracker_tables = [
            'in_house_loans',
            'external_loans', 
            'motor_vehicle_maintenance'
        ]
        
        existing_tables = []
        for table in tracker_tables:
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name=?", (table,))
            if cursor.fetchone():
                existing_tables.append(table)
                print(f"   ✅ {table} table exists")
            else:
                print(f"   ❌ {table} table missing")
        
        if len(existing_tables) != len(tracker_tables):
            print(f"   ⚠️ Only {len(existing_tables)}/{len(tracker_tables)} tracker tables exist")
        
        # 2. Check table schemas
        print(f"\n2. 🔍 CHECKING TABLE SCHEMAS:")
        
        for table in existing_tables:
            cursor.execute(f"PRAGMA table_info({table})")
            columns = cursor.fetchall()
            print(f"   📋 {table} columns: {len(columns)}")
            for col in columns[:5]:  # Show first 5 columns
                print(f"      - {col[1]} ({col[2]})")
            if len(columns) > 5:
                print(f"      ... and {len(columns) - 5} more columns")
        
        # 3. Check data population
        print(f"\n3. 📊 CHECKING DATA POPULATION:")
        
        total_records = 0
        for table in existing_tables:
            cursor.execute(f"SELECT COUNT(*) FROM {table}")
            count = cursor.fetchone()[0]
            total_records += count
            print(f"   📈 {table}: {count} records")
            
            if count > 0:
                # Show sample data
                cursor.execute(f"SELECT * FROM {table} LIMIT 3")
                samples = cursor.fetchall()
                cursor.execute(f"PRAGMA table_info({table})")
                column_names = [col[1] for col in cursor.fetchall()]
                
                print(f"      Sample data:")
                for i, sample in enumerate(samples):
                    print(f"      {i+1}. Employee: {sample[1] if len(sample) > 1 else 'N/A'}")
                    if table == 'in_house_loans' or table == 'external_loans':
                        loan_type_idx = column_names.index('loan_type') if 'loan_type' in column_names else 3
                        amount_idx = column_names.index('loan_amount') if 'loan_amount' in column_names else 4
                        print(f"         Loan: {sample[loan_type_idx] if len(sample) > loan_type_idx else 'N/A'}")
                        print(f"         Amount: {sample[amount_idx] if len(sample) > amount_idx else 'N/A'}")
                    elif table == 'motor_vehicle_maintenance':
                        print(f"         Type: Motor Vehicle Maintenance")
                        amount_idx = column_names.index('maintenance_amount') if 'maintenance_amount' in column_names else 3
                        print(f"         Amount: {sample[amount_idx] if len(sample) > amount_idx else 'N/A'}")
        
        # 4. Check recent sessions
        print(f"\n4. 🔍 CHECKING RECENT SESSIONS:")
        
        cursor.execute("""
            SELECT session_id, created_at 
            FROM audit_sessions 
            ORDER BY created_at DESC 
            LIMIT 3
        """)
        sessions = cursor.fetchall()
        
        if sessions:
            print(f"   Recent sessions:")
            for session_id, created_at in sessions:
                print(f"   - {session_id} ({created_at})")
                
                # Check if this session has tracker data
                session_data = {}
                for table in existing_tables:
                    cursor.execute(f"SELECT COUNT(*) FROM {table} WHERE source_session = ?", (session_id,))
                    session_count = cursor.fetchone()[0]
                    session_data[table] = session_count
                
                print(f"     Tracker data: {session_data}")
        else:
            print("   ❌ No audit sessions found")
        
        # 5. Check tracker feeding methods
        print(f"\n5. 🔧 CHECKING TRACKER FEEDING METHODS:")
        
        # Check if tracker_results table exists (intermediate table)
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='tracker_results'")
        if cursor.fetchone():
            cursor.execute("SELECT COUNT(*) FROM tracker_results")
            tracker_results_count = cursor.fetchone()[0]
            print(f"   ✅ tracker_results table exists: {tracker_results_count} records")
            
            if tracker_results_count > 0:
                # Check tracker types
                cursor.execute("SELECT DISTINCT tracker_type FROM tracker_results")
                tracker_types = [row[0] for row in cursor.fetchall()]
                print(f"   📊 Tracker types: {tracker_types}")
        else:
            print("   ⚠️ tracker_results table not found")
        
        # 6. Test population methods
        print(f"\n6. 🧪 TESTING POPULATION METHODS:")
        
        try:
            sys.path.append(os.path.dirname(__file__))
            
            # Test if the API method exists
            from main import app
            if hasattr(app, 'api') and hasattr(app.api, 'populateTrackerTables'):
                print("   ✅ populateTrackerTables API method exists")
            else:
                print("   ⚠️ populateTrackerTables API method not found")
            
            # Check if the backend method exists
            try:
                from core.phased_process_manager import PhasedProcessManager
                manager = PhasedProcessManager()
                if hasattr(manager, 'populate_tracker_tables'):
                    print("   ✅ populate_tracker_tables backend method exists")
                else:
                    print("   ⚠️ populate_tracker_tables backend method not found")
            except Exception as e:
                print(f"   ⚠️ Could not test backend method: {e}")
                
        except Exception as e:
            print(f"   ⚠️ Could not test population methods: {e}")
        
        # 7. Check enforcement rules
        print(f"\n7. 🛡️ CHECKING ENFORCEMENT RULES:")
        
        # Check for duplicate prevention
        for table in existing_tables:
            if table in ['in_house_loans', 'external_loans']:
                cursor.execute(f"""
                    SELECT employee_no, loan_type, period_month, period_year, COUNT(*) as count
                    FROM {table}
                    GROUP BY employee_no, loan_type, period_month, period_year
                    HAVING COUNT(*) > 1
                """)
                duplicates = cursor.fetchall()
                
                if duplicates:
                    print(f"   ⚠️ {table}: {len(duplicates)} duplicate entries found")
                    for dup in duplicates[:3]:  # Show first 3
                        print(f"      - {dup[0]} {dup[1]} {dup[2]}/{dup[3]}: {dup[4]} entries")
                else:
                    print(f"   ✅ {table}: No duplicates found")
        
        # Check data quality
        for table in existing_tables:
            cursor.execute(f"SELECT COUNT(*) FROM {table} WHERE employee_no IS NULL OR employee_no = ''")
            null_employees = cursor.fetchone()[0]
            
            if null_employees > 0:
                print(f"   ⚠️ {table}: {null_employees} records with missing employee numbers")
            else:
                print(f"   ✅ {table}: All records have employee numbers")
        
        conn.close()
        
        # 8. Summary
        print(f"\n8. 📋 TRACKER TABLES SUMMARY:")
        print("=" * 30)
        
        if len(existing_tables) == len(tracker_tables) and total_records > 0:
            print("✅ Tracker tables: All exist and populated")
            print(f"✅ Total records: {total_records}")
            print("✅ Data quality: Good")
            print("✅ Methods: Available")
            return True
        else:
            print("⚠️ Tracker tables: Issues detected")
            print(f"   Tables: {len(existing_tables)}/{len(tracker_tables)} exist")
            print(f"   Records: {total_records}")
            return False
        
    except Exception as e:
        print(f"❌ Verification failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = verify_tracker_tables()
    
    if success:
        print("\n🎉 TRACKER TABLES VERIFICATION PASSED!")
        print("   All tables exist and are properly populated")
    else:
        print("\n⚠️ TRACKER TABLES VERIFICATION FAILED!")
        print("   Some issues detected with tracker tables")
