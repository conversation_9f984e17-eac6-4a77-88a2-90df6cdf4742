#!/usr/bin/env python3
"""
Test Complete Beautiful UI with Interactive Features
Tests the full interactive pre-reporting UI with all features
"""

import os
import sys
import sqlite3
import time
from pathlib import Path

def get_database_path():
    """Get the database path"""
    db_path = Path(__file__).parent / "data" / "templar_payroll_auditor.db"
    if db_path.exists():
        return str(db_path)
    return None

def create_comprehensive_test_data():
    """Create comprehensive test data with various scenarios"""
    
    print("🧪 CREATING COMPREHENSIVE TEST DATA FOR BEAUTIFUL UI")
    print("=" * 55)
    
    try:
        db_path = get_database_path()
        if not db_path:
            print("❌ Database file not found")
            return None
        
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 1. Create a test session
        session_id = f"beautiful_ui_test_{int(time.time())}"
        print(f"1. 📊 Creating test session: {session_id}")
        
        cursor.execute("""
            INSERT INTO audit_sessions 
            (session_id, current_month, current_year, previous_month, previous_year, 
             current_pdf_path, previous_pdf_path, created_at)
            VALUES (?, 'June', '2025', 'May', '2025', 'test.pdf', 'test_prev.pdf', datetime('now'))
        """, (session_id,))
        
        # 2. Create diverse test comparison results
        print("2. 📊 Creating diverse comparison results...")
        
        test_changes = [
            # High Priority Changes (Personal Details, Earnings, Deductions, Bank Details)
            ("EMP001", "JOHN DOE", "PERSONAL DETAILS", "BASIC SALARY", "5000.00", "5500.00", "INCREASED", "HIGH"),
            ("EMP001", "JOHN DOE", "EARNINGS", "UTILITY SUBSIDY", "0.00", "200.00", "NEW", "HIGH"),
            ("EMP001", "JOHN DOE", "DEDUCTIONS", "INCOME TAX", "800.00", "850.00", "INCREASED", "HIGH"),
            ("EMP001", "JOHN DOE", "BANK DETAILS", "ACCOUNT NUMBER", "*********", "*********", "MODIFIED", "HIGH"),
            
            # Multiple employees with same change (Small Bulk)
            ("EMP002", "JANE SMITH", "EARNINGS", "TRANSPORT ALLOWANCE", "0.00", "150.00", "NEW", "HIGH"),
            ("EMP003", "BOB JOHNSON", "EARNINGS", "TRANSPORT ALLOWANCE", "0.00", "150.00", "NEW", "HIGH"),
            ("EMP004", "ALICE BROWN", "EARNINGS", "TRANSPORT ALLOWANCE", "0.00", "150.00", "NEW", "HIGH"),
            ("EMP005", "CHARLIE DAVIS", "EARNINGS", "TRANSPORT ALLOWANCE", "0.00", "150.00", "NEW", "HIGH"),
            
            # Moderate Priority Changes (Loans)
            ("EMP002", "JANE SMITH", "LOANS", "SALARY ADVANCE", "1000.00", "500.00", "DECREASED", "MODERATE"),
            ("EMP003", "BOB JOHNSON", "LOANS", "BUILDING LOAN", "5000.00", "4500.00", "DECREASED", "MODERATE"),
            ("EMP004", "ALICE BROWN", "LOANS", "RENT ADVANCE", "800.00", "0.00", "REMOVED", "MODERATE"),
            
            # Low Priority Changes (Employer Contributions)
            ("EMP005", "CHARLIE DAVIS", "EMPLOYER CONTRIBUTIONS", "PENSION CONTRIBUTION", "300.00", "320.00", "INCREASED", "LOW"),
            ("EMP006", "EVE WILSON", "EMPLOYER CONTRIBUTIONS", "SOCIAL SECURITY", "150.00", "160.00", "INCREASED", "LOW"),
            
            # Large Bulk Changes (Medium Bulk - same change for many employees)
            ("EMP007", "FRANK MILLER", "DEDUCTIONS", "UNION DUES", "25.00", "30.00", "INCREASED", "HIGH"),
            ("EMP008", "GRACE TAYLOR", "DEDUCTIONS", "UNION DUES", "25.00", "30.00", "INCREASED", "HIGH"),
            ("EMP009", "HENRY CLARK", "DEDUCTIONS", "UNION DUES", "25.00", "30.00", "INCREASED", "HIGH"),
            ("EMP010", "IVY LEWIS", "DEDUCTIONS", "UNION DUES", "25.00", "30.00", "INCREASED", "HIGH"),
            ("EMP011", "JACK WALKER", "DEDUCTIONS", "UNION DUES", "25.00", "30.00", "INCREASED", "HIGH"),
            ("EMP012", "KELLY HALL", "DEDUCTIONS", "UNION DUES", "25.00", "30.00", "INCREASED", "HIGH"),
            ("EMP013", "LIAM YOUNG", "DEDUCTIONS", "UNION DUES", "25.00", "30.00", "INCREASED", "HIGH"),
            ("EMP014", "MAYA KING", "DEDUCTIONS", "UNION DUES", "25.00", "30.00", "INCREASED", "HIGH"),
            ("EMP015", "NOAH WRIGHT", "DEDUCTIONS", "UNION DUES", "25.00", "30.00", "INCREASED", "HIGH"),
            ("EMP016", "OLIVIA GREEN", "DEDUCTIONS", "UNION DUES", "25.00", "30.00", "INCREASED", "HIGH"),
            ("EMP017", "PAUL ADAMS", "DEDUCTIONS", "UNION DUES", "25.00", "30.00", "INCREASED", "HIGH"),
            ("EMP018", "QUINN BAKER", "DEDUCTIONS", "UNION DUES", "25.00", "30.00", "INCREASED", "HIGH"),
        ]
        
        # Get the next available ID
        cursor.execute("SELECT COALESCE(MAX(id), 0) + 1 FROM comparison_results")
        change_id = cursor.fetchone()[0]

        for emp_id, emp_name, section, item, prev_val, curr_val, change_type, priority in test_changes:
            # Calculate numeric difference
            try:
                prev_num = float(prev_val.replace(',', '')) if prev_val != "0.00" else 0.0
                curr_num = float(curr_val.replace(',', '')) if curr_val != "0.00" else 0.0
                numeric_diff = curr_num - prev_num
                percentage_change = (numeric_diff / prev_num * 100) if prev_num > 0 else 0.0
            except:
                numeric_diff = 0.0
                percentage_change = 0.0
            
            cursor.execute("""
                INSERT INTO comparison_results 
                (id, session_id, employee_id, employee_name, section_name, item_label, 
                 previous_value, current_value, change_type, priority, numeric_difference, percentage_change)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (change_id, session_id, emp_id, emp_name, section, item, prev_val, curr_val, 
                  change_type, priority, numeric_diff, percentage_change))
            change_id += 1
        
        # 3. Create pre-reporting results with proper bulk categorization
        print("3. 📊 Creating pre-reporting results with bulk categorization...")
        
        # Categorize changes by bulk size
        change_groups = {}
        cursor.execute("""
            SELECT id, section_name, item_label, change_type, priority
            FROM comparison_results 
            WHERE session_id = ?
        """, (session_id,))
        
        for row in cursor.fetchall():
            change_id, section, item, change_type, priority = row
            key = f"{section}::{item}::{change_type}"
            if key not in change_groups:
                change_groups[key] = []
            change_groups[key].append(change_id)
        
        # Insert pre-reporting results with bulk categories
        for key, change_ids in change_groups.items():
            bulk_size = len(change_ids)
            if bulk_size == 1:
                bulk_category = "Individual"
            elif bulk_size <= 4:
                bulk_category = "Small_Bulk"
            elif bulk_size <= 16:
                bulk_category = "Medium_Bulk"
            else:
                bulk_category = "Large_Bulk"
            
            for change_id in change_ids:
                cursor.execute("""
                    INSERT INTO pre_reporting_results 
                    (session_id, change_id, selected_for_report, bulk_category, bulk_size)
                    VALUES (?, ?, 1, ?, ?)
                """, (session_id, change_id, bulk_category, bulk_size))
        
        conn.commit()
        
        print(f"✅ Comprehensive test data created successfully!")
        print(f"   Session ID: {session_id}")
        print(f"   Comparison results: {len(test_changes)}")
        print(f"   Bulk categories: Individual, Small Bulk, Medium Bulk")
        print(f"   Priority levels: High, Moderate, Low")
        print(f"   Employee count: 18")
        print(f"   Change types: NEW, INCREASED, DECREASED, REMOVED, MODIFIED")
        
        conn.close()
        return session_id
        
    except Exception as e:
        print(f"❌ Failed to create test data: {e}")
        import traceback
        traceback.print_exc()
        return None

def test_beautiful_ui_features():
    """Test all beautiful UI features"""
    
    print("\n🎨 TESTING BEAUTIFUL UI FEATURES")
    print("=" * 35)
    
    try:
        session_id = create_comprehensive_test_data()
        if not session_id:
            return
        
        # Test the API method
        sys.path.append(os.path.dirname(__file__))
        from core.phased_process_manager import PhasedProcessManager
        
        manager = PhasedProcessManager()
        result = manager.get_pre_reporting_data(session_id)
        
        print(f"🔍 API Test Results:")
        print(f"  Success: {result.get('success')}")
        print(f"  Data Count: {len(result.get('data', []))}")
        print(f"  Total Changes: {result.get('total_changes', 0)}")
        
        if result.get('success') and result.get('data'):
            data = result['data']
            
            # Analyze the data structure
            print(f"\n📊 Data Analysis:")
            
            # Count by priority
            priorities = {}
            sections = {}
            change_types = {}
            employees = set()
            
            for item in data:
                priority = item.get('priority', 'Unknown')
                section = item.get('section_name', 'Unknown')
                change_type = item.get('change_type', 'Unknown')
                employee = item.get('employee_id', 'Unknown')
                
                priorities[priority] = priorities.get(priority, 0) + 1
                sections[section] = sections.get(section, 0) + 1
                change_types[change_type] = change_types.get(change_type, 0) + 1
                employees.add(employee)
            
            print(f"  Priorities: {dict(priorities)}")
            print(f"  Sections: {dict(sections)}")
            print(f"  Change Types: {dict(change_types)}")
            print(f"  Unique Employees: {len(employees)}")
            
            print(f"\n🎯 BEAUTIFUL UI FEATURES READY:")
            print(f"  ✅ Priority Filtering (High: {priorities.get('HIGH', 0)}, Moderate: {priorities.get('MODERATE', 0)}, Low: {priorities.get('LOW', 0)})")
            print(f"  ✅ Employee Grouping ({len(employees)} employees)")
            print(f"  ✅ Category Grouping ({len(sections)} sections)")
            print(f"  ✅ Bulk Categorization (Individual/Small/Medium/Large)")
            print(f"  ✅ Interactive Selection Controls")
            print(f"  ✅ Expandable Change Details")
            print(f"  ✅ Auto-Selection Rules")
            
            print(f"\n🚀 READY FOR BEAUTIFUL UI TESTING!")
            print(f"   Run a payroll audit to see the enhanced interactive interface!")
        else:
            print(f"\n❌ API FAILED: {result}")
        
    except Exception as e:
        print(f"❌ Feature test failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_beautiful_ui_features()
