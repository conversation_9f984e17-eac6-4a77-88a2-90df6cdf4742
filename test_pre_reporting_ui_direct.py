#!/usr/bin/env python3
"""
Test Pre-Reporting UI Direct Trigger
Creates test data and triggers the pre-reporting UI directly
"""

import os
import sys
import sqlite3
import time
from pathlib import Path

def get_database_path():
    """Get the database path"""
    db_path = Path(__file__).parent / "data" / "templar_payroll_auditor.db"
    if db_path.exists():
        return str(db_path)
    return None

def create_test_pre_reporting_data():
    """Create test pre-reporting data in the database"""
    
    print("🧪 CREATING TEST PRE-REPORTING DATA")
    print("=" * 40)
    
    try:
        db_path = get_database_path()
        if not db_path:
            print("❌ Database file not found")
            return None
        
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 1. Create a test session
        session_id = f"test_session_{int(time.time())}"
        print(f"1. 📊 Creating test session: {session_id}")
        
        cursor.execute("""
            INSERT INTO audit_sessions
            (session_id, current_month, current_year, previous_month, previous_year,
             current_pdf_path, previous_pdf_path, created_at)
            VALUES (?, 'June', '2025', 'May', '2025', 'test.pdf', 'test_prev.pdf', datetime('now'))
        """, (session_id,))
        
        # 2. Create test comparison results
        print("2. 📊 Creating test comparison results...")
        
        test_changes = [
            ("EMP001", "JOHN DOE", "PERSONAL DETAILS", "BASIC SALARY", "5000.00", "5500.00", "INCREASED", "HIGH"),
            ("EMP001", "JOHN DOE", "EARNINGS", "UTILITY SUBSIDY", "0.00", "200.00", "NEW", "HIGH"),
            ("EMP002", "JANE SMITH", "DEDUCTIONS", "INCOME TAX", "800.00", "850.00", "INCREASED", "HIGH"),
            ("EMP003", "BOB JOHNSON", "LOANS", "SALARY ADVANCE", "1000.00", "500.00", "DECREASED", "MODERATE"),
            ("EMP004", "ALICE BROWN", "BANK DETAILS", "ACCOUNT NUMBER", "*********", "*********", "MODIFIED", "HIGH"),
        ]
        
        change_id = 1
        for emp_id, emp_name, section, item, prev_val, curr_val, change_type, priority in test_changes:
            cursor.execute("""
                INSERT INTO comparison_results 
                (id, session_id, employee_id, employee_name, section_name, item_label, 
                 previous_value, current_value, change_type, priority, numeric_difference, percentage_change)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 0.0, 0.0)
            """, (change_id, session_id, emp_id, emp_name, section, item, prev_val, curr_val, change_type, priority))
            change_id += 1
        
        # 3. Create pre-reporting results
        print("3. 📊 Creating pre-reporting results...")
        
        for i in range(1, 6):  # For each comparison result
            cursor.execute("""
                INSERT INTO pre_reporting_results 
                (session_id, change_id, selected_for_report, bulk_category, bulk_size)
                VALUES (?, ?, 1, 'Individual', 1)
            """, (session_id, i))
        
        conn.commit()
        
        print(f"✅ Test data created successfully!")
        print(f"   Session ID: {session_id}")
        print(f"   Comparison results: 5")
        print(f"   Pre-reporting results: 5")
        
        conn.close()
        return session_id
        
    except Exception as e:
        print(f"❌ Failed to create test data: {e}")
        import traceback
        traceback.print_exc()
        return None

def test_pre_reporting_api():
    """Test the pre-reporting API method"""
    
    print("\n🧪 TESTING PRE-REPORTING API")
    print("=" * 30)
    
    try:
        session_id = create_test_pre_reporting_data()
        if not session_id:
            return
        
        # Test the API method
        sys.path.append(os.path.dirname(__file__))
        from core.phased_process_manager import PhasedProcessManager
        
        manager = PhasedProcessManager()
        result = manager.get_pre_reporting_data(session_id)
        
        print(f"API Result:")
        print(f"  Success: {result.get('success')}")
        print(f"  Data Count: {len(result.get('data', []))}")
        print(f"  Total Changes: {result.get('total_changes', 0)}")
        print(f"  Session ID: {result.get('session_id', 'unknown')}")
        
        if result.get('success') and result.get('data'):
            print("\nSample Data:")
            sample = result['data'][0]
            for key, value in sample.items():
                print(f"  {key}: {value}")
            
            print("\n🎯 API IS WORKING - UI SHOULD BE ABLE TO LOAD DATA!")
        else:
            print(f"\n❌ API FAILED: {result}")
        
    except Exception as e:
        print(f"❌ API test failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_pre_reporting_api()
