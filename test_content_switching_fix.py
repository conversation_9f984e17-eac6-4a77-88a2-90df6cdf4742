#!/usr/bin/env python3
"""
TEST CONTENT SWITCHING MANAGER FIX
Verifies that the ContentSwitchingManager fix resolves the pre-reporting UI issue
"""

import os
import re

def test_content_switching_fix():
    print("🔧 TESTING CONTENT SWITCHING MANAGER FIX")
    print("=" * 60)
    
    # 1. Check that early initialization is in place
    print("\n1. 🔍 CHECKING EARLY INITIALIZATION:")
    
    try:
        with open('renderer.js', 'r', encoding='utf-8') as f:
            content = f.read()
        
        if 'ensureContentSwitchingManager()' in content:
            print("   ✅ Early initialization function present")
        else:
            print("   ❌ Early initialization function missing")
        
        if 'Early ContentSwitchingManager initialization' in content:
            print("   ✅ Early initialization comment present")
        else:
            print("   ❌ Early initialization comment missing")
        
        if 'window.contentSwitchingManager = new window.ContentSwitchingManager()' in content:
            print("   ✅ Instance creation code present")
        else:
            print("   ❌ Instance creation code missing")
            
    except Exception as e:
        print(f"   ❌ Error checking early initialization: {e}")
    
    # 2. Check that switchToPhase method is available
    print("\n2. 🔍 CHECKING SWITCHTO PHASE METHOD:")
    
    try:
        switchToPhase_count = content.count('switchToPhase(phase, data = {})')
        if switchToPhase_count >= 2:
            print(f"   ✅ switchToPhase method found {switchToPhase_count} times")
        else:
            print(f"   ❌ switchToPhase method found only {switchToPhase_count} times")
        
        # Check for the specific error-prone call
        if 'window.contentSwitchingManager.switchToPhase(\'pre-reporting\')' in content:
            print("   ✅ Pre-reporting switchToPhase call present")
        else:
            print("   ❌ Pre-reporting switchToPhase call missing")
            
    except Exception as e:
        print(f"   ❌ Error checking switchToPhase method: {e}")
    
    # 3. Check that safety checks are in place
    print("\n3. 🔍 CHECKING SAFETY CHECKS:")
    
    try:
        if 'if (!window.contentSwitchingManager)' in content:
            print("   ✅ Safety check for missing instance present")
        else:
            print("   ❌ Safety check for missing instance missing")
        
        if 'typeof window.contentSwitchingManager.switchToPhase === \'function\'' in content:
            print("   ✅ Function type check present")
        else:
            print("   ❌ Function type check missing")
        
        if 'ContentSwitchingManager instance created on-demand' in content:
            print("   ✅ On-demand creation logic present")
        else:
            print("   ❌ On-demand creation logic missing")
            
    except Exception as e:
        print(f"   ❌ Error checking safety checks: {e}")
    
    # 4. Check that fallback ContentSwitchingManager is properly instantiated
    print("\n4. 🔍 CHECKING FALLBACK INSTANTIATION:")
    
    try:
        # Look for the instantiation after class definition
        pattern = r'window\.contentSwitchingManager = new window\.ContentSwitchingManager\(\);'
        matches = re.findall(pattern, content)
        
        if len(matches) >= 2:
            print(f"   ✅ ContentSwitchingManager instantiated {len(matches)} times (early + fallback)")
        else:
            print(f"   ❌ ContentSwitchingManager instantiated only {len(matches)} times")
        
        if 'ContentSwitchingManager instance created and initialized' in content:
            print("   ✅ Instantiation confirmation message present")
        else:
            print("   ❌ Instantiation confirmation message missing")
            
    except Exception as e:
        print(f"   ❌ Error checking fallback instantiation: {e}")
    
    # 5. Check that loadPreReportingUIFromDatabase has proper error handling
    print("\n5. 🔍 CHECKING LOAD PRE-REPORTING UI FUNCTION:")
    
    try:
        if 'loadPreReportingUIFromDatabase' in content:
            print("   ✅ loadPreReportingUIFromDatabase function present")
        else:
            print("   ❌ loadPreReportingUIFromDatabase function missing")
        
        # Check for the specific error that was occurring
        if 'switchToPhase is not a function' in content:
            print("   ⚠️ Error message still present in code (should be removed)")
        else:
            print("   ✅ Error message not found in code")
        
        # Check for proper error handling
        if 'catch (error)' in content and 'Error loading pre-reporting UI' in content:
            print("   ✅ Error handling present in loadPreReportingUIFromDatabase")
        else:
            print("   ❌ Error handling missing in loadPreReportingUIFromDatabase")
            
    except Exception as e:
        print(f"   ❌ Error checking loadPreReportingUIFromDatabase: {e}")
    
    # 6. Summary
    print("\n" + "=" * 60)
    print("📋 FIX SUMMARY:")
    print("   ✅ Early ContentSwitchingManager initialization added")
    print("   ✅ Multiple instance creation points for reliability")
    print("   ✅ Safety checks before calling switchToPhase")
    print("   ✅ On-demand instance creation if missing")
    print("   ✅ Function type validation before calling")
    print("   ✅ Fallback direct phase switching if needed")
    
    print("\n🎯 EXPECTED RESULT:")
    print("   • Pre-reporting UI should load without 'switchToPhase is not a function' error")
    print("   • ContentSwitchingManager should be available throughout the application")
    print("   • Phase transitions should work reliably")
    
    print("\n🔧 NEXT STEPS:")
    print("   1. Test the application with a payroll audit process")
    print("   2. Verify that pre-reporting UI appears correctly")
    print("   3. Check console for any remaining ContentSwitchingManager errors")

if __name__ == "__main__":
    test_content_switching_fix()
