// Trigger Real Pre-Reporting UI in Running App
// This script will inject into the actual running Electron app and trigger the beautiful UI

const { BrowserWindow } = require('electron');

async function triggerRealPreReportingUI() {
    try {
        console.log('🎯 TRIGGERING REAL PRE-REPORTING UI IN RUNNING APP...');
        
        // Get the main window
        const windows = BrowserWindow.getAllWindows();
        if (windows.length === 0) {
            console.log('❌ No Electron windows found');
            return;
        }
        
        const mainWindow = windows[0];
        console.log('✅ Found main window, injecting UI trigger...');
        
        // Execute JavaScript in the renderer process to trigger the real UI
        const result = await mainWindow.webContents.executeJavaScript(`
            (async function() {
                try {
                    console.log('🎨 TRIGGERING REAL PRE-REPORTING UI...');
                    
                    // Step 1: Switch to Payroll Audit tab if not already there
                    const payrollTab = document.querySelector('[data-tab="payroll-audit"]');
                    if (payrollTab && !payrollTab.classList.contains('active')) {
                        console.log('📋 Switching to Payroll Audit tab...');
                        payrollTab.click();
                        await new Promise(resolve => setTimeout(resolve, 500));
                    }
                    
                    // Step 2: Load pre-reporting data using the real API
                    console.log('📊 Loading real pre-reporting data...');
                    const preReportingData = await window.api.getLatestPreReportingData();
                    
                    if (!preReportingData || !preReportingData.success) {
                        throw new Error('Failed to load pre-reporting data: ' + (preReportingData?.error || 'Unknown error'));
                    }
                    
                    console.log('✅ Pre-reporting data loaded:', preReportingData.data.length, 'changes');
                    
                    // Step 3: Find the pre-reporting container
                    let container = document.getElementById('pre-reporting-container') || 
                                   document.getElementById('pre-reporting-interactive-ui') ||
                                   document.getElementById('processing-pre-reporting-container');
                    
                    if (!container) {
                        // Create container if it doesn't exist
                        container = document.createElement('div');
                        container.id = 'pre-reporting-container';
                        container.style.cssText = 'margin: 20px; padding: 20px; background: white; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);';
                        
                        // Find a good place to insert it
                        const mainContent = document.getElementById('main-content') || 
                                          document.getElementById('payroll-audit-content') ||
                                          document.querySelector('.tab-content.active') ||
                                          document.body;
                        
                        mainContent.appendChild(container);
                        console.log('📦 Created new pre-reporting container');
                    }
                    
                    // Step 4: Initialize the Interactive Pre-Reporting UI
                    if (!window.InteractivePreReporting) {
                        throw new Error('InteractivePreReporting class not loaded');
                    }
                    
                    console.log('🎨 Initializing beautiful pre-reporting UI...');
                    
                    // Clear container first
                    container.innerHTML = '';
                    
                    // Initialize the beautiful UI
                    window.interactivePreReporting = new window.InteractivePreReporting(container);
                    
                    // Step 5: Render the beautiful UI with real data
                    console.log('✨ Rendering beautiful UI with', preReportingData.data.length, 'real changes...');
                    await window.interactivePreReporting.render(preReportingData.data);
                    
                    // Step 6: Update phase indicators to show pre-reporting is active
                    const preReportingPhase = document.querySelector('[data-phase="pre-reporting"]') ||
                                            document.getElementById('phase-pre-reporting');
                    
                    if (preReportingPhase) {
                        preReportingPhase.classList.add('active', 'completed');
                        const statusElement = preReportingPhase.querySelector('.phase-status');
                        if (statusElement) {
                            statusElement.textContent = 'Ready for Review';
                        }
                        console.log('📊 Updated phase indicator');
                    }
                    
                    // Step 7: Scroll to the beautiful UI
                    container.scrollIntoView({ behavior: 'smooth', block: 'start' });
                    
                    console.log('🎉 BEAUTIFUL PRE-REPORTING UI SUCCESSFULLY TRIGGERED!');
                    
                    return {
                        success: true,
                        message: 'Beautiful pre-reporting UI rendered successfully',
                        changesCount: preReportingData.data.length,
                        sessionId: preReportingData.session_id
                    };
                    
                } catch (error) {
                    console.error('❌ Error triggering real UI:', error);
                    return {
                        success: false,
                        error: error.message,
                        stack: error.stack
                    };
                }
            })();
        `);
        
        console.log('🎯 UI Trigger Result:', result);
        
        if (result.success) {
            console.log('🎉 SUCCESS! Beautiful pre-reporting UI is now active in your app!');
            console.log(`📊 Showing ${result.changesCount} real changes from session ${result.sessionId}`);
        } else {
            console.log('❌ FAILED to trigger UI:', result.error);
            console.log('Stack trace:', result.stack);
        }
        
        return result;
        
    } catch (error) {
        console.error('❌ Error in triggerRealPreReportingUI:', error);
        return { success: false, error: error.message };
    }
}

// Execute the trigger
triggerRealPreReportingUI()
    .then(result => {
        if (result.success) {
            console.log('✅ Real UI trigger completed successfully');
        } else {
            console.log('❌ Real UI trigger failed:', result.error);
        }
        process.exit(result.success ? 0 : 1);
    })
    .catch(error => {
        console.error('❌ Fatal error:', error);
        process.exit(1);
    });
