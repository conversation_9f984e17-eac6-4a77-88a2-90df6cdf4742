#!/usr/bin/env python3
"""
Debug Population Logic
Find out why 751 IN-HOUSE loan items become only 3 records
"""

import os
import sys
import sqlite3
from pathlib import Path

def get_database_path():
    """Get the database path"""
    db_path = Path(__file__).parent / "data" / "templar_payroll_auditor.db"
    if db_path.exists():
        return str(db_path)
    return None

def debug_population_logic():
    """Debug why population is failing"""
    
    print("🔍 DEBUGGING POPULATION LOGIC")
    print("=" * 35)
    
    try:
        db_path = get_database_path()
        if not db_path:
            print("❌ Database file not found")
            return False
        
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 1. Find the session being used for population
        print("1. 📊 FINDING POPULATION SESSION:")
        
        cursor.execute("""
            SELECT session_id, COUNT(*) as tracker_count
            FROM tracker_results
            GROUP BY session_id
            ORDER BY tracker_count DESC, session_id DESC
            LIMIT 1
        """)
        
        result = cursor.fetchone()
        if not result:
            print("   ❌ No tracker_results found")
            return False
        
        population_session = result[0]
        total_tracker_items = result[1]
        
        print(f"   ✅ Population session: {population_session}")
        print(f"   📊 Total tracker items: {total_tracker_items}")
        
        # 2. Check IN-HOUSE loans in this session
        print(f"\n2. 📊 IN-HOUSE LOANS IN POPULATION SESSION:")
        
        cursor.execute("""
            SELECT employee_id, employee_name, item_label, item_value, numeric_value
            FROM tracker_results
            WHERE session_id = ? AND tracker_type = 'IN_HOUSE_LOAN'
            ORDER BY employee_id, item_label
        """, (population_session,))
        
        in_house_data = cursor.fetchall()
        print(f"   📊 IN-HOUSE loan records: {len(in_house_data)}")
        
        if len(in_house_data) > 0:
            print("   📝 Sample records:")
            for i, row in enumerate(in_house_data[:10]):  # Show first 10
                employee_id, employee_name, item_label, item_value, numeric_value = row
                print(f"      {i+1}. {employee_id} - {employee_name}")
                print(f"         Loan: {item_label}")
                print(f"         Value: {item_value} (numeric: {numeric_value})")
        
        # 3. Simulate the population process
        print(f"\n3. 🧪 SIMULATING POPULATION PROCESS:")
        
        successful_inserts = 0
        failed_inserts = 0
        error_details = []
        
        for row in in_house_data:
            try:
                employee_id, employee_name, item_label, item_value, numeric_value = row
                
                # Clean loan type (same logic as population script)
                loan_type = item_label
                if ' - ' in loan_type:
                    loan_type = loan_type.split(' - ')[0].strip()
                
                # Test the insert (but don't actually insert)
                test_values = (
                    employee_id,  # employee_no
                    employee_name,  # employee_name
                    'Department not specified',  # department
                    loan_type,  # loan_type
                    numeric_value or 0,  # loan_amount
                    '06',  # period_month
                    '2025',  # period_year
                    '2025-06',  # period_acquired
                    population_session,  # source_session
                    'Monitoring'  # remarks
                )
                
                # Check if this would be a valid insert
                if employee_id and employee_name and loan_type:
                    successful_inserts += 1
                    if successful_inserts <= 5:  # Show first 5 successful
                        print(f"   ✅ Would insert: {employee_id} - {loan_type} = {numeric_value or 0}")
                else:
                    failed_inserts += 1
                    error_details.append(f"Missing data: {employee_id}, {employee_name}, {loan_type}")
                    
            except Exception as e:
                failed_inserts += 1
                error_details.append(f"Error processing {row}: {e}")
        
        print(f"\n   📊 Simulation results:")
        print(f"      Successful inserts: {successful_inserts}")
        print(f"      Failed inserts: {failed_inserts}")
        
        if error_details:
            print(f"   ❌ Error details (first 5):")
            for error in error_details[:5]:
                print(f"      - {error}")
        
        # 4. Check what's actually in the final table
        print(f"\n4. 📊 CHECKING FINAL TABLE:")
        
        cursor.execute("SELECT COUNT(*) FROM in_house_loans")
        final_count = cursor.fetchone()[0]
        
        cursor.execute("""
            SELECT employee_no, employee_name, loan_type, loan_amount, source_session
            FROM in_house_loans
            ORDER BY id
        """)
        final_records = cursor.fetchall()
        
        print(f"   📊 Final table records: {final_count}")
        
        if final_records:
            print("   📝 Final table contents:")
            for i, record in enumerate(final_records):
                employee_no, employee_name, loan_type, loan_amount, source_session = record
                print(f"      {i+1}. {employee_no} - {loan_type} = {loan_amount} (session: {source_session})")
        
        # 5. Test the actual population method
        print(f"\n5. 🧪 TESTING ACTUAL POPULATION METHOD:")
        
        # Clear the table first
        cursor.execute("DELETE FROM in_house_loans")
        conn.commit()
        
        print("   🧹 Cleared in_house_loans table")
        
        # Run the population manually
        insert_count = 0
        
        for row in in_house_data[:5]:  # Test with first 5 records
            try:
                employee_id, employee_name, item_label, item_value, numeric_value = row
                
                loan_type = item_label
                if ' - ' in loan_type:
                    loan_type = loan_type.split(' - ')[0].strip()
                
                cursor.execute("""
                    INSERT INTO in_house_loans
                    (employee_no, employee_name, department, loan_type, loan_amount,
                     period_month, period_year, period_acquired, source_session, remarks)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    employee_id, employee_name, 'Department not specified', loan_type, numeric_value or 0,
                    '06', '2025', '2025-06', population_session, 'Manual Test'
                ))
                insert_count += 1
                print(f"   ✅ Inserted: {employee_id} - {loan_type}")
                
            except Exception as e:
                print(f"   ❌ Insert failed: {e}")
        
        conn.commit()
        
        # Check final count
        cursor.execute("SELECT COUNT(*) FROM in_house_loans WHERE remarks = 'Manual Test'")
        test_count = cursor.fetchone()[0]
        
        print(f"   📊 Manual test inserts: {test_count}")
        
        # 6. Check for constraints or triggers
        print(f"\n6. 🔍 CHECKING TABLE CONSTRAINTS:")
        
        cursor.execute("PRAGMA table_info(in_house_loans)")
        columns = cursor.fetchall()
        
        print("   📋 Table schema:")
        for col in columns:
            col_name = col[1]
            col_type = col[2]
            not_null = col[3]
            default_val = col[4]
            primary_key = col[5]
            
            constraints = []
            if not_null:
                constraints.append("NOT NULL")
            if primary_key:
                constraints.append("PRIMARY KEY")
            if default_val:
                constraints.append(f"DEFAULT {default_val}")
            
            constraint_str = " " + ", ".join(constraints) if constraints else ""
            print(f"      {col_name}: {col_type}{constraint_str}")
        
        conn.close()
        
        # 7. Summary
        print(f"\n7. 📋 POPULATION DEBUG SUMMARY:")
        print("=" * 30)
        
        if successful_inserts > 3 and final_count <= 3:
            print(f"❌ POPULATION ISSUE CONFIRMED:")
            print(f"   Expected inserts: {successful_inserts}")
            print(f"   Actual inserts: {final_count}")
            print(f"   Missing: {successful_inserts - final_count}")
            
            print(f"\n🔧 LIKELY CAUSES:")
            print(f"   1. Population script not processing all records")
            print(f"   2. Silent failures in insert statements")
            print(f"   3. Transaction rollback issues")
            print(f"   4. Wrong session being used")
            
            return True
        else:
            print(f"✅ Population logic appears correct")
            print(f"⚠️ Issue may be elsewhere")
            return False
        
    except Exception as e:
        print(f"❌ Debug failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = debug_population_logic()
    
    if success:
        print("\n🎯 POPULATION LOGIC DEBUG COMPLETED!")
        print("   Issue identified - see summary above")
    else:
        print("\n⚠️ POPULATION LOGIC DEBUG INCONCLUSIVE!")
        print("   May need further investigation")
